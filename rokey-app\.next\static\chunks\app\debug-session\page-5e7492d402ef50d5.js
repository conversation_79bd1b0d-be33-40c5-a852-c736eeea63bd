(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2303],{92017:(e,t,s)=>{Promise.resolve().then(s.bind(s,92405))},92405:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(95155),n=s(12115),i=s(52643);function a(){let[e,t]=(0,n.useState)(null),[s,a]=(0,n.useState)(!0),[l,o]=(0,n.useState)(null),[d,c]=(0,n.useState)(!1),u=(0,i.u)();(0,n.useEffect)(()=>{let e=async()=>{try{var e,s,r;let{data:{session:n},error:i}=await u.auth.getSession();t({hasSession:!!n,sessionError:null==i?void 0:i.message,userId:null==n||null==(e=n.user)?void 0:e.id,userEmail:null==n||null==(s=n.user)?void 0:s.email,userMetadata:null==n||null==(r=n.user)?void 0:r.user_metadata,accessToken:(null==n?void 0:n.access_token)?"Present":"Missing",refreshToken:(null==n?void 0:n.refresh_token)?"Present":"Missing",expiresAt:null==n?void 0:n.expires_at,timestamp:new Date().toISOString()})}catch(e){t({error:e instanceof Error?e.message:String(e),timestamp:new Date().toISOString()})}finally{a(!1)}};e();let{data:{subscription:s}}=u.auth.onAuthStateChange((t,s)=>{e()});return()=>s.unsubscribe()},[]);let h=async()=>{try{a(!0);let{data:e,error:t}=await u.auth.signInWithPassword({email:"<EMAIL>",password:"test123456"});t?alert("Sign in error: "+t.message):alert("Sign in successful!")}catch(e){alert("Sign in error: "+(e instanceof Error?e.message:String(e)))}finally{a(!1)}},m=async()=>{try{a(!0),await u.auth.signOut(),alert("Signed out successfully!")}catch(e){alert("Sign out error: "+(e instanceof Error?e.message:String(e)))}finally{a(!1)}},g=async()=>{try{var e,t,s;c(!0),o(null);let{data:{session:r}}=await u.auth.getSession();if(!(null==r||null==(e=r.user)?void 0:e.id))return void alert("Please sign in first to test hybrid orchestration");let{data:n}=await u.from("custom_api_configs").select("id").eq("user_id",r.user.id).limit(1);if(!n||0===n.length)return void alert("No API configuration found. Please set up your API keys first.");let i=n[0].id,a=await fetch("/api/hybrid-test",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({prompt:"I need to brainstorm creative ideas for a new web application and then code the backend API for it. Please help me with both the creative ideation and the technical implementation.",configId:i,userId:r.user.id})});if(a.ok)if(null==(t=a.headers.get("content-type"))?void 0:t.includes("text/event-stream")){let e=null==(s=a.body)?void 0:s.getReader(),t=new TextDecoder,r="";if(e)for(;;){let{done:s,value:n}=await e.read();if(s)break;let i=t.decode(n);r+=i,o({type:"streaming",content:r,status:"in_progress"})}o({type:"streaming",content:r,status:"completed"})}else{let e=await a.json();o({type:"json",content:e,status:"completed"})}else{let e=await a.json();o({type:"error",content:e,status:"failed"})}}catch(e){o({type:"error",content:{error:e instanceof Error?e.message:String(e)},status:"failed"})}finally{c(!1)}};return s?(0,r.jsx)("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,r.jsx)("p",{children:"Loading session info..."})]})}):(0,r.jsx)("div",{className:"min-h-screen bg-gray-100 p-8",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Session Debug Page"}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Current Session Info"}),(0,r.jsx)("pre",{className:"bg-gray-100 p-4 rounded text-sm overflow-auto",children:JSON.stringify(e,null,2)})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Test Actions"}),(0,r.jsxs)("div",{className:"space-x-4",children:[(0,r.jsx)("button",{onClick:h,className:"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600",disabled:s,children:"Test Sign In"}),(0,r.jsx)("button",{onClick:m,className:"bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600",disabled:s,children:"Test Sign Out"}),(0,r.jsx)("button",{onClick:()=>window.location.reload(),className:"bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600",children:"Refresh Page"})]})]}),(0,r.jsxs)("div",{className:"bg-gradient-to-r from-orange-50 to-yellow-50 border-2 border-orange-200 rounded-lg shadow-lg p-6 mt-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4 text-orange-800",children:"\uD83D\uDE80 Revolutionary Hybrid CrewAI + AutoGen Orchestration Test"}),(0,r.jsx)("p",{className:"text-orange-700 mb-4",children:"Test the new hybrid orchestration system that combines CrewAI and Microsoft AutoGen for superior multi-role AI coordination with dynamic expert consultation."}),(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsx)("button",{onClick:g,className:"bg-gradient-to-r from-orange-500 to-red-500 text-white px-6 py-3 rounded-lg hover:from-orange-600 hover:to-red-600 font-semibold shadow-lg transform hover:scale-105 transition-all duration-200",disabled:d||s,children:d?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Testing Hybrid Orchestration..."]}):"\uD83C\uDFAF Test Hybrid Orchestration"})}),l&&(0,r.jsxs)("div",{className:"bg-white rounded-lg p-4 border border-orange-200",children:[(0,r.jsxs)("h3",{className:"font-semibold mb-2 text-orange-800",children:["Hybrid Orchestration Result (",l.status,"):"]}),(0,r.jsx)("pre",{className:"bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96",children:"streaming"===l.type?l.content:JSON.stringify(l.content,null,2)})]}),(0,r.jsxs)("div",{className:"mt-4 bg-orange-100 border border-orange-300 rounded-lg p-3",children:[(0,r.jsx)("h4",{className:"font-semibold text-orange-800 mb-2",children:"\uD83C\uDFAF What This Tests:"}),(0,r.jsxs)("ul",{className:"list-disc list-inside text-orange-700 space-y-1 text-sm",children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Multi-Role Detection:"})," Analyzes complex prompts requiring multiple AI specialists"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Dynamic Expert Consultation:"})," Contacts additional API keys when expertise is needed"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"CrewAI + AutoGen Hybrid:"})," Combines sequential and conversational orchestration"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Smart Role Matching:"})," Only uses API keys with relevant assigned roles"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Streaming Responses:"})," Real-time updates during orchestration execution"]})]})]})]}),(0,r.jsxs)("div",{className:"mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,r.jsx)("h3",{className:"font-semibold text-yellow-800 mb-2",children:"Instructions:"}),(0,r.jsxs)("ol",{className:"list-decimal list-inside text-yellow-700 space-y-1",children:[(0,r.jsx)("li",{children:"Check the current session info above"}),(0,r.jsx)("li",{children:'Try the "Test Sign In" button (update the email/password in the code if needed)'}),(0,r.jsx)("li",{children:"Check if session info updates after sign in"}),(0,r.jsx)("li",{children:"Try navigating to /checkout after successful sign in"})]})]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[8888,1459,5738,6308,563,2662,8669,8848,622,9173,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(92017)),_N_E=e.O()}]);
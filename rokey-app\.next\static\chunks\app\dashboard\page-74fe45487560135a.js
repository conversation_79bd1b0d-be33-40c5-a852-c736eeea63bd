(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5105],{23079:(e,a,s)=>{Promise.resolve().then(s.bind(s,66521))},66521:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>p});var t=s(95155),l=s(12115),n=s(35695),i=s(27572),r=s(14615),c=s(5500),d=s(6865),o=s(82771),m=s(58397),u=s(28960),h=s(92975),x=s(55628),g=s(57765),y=s(83298);function p(){var e,a,s;let p=(0,n.useRouter)(),{user:j}=(0,y.R)(),[v,N]=(0,l.useState)(null),[w,f]=(0,l.useState)(!1),[b,k]=(0,l.useState)(!0),[_,A]=(0,l.useState)(null),[C,D]=(0,l.useState)([]),[S,T]=(0,l.useState)([{name:"API Gateway",status:"operational"},{name:"Routing Engine",status:"operational"},{name:"Analytics",status:"degraded"}]);(0,l.useEffect)(()=>{(async()=>{await new Promise(e=>setTimeout(e,50));let e=[L(),R(),F()];await Promise.allSettled(e),k(!1)})();let e=setInterval(R,3e4),a=setInterval(F,6e4);return()=>{clearInterval(e),clearInterval(a)}},[]);let L=(0,l.useCallback)(async()=>{try{b&&f(!0);let e=new Date;e.setDate(e.getDate()-30);let a=await fetch("/api/analytics/summary?startDate=".concat(e.toISOString(),"&groupBy=day"));if(!a.ok)throw Error("Failed to fetch analytics data");let s=await a.json();N(s)}catch(e){A(e.message)}finally{b&&f(!1)}},[b]),E=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:6}).format(e),I=e=>new Intl.NumberFormat("en-US").format(e),R=async()=>{try{let e=await fetch("/api/activity?limit=10");if(!e.ok)throw Error("Failed to fetch recent activity");let a=(await e.json()).activities.map(e=>({id:e.id,action:e.action,model:e.model,time:e.time,status:e.status,details:e.details}));D(a)}catch(e){D([{id:"1",action:"System initialized",model:"RoKey",time:"Just now",status:"info"}])}},F=async()=>{try{let e=await fetch("/api/system-status");if(!e.ok)throw Error("Failed to fetch system status");let a=(await e.json()).checks.map(e=>({name:e.name,status:e.status,lastChecked:new Date(e.lastChecked).toLocaleTimeString()}));T(a)}catch(e){T([{name:"API Gateway",status:"down",lastChecked:new Date().toLocaleTimeString()},{name:"Routing Engine",status:"down",lastChecked:new Date().toLocaleTimeString()},{name:"Analytics",status:"down",lastChecked:new Date().toLocaleTimeString()}])}},P=(null==j||null==(e=j.user_metadata)?void 0:e.first_name)||(null==j||null==(s=j.user_metadata)||null==(a=s.full_name)?void 0:a.split(" ")[0])||"there",q=v?[{name:"Total Requests",value:I(v.summary.total_requests),change:"Last 30 days",changeType:"neutral",icon:c.A},{name:"Total Cost",value:E(v.summary.total_cost),change:"".concat(E(v.summary.average_cost_per_request)," avg/request"),changeType:"neutral",icon:u.A},{name:"Success Rate",value:"".concat(v.summary.success_rate.toFixed(1),"%"),change:"".concat(I(v.summary.successful_requests)," successful"),changeType:v.summary.success_rate>=95?"positive":"negative",icon:d.A},{name:"Total Tokens",value:I(v.summary.total_tokens),change:"".concat(I(v.summary.total_input_tokens)," in, ").concat(I(v.summary.total_output_tokens)," out"),changeType:"neutral",icon:m.A}]:[];return w&&b?(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"animate-pulse",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3 mb-4"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[void 0,void 0,void 0,void 0].map((e,a)=>(0,t.jsxs)("div",{className:"card p-6 animate-pulse",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-4"}),(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-3/4 mb-2"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/3"})]},a))})]}):_?(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"animate-slide-in",children:[(0,t.jsxs)("h1",{className:"text-4xl font-bold text-gray-900 mb-2",children:["Welcome ",P,"! \uD83D\uDC4B"]}),(0,t.jsx)("p",{className:"text-gray-600 text-lg",children:"Here's what's happening with your LLM infrastructure today."})]}),(0,t.jsxs)("div",{className:"card p-6 text-center",children:[(0,t.jsxs)("p",{className:"text-red-600 mb-4",children:["Error loading analytics data: ",_]}),(0,t.jsx)("button",{onClick:L,className:"btn-primary",children:"Retry"})]})]}):(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"animate-slide-in",children:[(0,t.jsxs)("h1",{className:"text-4xl font-bold text-gray-900 mb-2",children:["Welcome ",P,"! \uD83D\uDC4B"]}),(0,t.jsx)("p",{className:"text-gray-600 text-lg",children:"Here's what's happening with your LLM infrastructure today."})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 animate-slide-in",children:q.map((e,a)=>(0,t.jsx)("div",{className:"card p-6 hover:shadow-lg transition-all duration-200",style:{animationDelay:"".concat(100*a,"ms")},children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.name}),(0,t.jsx)("p",{className:"text-3xl font-bold text-gray-900 mt-2",children:e.value}),(0,t.jsxs)("p",{className:"text-sm mt-2 flex items-center ".concat("positive"===e.changeType?"text-green-600":"negative"===e.changeType?"text-red-600":"text-gray-500"),children:["neutral"!==e.changeType&&(0,t.jsx)(i.A,{className:"h-4 w-4 mr-1 ".concat("negative"===e.changeType?"rotate-180":"")}),e.change]})]}),(0,t.jsx)("div",{className:"p-3 rounded-lg bg-orange-50",children:(0,t.jsx)(e.icon,{className:"h-6 w-6 text-orange-600"})})]})},e.name))}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"lg:col-span-1 space-y-6",children:[(0,t.jsxs)("div",{className:"card p-6 animate-slide-in",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Quick Actions"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("button",{onClick:()=>{p.push("/my-models")},className:"btn-primary w-full justify-center hover:scale-105 transition-transform duration-200",children:[(0,t.jsx)(g.A,{className:"h-5 w-5 mr-3"}),"Add New Model"]}),(0,t.jsxs)("button",{onClick:()=>{p.push("/playground")},className:"btn-secondary w-full justify-center hover:scale-105 transition-transform duration-200",children:[(0,t.jsx)(r.A,{className:"h-5 w-5 mr-3"}),"Test in Playground"]}),(0,t.jsxs)("button",{onClick:()=>{p.push("/logs")},className:"btn-outline w-full justify-center hover:scale-105 transition-transform duration-200",children:[(0,t.jsx)(h.A,{className:"h-5 w-5 mr-3"}),"View Logs"]})]})]}),(0,t.jsxs)("div",{className:"card p-6 animate-slide-in",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:"System Status"}),(0,t.jsx)("div",{className:"space-y-4",children:S.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full mr-3 ".concat("operational"===e.status?"bg-green-500":"degraded"===e.status?"bg-yellow-500":"bg-red-500")}),(0,t.jsx)("span",{className:"text-gray-700",children:e.name})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("span",{className:"text-sm font-medium ".concat("operational"===e.status?"text-green-600":"degraded"===e.status?"text-yellow-600":"text-red-600"),children:"operational"===e.status?"Operational":"degraded"===e.status?"Degraded":"Down"}),e.lastChecked&&(0,t.jsx)("p",{className:"text-xs text-gray-500",children:e.lastChecked})]})]},e.name))})]})]}),(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsxs)("div",{className:"card p-6 animate-slide-in",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Recent Activity"}),(0,t.jsx)("button",{onClick:R,className:"text-orange-600 hover:text-orange-700 text-sm font-medium",children:"Refresh"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[0===C.length?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(o.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-500",children:"No recent activity"}),(0,t.jsx)("p",{className:"text-gray-400 text-sm",children:"Activity will appear here as you use the API"})]}):C.slice(-4).map(e=>(0,t.jsxs)("div",{className:"flex items-start p-4 rounded-lg bg-gray-50 hover:bg-gray-100 transition-colors duration-200 group overflow-hidden",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full mr-4 ".concat("success"===e.status?"bg-green-500":"warning"===e.status?"bg-yellow-500":"error"===e.status?"bg-red-500":"bg-blue-500")}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("p",{className:"text-gray-900 font-medium break-words",children:e.action}),(0,t.jsxs)("p",{className:"text-gray-600 text-sm break-words",children:[e.model," • ",e.time]}),e.details&&(0,t.jsx)("p",{className:"text-gray-500 text-xs mt-1 line-clamp-2 leading-relaxed",title:e.details,children:e.details})]}),(0,t.jsx)("div",{className:"text-gray-500 group-hover:text-gray-700",children:"error"===e.status?(0,t.jsx)(x.A,{className:"h-5 w-5 text-red-500"}):(0,t.jsx)(m.A,{className:"h-5 w-5"})})]},e.id)),C.length>4&&(0,t.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,t.jsxs)("button",{onClick:()=>{window.location.href="/logs"},className:"text-sm text-orange-600 hover:text-orange-700 font-medium flex items-center justify-center w-full py-2 hover:bg-orange-50 rounded-lg transition-colors",children:["View All Activity (",C.length,")",(0,t.jsx)("svg",{className:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})})]})]})})]})]})}}},e=>{var a=a=>e(e.s=a);e.O(0,[8888,1459,5738,6308,563,2662,8669,8848,622,9173,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>a(23079)),_N_E=e.O()}]);
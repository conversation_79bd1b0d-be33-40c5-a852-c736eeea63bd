(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1524],{23405:(e,t,s)=>{"use strict";s.d(t,{f:()=>a.A});var a=s(74500)},43760:(e,t,s)=>{Promise.resolve().then(s.bind(s,62924))},62924:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var a=s(95155),l=s(12115),r=s(26784),n=s(5279),i=s(15713);let c=l.forwardRef(function(e,t){let{title:s,titleId:a,...r}=e;return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},r),s?l.createElement("title",{id:a},s):null,l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 7.5 7.5 3m0 0L12 7.5M7.5 3v13.5m13.5 0L16.5 21m0 0L12 16.5m4.5 4.5V7.5"}))}),o=l.forwardRef(function(e,t){let{title:s,titleId:a,...r}=e;return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},r),s?l.createElement("title",{id:a},s):null,l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"}))});var d=s(82771);let m=l.forwardRef(function(e,t){let{title:s,titleId:a,...r}=e;return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},r),s?l.createElement("title",{id:a},s):null,l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m5.231 13.481L15 17.25m-4.5-15H5.625c-.621 0-1.125.504-1.125 1.125v16.5c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Zm3.75 11.625a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"}))});var x=s(92975),u=s(10184),h=s(55233),g=s(71848),p=s(74338),f=s(38456);let j=[{label:"Timestamp",field:"request_timestamp",defaultSortOrder:"desc"},{label:"API Model",field:"custom_api_config_id"},{label:"Role Used",field:"role_used"},{label:"Provider",field:"llm_provider_name"},{label:"LLM Model",field:"llm_model_name"},{label:"Status",field:"status_code"},{label:"Latency (LLM)",field:"llm_provider_latency_ms"},{label:"Latency (RoKey)",field:"processing_duration_ms"},{label:"Input Tokens",field:"input_tokens"},{label:"Output Tokens",field:"output_tokens"}];function y(){let[e,t]=(0,l.useState)([]),[s,y]=(0,l.useState)(null),[v,N]=(0,l.useState)(!0),[b,w]=(0,l.useState)(!0),[_,k]=(0,l.useState)(null),[A,S]=(0,l.useState)([]),C={startDate:"",endDate:"",customApiConfigId:"all",status:"all"},[L,D]=(0,l.useState)(C),[P,E]=(0,l.useState)(!1),[M,I]=(0,l.useState)({}),[O,Z]=(0,l.useState)(!1),[H,B]=(0,l.useState)(null),[F,R]=(0,l.useState)({sortBy:"request_timestamp",sortOrder:"desc"}),q=async()=>{w(!0);try{let e=await fetch("/api/custom-configs");if(!e.ok)throw Error("Failed to fetch API model configurations");let t=await e.json();S(t);let s={};t.forEach(e=>{s[e.id]=e.name}),I(s)}catch(e){k("Error fetching configurations: ".concat(e.message))}finally{w(!1)}},V=(0,l.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,s=arguments.length>1?arguments[1]:void 0,a=arguments.length>2?arguments[2]:void 0;N(!0),k(null);try{let l={page:e.toString(),pageSize:"10",sortBy:a.sortBy,sortOrder:a.sortOrder};s.startDate&&(l.startDate=new Date(s.startDate).toISOString()),s.endDate&&(l.endDate=new Date(s.endDate).toISOString()),"all"!==s.customApiConfigId&&(l.customApiConfigId=s.customApiConfigId),"all"!==s.status&&(l.status=s.status);let r=await fetch("/api/logs?".concat(new URLSearchParams(l).toString()));if(!r.ok){let e=await r.json();throw Error(e.error||e.details||"Failed to fetch logs")}let n=await r.json();t(n.logs||[]),y(n.pagination||null)}catch(e){k(e.message),t([]),y(null)}finally{N(!1)}},[]);(0,l.useEffect)(()=>{let e=setTimeout(()=>{q(),V(1,L,F)},100);return()=>clearTimeout(e)},[V,L,F]);let T=e=>{D(t=>({...t,[e.target.name]:e.target.value}))},z=e=>{e>0&&(!s||e<=s.totalPages)&&V(e,L,F)},U=e=>{let t=F.sortBy===e&&"asc"===F.sortOrder?"desc":"asc",s={sortBy:e,sortOrder:t};R(s),V(1,L,s)},W=e=>{B(e),Z(!0)},K=e=>null===e?"bg-gray-600 text-gray-100":e>=200&&e<300?"bg-green-600 text-green-100":e>=400?"bg-red-600 text-red-100":"bg-yellow-500 text-yellow-100",G=e=>{let{column:t}=e,s=F.sortBy===t.field;return(0,a.jsx)("th",{scope:"col",className:"px-6 py-4 text-left text-xs font-medium text-gray-600 uppercase tracking-wider",children:(0,a.jsxs)("button",{onClick:()=>U(t.field),className:"flex items-center space-x-2 hover:text-gray-900 transition-colors duration-200 group",children:[(0,a.jsx)("span",{children:t.label}),s?"asc"===F.sortOrder?(0,a.jsx)(i.A,{className:"h-4 w-4 text-orange-500"}):(0,a.jsx)(r.A,{className:"h-4 w-4 text-orange-500"}):(0,a.jsx)(c,{className:"h-4 w-4 text-gray-400 group-hover:text-gray-600"})]})})};return(0,a.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-900",children:"\uD83D\uDCCA Request Logs"}),(0,a.jsx)("p",{className:"text-gray-600 mt-2",children:"Monitor and analyze your API request history"})]}),(0,a.jsxs)("button",{onClick:()=>E(!P),className:P?"btn-primary":"btn-secondary",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2"}),P?"Hide Filters":"Show Filters"]})]}),_&&(0,a.jsx)("div",{className:"card border-red-200 bg-red-50 p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),(0,a.jsx)("p",{className:"text-red-800",children:_})]})}),P&&(0,a.jsxs)("div",{className:"card p-6 animate-scale-in",children:[(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900",children:"Filter Logs"}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Narrow down your search results"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-500",children:[(0,a.jsx)(d.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Real-time updates"})]})]})}),(0,a.jsxs)("form",{onSubmit:e=>{null==e||e.preventDefault(),V(1,L,F)},className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,a.jsx)(o,{className:"h-4 w-4 inline mr-1"}),"Start Date"]}),(0,a.jsx)("input",{type:"date",name:"startDate",value:L.startDate,onChange:T,className:"form-input"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,a.jsx)(o,{className:"h-4 w-4 inline mr-1"}),"End Date"]}),(0,a.jsx)("input",{type:"date",name:"endDate",value:L.endDate,onChange:T,className:"form-input"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"API Model"}),(0,a.jsxs)("select",{name:"customApiConfigId",value:L.customApiConfigId,onChange:T,disabled:b,className:"form-select",children:[(0,a.jsx)("option",{value:"all",children:"All Models"}),A.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),(0,a.jsxs)("select",{name:"status",value:L.status,onChange:T,className:"form-select",children:[(0,a.jsx)("option",{value:"all",children:"All Statuses"}),(0,a.jsx)("option",{value:"success",children:"Success"}),(0,a.jsx)("option",{value:"error",children:"Error"})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,a.jsxs)("button",{type:"submit",className:"btn-primary flex-1",children:[(0,a.jsx)(m,{className:"h-4 w-4 mr-2"}),"Apply Filters"]}),(0,a.jsxs)("button",{type:"button",onClick:()=>{D(C);let e={sortBy:"request_timestamp",sortOrder:"desc"};R(e),V(1,C,e)},className:"btn-secondary flex-1",children:[(0,a.jsx)(n.A,{className:"h-4 w-4 mr-2"}),"Reset Filters"]})]})]})]}),v&&(0,a.jsx)(p.F6,{rows:8,columns:11}),!v&&!e.length&&!_&&(0,a.jsx)("div",{className:"card text-center py-12",children:(0,a.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-orange-50 rounded-2xl flex items-center justify-center mx-auto mb-6 border border-orange-100",children:(0,a.jsx)(x.A,{className:"h-8 w-8 text-orange-600"})}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"No Logs Found"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"No request logs match your criteria. Once you make requests to the unified API, they will appear here."})]})}),!v&&e.length>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"card overflow-hidden",children:(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full text-sm",children:[(0,a.jsx)("thead",{className:"bg-gray-50 border-b border-gray-200",children:(0,a.jsxs)("tr",{children:[j.map(e=>(0,a.jsx)(G,{column:e},e.field)),(0,a.jsx)("th",{scope:"col",className:"px-6 py-4 text-left text-xs font-medium text-gray-600 uppercase tracking-wider",children:"Multimodal"}),(0,a.jsx)("th",{scope:"col",className:"px-6 py-4 text-left text-xs font-medium text-gray-600 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"divide-y divide-gray-200 bg-white",children:e.map((e,t)=>(0,a.jsxs)("tr",{className:"hover:bg-gray-50 transition-colors duration-200 animate-slide-in",style:{animationDelay:"".concat(50*t,"ms")},children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-gray-900",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 text-gray-500"}),(0,a.jsx)("span",{children:new Date(e.request_timestamp).toLocaleString()})]})}),(0,a.jsx)("td",{className:"px-6 py-4 text-gray-900",children:(0,a.jsx)("div",{className:"font-medium",children:e.custom_api_config_id?M[e.custom_api_config_id]||e.custom_api_config_id.substring(0,8)+"...":"N/A"})}),(0,a.jsx)("td",{className:"px-6 py-4 text-gray-900",children:(()=>{let t=(0,f.gI)(e.role_used);return(0,a.jsx)("span",{className:(0,f.Rf)(t.type),children:t.text})})()}),(0,a.jsx)("td",{className:"px-6 py-4 text-gray-900",children:(0,a.jsx)("span",{className:"font-medium",children:(0,f.aU)(e.llm_provider_name)})}),(0,a.jsx)("td",{className:"px-6 py-4 text-gray-900 truncate max-w-xs",title:(0,f.Il)(e.llm_model_name),children:(0,a.jsx)("span",{className:"font-medium",children:(0,f.Il)(e.llm_model_name)})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("span",{className:"px-3 py-1 rounded-full text-xs font-medium ".concat(K(e.status_code)),children:e.status_code||"N/A"})}),(0,a.jsx)("td",{className:"px-6 py-4 text-right text-gray-900",children:null!==e.llm_provider_latency_ms?"".concat(e.llm_provider_latency_ms," ms"):"-"}),(0,a.jsx)("td",{className:"px-6 py-4 text-right text-gray-900",children:null!==e.processing_duration_ms?"".concat(e.processing_duration_ms," ms"):"-"}),(0,a.jsx)("td",{className:"px-6 py-4 text-right text-gray-900",children:null!==e.input_tokens?e.input_tokens.toLocaleString():"-"}),(0,a.jsx)("td",{className:"px-6 py-4 text-right text-gray-900",children:null!==e.output_tokens?e.output_tokens.toLocaleString():"-"}),(0,a.jsx)("td",{className:"px-6 py-4 text-center",children:e.is_multimodal?(0,a.jsx)("span",{className:"w-2 h-2 bg-green-500 rounded-full inline-block"}):(0,a.jsx)("span",{className:"w-2 h-2 bg-gray-400 rounded-full inline-block"})}),(0,a.jsx)("td",{className:"px-6 py-4",children:(0,a.jsx)("button",{onClick:()=>W(e),className:"p-2 text-gray-400 hover:text-orange-600 hover:bg-orange-50 rounded-lg transition-all duration-200",children:(0,a.jsx)(u.A,{className:"h-4 w-4"})})})]},e.id))})]})})}),s&&s.totalPages>1&&(0,a.jsx)("div",{className:"card p-4",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Showing ",(0,a.jsx)("span",{className:"font-medium text-gray-900",children:(s.currentPage-1)*s.pageSize+1}),e.length>0?" - ".concat(Math.min(s.currentPage*s.pageSize,s.totalCount)):""," ","of ",(0,a.jsx)("span",{className:"font-medium text-gray-900",children:s.totalCount})," logs"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>z(s.currentPage-1),disabled:s.currentPage<=1||v,className:"btn-secondary text-sm px-3 py-1",children:"Previous"}),(0,a.jsxs)("span",{className:"px-3 py-1 text-sm text-gray-600",children:["Page ",s.currentPage," of ",s.totalPages]}),(0,a.jsx)("button",{onClick:()=>z(s.currentPage+1),disabled:s.currentPage>=s.totalPages||v,className:"btn-secondary text-sm px-3 py-1",children:"Next"})]})]})})]}),O&&H&&(0,a.jsx)(g.A,{log:H,onClose:()=>{Z(!1),B(null)},apiConfigNameMap:M})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[5738,274,6308,563,2662,8669,8848,622,9173,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(43760)),_N_E=e.O()}]);
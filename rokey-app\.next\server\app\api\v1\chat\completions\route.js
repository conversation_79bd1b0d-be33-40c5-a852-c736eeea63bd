(()=>{var e={};e.id=8246,e.ids=[1489,2842,8246],e.modules={507:(e,t,r)=>{"use strict";r.d(t,{Dc:()=>i,p2:()=>a});let a=[{id:"general_chat",name:"<PERSON> Chat",description:"Handles general conversation, Q&A, and tasks not covered by other specific roles. Often the default fallback.",backstory:"You are a knowledgeable and helpful AI assistant with broad expertise across multiple domains. You have years of experience helping people with diverse questions and tasks, from simple queries to complex problem-solving. You are patient, thorough, and always strive to provide accurate and useful information while maintaining a friendly and professional demeanor."},{id:"logic_reasoning",name:"Logic & Reasoning",description:"For tasks requiring logical deduction, problem-solving, mathematical reasoning, and complex analytical thinking.",backstory:"You are a brilliant analytical thinker with a PhD in Logic and Philosophy, combined with extensive experience in mathematical reasoning and problem-solving. You have spent years working as a consultant for complex analytical challenges, helping organizations break down intricate problems into manageable components. Your approach is methodical, precise, and you excel at identifying patterns and logical connections that others might miss."},{id:"writing",name:"Writing & Content Creation",description:"For all writing tasks, including articles, blog posts, marketing copy, creative content, essays, and more.",backstory:"You are an accomplished professional writer and content strategist with over 15 years of experience in journalism, marketing, and creative writing. You have worked with major publications, Fortune 500 companies, and bestselling authors. Your expertise spans multiple writing styles, from technical documentation to compelling storytelling. You understand audience psychology and know how to craft messages that resonate, engage, and drive action."},{id:"coding_frontend",name:"Coding - Frontend",description:"For generating and assisting with HTML, CSS, JavaScript, and frontend frameworks (React, Vue, Angular, etc.).",backstory:"You are a senior frontend engineer with 10+ years of experience building beautiful, responsive, and user-friendly web applications. You have worked at leading tech companies and startups, mastering modern frameworks like React, Vue, and Angular. You are passionate about user experience, accessibility, and performance optimization. You stay current with the latest web technologies and best practices, and you have a keen eye for design and usability."},{id:"coding_backend",name:"Coding - Backend",description:"For generating and assisting with server-side logic, APIs, databases, and backend frameworks (Node.js, Python, Java, etc.).",backstory:"You are a seasoned backend engineer and system architect with 12+ years of experience building scalable, robust server-side applications. You have expertise in multiple programming languages including Python, Node.js, Java, and Go. You have designed and implemented high-performance APIs, microservices, and distributed systems for companies ranging from startups to enterprise-level organizations. You are well-versed in database design, cloud architecture, and DevOps practices."},{id:"research_synthesis",name:"Research & Synthesis",description:"For information retrieval from various sources, data analysis, and synthesizing findings into reports or summaries.",backstory:"You are a research analyst and information scientist with a Master's degree in Information Science and 8+ years of experience in academic and corporate research. You have worked with think tanks, consulting firms, and research institutions, specializing in gathering, analyzing, and synthesizing complex information from diverse sources. You are skilled at identifying credible sources, extracting key insights, and presenting findings in clear, actionable formats."},{id:"summarization_briefing",name:"Summarization & Briefing",description:"For condensing long texts, documents, or conversations into concise summaries or executive briefings.",backstory:"You are an executive communications specialist with 10+ years of experience working with C-level executives and government officials. You have mastered the art of distilling complex information into clear, concise, and actionable briefings. Your background includes work in consulting, journalism, and corporate communications. You understand how busy decision-makers consume information and can quickly identify the most critical points that require attention."},{id:"translation_localization",name:"Translation & Localization",description:"For translating text between languages and adapting content culturally for different locales.",backstory:"You are a professional translator and localization expert with 12+ years of experience working with international organizations and global brands. You are fluent in multiple languages and have deep cultural knowledge of various regions. You have worked on everything from legal documents to marketing campaigns, ensuring that content not only translates accurately but also resonates culturally with target audiences. You understand the nuances of language, cultural context, and regional preferences."},{id:"data_extraction_structuring",name:"Data Extraction & Structuring",description:"For identifying and extracting specific pieces of information from unstructured/semi-structured text and organizing it.",backstory:"You are a data analyst and information architect with expertise in natural language processing and data mining. You have 8+ years of experience working with large datasets, unstructured documents, and complex information systems. You have helped organizations extract valuable insights from messy data sources, create structured databases from unorganized information, and develop automated data processing pipelines. You are meticulous, detail-oriented, and skilled at pattern recognition."},{id:"brainstorming_ideation",name:"Brainstorming & Ideation",description:"For generating new ideas, exploring concepts, and creative problem-solving sessions.",backstory:"You are a creative innovation consultant and design thinking expert with 10+ years of experience helping organizations breakthrough creative blocks and generate breakthrough ideas. You have worked with startups, Fortune 500 companies, and creative agencies, facilitating ideation sessions and innovation workshops. You are skilled at lateral thinking, connecting disparate concepts, and creating environments where creativity flourishes. Your approach combines structured methodologies with free-flowing creative exploration."},{id:"education_tutoring",name:"Education & Tutoring",description:"For explaining concepts, answering educational questions, and providing tutoring assistance across various subjects.",backstory:"You are an experienced educator and academic tutor with a Master's in Education and 15+ years of teaching experience across multiple subjects and age groups. You have worked in schools, universities, and private tutoring, helping thousands of students understand complex concepts and achieve their learning goals. You are patient, encouraging, and skilled at adapting your teaching style to different learning preferences. You believe in making learning engaging, accessible, and meaningful."},{id:"image_generation",name:"Image Generation",description:"For creating images from textual descriptions. Assign to keys linked to image generation models.",backstory:"You are a digital artist and creative director with 8+ years of experience in visual design, digital art, and creative technology. You have worked with advertising agencies, game studios, and tech companies, creating compelling visual content for various media. You understand composition, color theory, visual storytelling, and the technical aspects of digital image creation. You are skilled at translating abstract concepts and ideas into powerful visual representations."},{id:"audio_transcription",name:"Audio Transcription",description:"For converting speech from audio files into written text. Assign to keys linked to transcription models.",backstory:"You are a professional transcriptionist and audio processing specialist with 10+ years of experience in media, legal, and academic transcription. You have worked with podcasters, journalists, legal firms, and researchers, converting audio content into accurate written text. You understand various accents, technical terminology, and industry-specific language. You are detail-oriented, accurate, and skilled at capturing not just words but also the context and nuance of spoken communication."}],i=e=>a.find(t=>t.id===e)},1025:(e,t,r)=>{"use strict";r.d(t,{C:()=>n});var a=r(62633),i=r(60799),s=r(99366);class n extends i.m{static lc_name(){return"ImagePromptTemplate"}constructor(e){if(super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","prompts","image"]}),Object.defineProperty(this,"template",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"templateFormat",{enumerable:!0,configurable:!0,writable:!0,value:"f-string"}),Object.defineProperty(this,"validateTemplate",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"additionalContentFields",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.template=e.template,this.templateFormat=e.templateFormat??this.templateFormat,this.validateTemplate=e.validateTemplate??this.validateTemplate,this.additionalContentFields=e.additionalContentFields,this.validateTemplate){let e=this.inputVariables;this.partialVariables&&(e=e.concat(Object.keys(this.partialVariables))),(0,s.Ns)([{type:"image_url",image_url:this.template}],this.templateFormat,e)}}_getPromptType(){return"prompt"}async partial(e){let t=this.inputVariables.filter(t=>!(t in e)),r={...this.partialVariables??{},...e};return new n({...this,inputVariables:t,partialVariables:r})}async format(e){let t={};for(let[r,a]of Object.entries(this.template))"string"==typeof a?t[r]=(0,s.Xm)(a,this.templateFormat,e):t[r]=a;let r=e.url||t.url,a=e.detail||t.detail;if(!r)throw Error("Must provide either an image URL.");if("string"!=typeof r)throw Error("url must be a string.");let i={url:r};return a&&(i.detail=a),i}async formatPromptValue(e){let t=await this.format(e);return new a.ImagePromptValue(t)}}},2507:(e,t,r)=>{"use strict";r.d(t,{Q:()=>n,createSupabaseServerClientOnRequest:()=>s});var a=r(34386),i=r(44999);async function s(){let e=await (0,i.UL)();return(0,a.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,a){try{e.set({name:t,value:r,...a})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}function n(e){return(0,a.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,r){},remove(e,t){}}})}},2842:(e,t,r)=>{"use strict";r.d(t,{trainingDataCache:()=>i});class a{set(e,t,r){this.cache.set(e,{data:t,timestamp:Date.now(),jobId:r})}get(e){let t=this.cache.get(e);return t?Date.now()-t.timestamp>this.TTL?(this.cache.delete(e),null):t:null}invalidate(e){return this.cache.delete(e)}clear(){this.cache.clear()}cleanup(){let e=Date.now();for(let[t,r]of this.cache.entries())e-r.timestamp>this.TTL&&this.cache.delete(t)}getStats(){return{size:this.cache.size,keys:Array.from(this.cache.keys())}}constructor(){this.cache=new Map,this.TTL=3e5}}let i=new a;setInterval(()=>{i.cleanup()},6e5)},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19784:e=>{"use strict";function t(e){if(this._capacity=a(e),this._length=0,this._front=0,r(e)){for(var t=e.length,i=0;i<t;++i)this[i]=e[i];this._length=t}}t.prototype.toArray=function(){for(var e=this._length,t=Array(e),r=this._front,a=this._capacity,i=0;i<e;++i)t[i]=this[r+i&a-1];return t},t.prototype.push=function(e){var t=arguments.length,r=this._length;if(t>1){var a=this._capacity;if(r+t>a){for(var i=0;i<t;++i){this._checkCapacity(r+1);var s=this._front+r&this._capacity-1;this[s]=arguments[i],r++,this._length=r}return r}for(var s=this._front,i=0;i<t;++i)this[s+r&a-1]=arguments[i],s++;return this._length=r+t,r+t}if(0===t)return r;this._checkCapacity(r+1);var i=this._front+r&this._capacity-1;return this[i]=e,this._length=r+1,r+1},t.prototype.pop=function(){var e=this._length;if(0!==e){var t=this._front+e-1&this._capacity-1,r=this[t];return this[t]=void 0,this._length=e-1,r}},t.prototype.shift=function(){var e=this._length;if(0!==e){var t=this._front,r=this[t];return this[t]=void 0,this._front=t+1&this._capacity-1,this._length=e-1,r}},t.prototype.unshift=function(e){var t=this._length,r=arguments.length;if(r>1){var a=this._capacity;if(t+r>a){for(var i=r-1;i>=0;i--){this._checkCapacity(t+1);var a=this._capacity,s=(this._front-1&a-1^a)-a;this[s]=arguments[i],t++,this._length=t,this._front=s}return t}for(var n=this._front,i=r-1;i>=0;i--){var s=(n-1&a-1^a)-a;this[s]=arguments[i],n=s}return this._front=n,this._length=t+r,t+r}if(0===r)return t;this._checkCapacity(t+1);var a=this._capacity,i=(this._front-1&a-1^a)-a;return this[i]=e,this._length=t+1,this._front=i,t+1},t.prototype.peekBack=function(){var e=this._length;if(0!==e)return this[this._front+e-1&this._capacity-1]},t.prototype.peekFront=function(){if(0!==this._length)return this[this._front]},t.prototype.get=function(e){var t=e;if(t===(0|t)){var r=this._length;if(t<0&&(t+=r),!(t<0)&&!(t>=r))return this[this._front+t&this._capacity-1]}},t.prototype.isEmpty=function(){return 0===this._length},t.prototype.clear=function(){for(var e=this._length,t=this._front,r=this._capacity,a=0;a<e;++a)this[t+a&r-1]=void 0;this._length=0,this._front=0},t.prototype.toString=function(){return this.toArray().toString()},t.prototype.valueOf=t.prototype.toString,t.prototype.removeFront=t.prototype.shift,t.prototype.removeBack=t.prototype.pop,t.prototype.insertFront=t.prototype.unshift,t.prototype.insertBack=t.prototype.push,t.prototype.enqueue=t.prototype.push,t.prototype.dequeue=t.prototype.shift,t.prototype.toJSON=t.prototype.toArray,Object.defineProperty(t.prototype,"length",{get:function(){return this._length},set:function(){throw RangeError("")}}),t.prototype._checkCapacity=function(e){this._capacity<e&&this._resizeTo(a(1.5*this._capacity+16))},t.prototype._resizeTo=function(e){var t=this._capacity;this._capacity=e;var r=this._front,a=this._length;r+a>t&&function(e,t,r,a,i){for(var s=0;s<i;++s)r[s+a]=e[s+0],e[s+t]=void 0}(this,0,this,t,r+a&t-1)};var r=Array.isArray;function a(e){var t;if("number"!=typeof e)if(!r(e))return 16;else e=e.length;return t=(Math.min(Math.max(16,e),0x40000000)>>>0)-1,t|=t>>1,t|=t>>2,t|=t>>4,t|=t>>8,(t|=t>>16)+1}e.exports=t},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33892:(e,t,r)=>{"use strict";r.d(t,{PromptTemplate:()=>s});var a=r(58111),i=r(99366);class s extends a.L{static lc_name(){return"PromptTemplate"}constructor(e){if(super(e),Object.defineProperty(this,"template",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"templateFormat",{enumerable:!0,configurable:!0,writable:!0,value:"f-string"}),Object.defineProperty(this,"validateTemplate",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"additionalContentFields",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),"mustache"===e.templateFormat&&void 0===e.validateTemplate&&(this.validateTemplate=!1),Object.assign(this,e),this.validateTemplate){if("mustache"===this.templateFormat)throw Error("Mustache templates cannot be validated.");let e=this.inputVariables;this.partialVariables&&(e=e.concat(Object.keys(this.partialVariables))),(0,i.Ns)(this.template,this.templateFormat,e)}}_getPromptType(){return"prompt"}async format(e){let t=await this.mergePartialAndUserVariables(e);return(0,i.Xm)(this.template,this.templateFormat,t)}static fromExamples(e,t,r,a="\n\n",i=""){return new s({inputVariables:r,template:[i,...e,t].join(a)})}static fromTemplate(e,t){let{templateFormat:r="f-string",...a}=t??{},n=new Set;return(0,i.QC)(e,r).forEach(e=>{"variable"===e.type&&n.add(e.name)}),new s({inputVariables:[...n],templateFormat:r,template:e,...a})}async partial(e){let t=this.inputVariables.filter(t=>!(t in e)),r={...this.partialVariables??{},...e};return new s({...this,inputVariables:t,partialVariables:r})}serialize(){if(void 0!==this.outputParser)throw Error("Cannot serialize a prompt template with an output parser");return{_type:this._getPromptType(),input_variables:this.inputVariables,template:this.template,template_format:this.templateFormat}}static async deserialize(e){if(!e.template)throw Error("Prompt template must have a template");return new s({inputVariables:e.input_variables,template:e.template,templateFormat:e.template_format})}}},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},46625:(e,t,r)=>{"use strict";r.d(t,{FewShotPromptTemplate:()=>o,s:()=>l});var a=r(58111),i=r(99366),s=r(33892),n=r(58356);class o extends a.L{constructor(e){if(super(e),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"examples",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"exampleSelector",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"examplePrompt",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"suffix",{enumerable:!0,configurable:!0,writable:!0,value:""}),Object.defineProperty(this,"exampleSeparator",{enumerable:!0,configurable:!0,writable:!0,value:"\n\n"}),Object.defineProperty(this,"prefix",{enumerable:!0,configurable:!0,writable:!0,value:""}),Object.defineProperty(this,"templateFormat",{enumerable:!0,configurable:!0,writable:!0,value:"f-string"}),Object.defineProperty(this,"validateTemplate",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.assign(this,e),void 0!==this.examples&&void 0!==this.exampleSelector)throw Error("Only one of 'examples' and 'example_selector' should be provided");if(void 0===this.examples&&void 0===this.exampleSelector)throw Error("One of 'examples' and 'example_selector' should be provided");if(this.validateTemplate){let e=this.inputVariables;this.partialVariables&&(e=e.concat(Object.keys(this.partialVariables))),(0,i.Ns)(this.prefix+this.suffix,this.templateFormat,e)}}_getPromptType(){return"few_shot"}static lc_name(){return"FewShotPromptTemplate"}async getExamples(e){if(void 0!==this.examples)return this.examples;if(void 0!==this.exampleSelector)return this.exampleSelector.selectExamples(e);throw Error("One of 'examples' and 'example_selector' should be provided")}async partial(e){let t=this.inputVariables.filter(t=>!(t in e)),r={...this.partialVariables??{},...e};return new o({...this,inputVariables:t,partialVariables:r})}async format(e){let t=await this.mergePartialAndUserVariables(e),r=await this.getExamples(t),a=await Promise.all(r.map(e=>this.examplePrompt.format(e))),s=[this.prefix,...a,this.suffix].join(this.exampleSeparator);return(0,i.Xm)(s,this.templateFormat,t)}serialize(){if(this.exampleSelector||!this.examples)throw Error("Serializing an example selector is not currently supported");if(void 0!==this.outputParser)throw Error("Serializing an output parser is not currently supported");return{_type:this._getPromptType(),input_variables:this.inputVariables,example_prompt:this.examplePrompt.serialize(),example_separator:this.exampleSeparator,suffix:this.suffix,prefix:this.prefix,template_format:this.templateFormat,examples:this.examples}}static async deserialize(e){let t,{example_prompt:r}=e;if(!r)throw Error("Missing example prompt");let a=await s.PromptTemplate.deserialize(r);if(Array.isArray(e.examples))t=e.examples;else throw Error("Invalid examples format. Only list or string are supported.");return new o({inputVariables:e.input_variables,examplePrompt:a,examples:t,exampleSeparator:e.example_separator,prefix:e.prefix,suffix:e.suffix,templateFormat:e.template_format})}}class l extends n.qF{_getPromptType(){return"few_shot_chat"}static lc_name(){return"FewShotChatMessagePromptTemplate"}constructor(e){if(super(e),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"examples",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"exampleSelector",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"examplePrompt",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"suffix",{enumerable:!0,configurable:!0,writable:!0,value:""}),Object.defineProperty(this,"exampleSeparator",{enumerable:!0,configurable:!0,writable:!0,value:"\n\n"}),Object.defineProperty(this,"prefix",{enumerable:!0,configurable:!0,writable:!0,value:""}),Object.defineProperty(this,"templateFormat",{enumerable:!0,configurable:!0,writable:!0,value:"f-string"}),Object.defineProperty(this,"validateTemplate",{enumerable:!0,configurable:!0,writable:!0,value:!0}),this.examples=e.examples,this.examplePrompt=e.examplePrompt,this.exampleSeparator=e.exampleSeparator??"\n\n",this.exampleSelector=e.exampleSelector,this.prefix=e.prefix??"",this.suffix=e.suffix??"",this.templateFormat=e.templateFormat??"f-string",this.validateTemplate=e.validateTemplate??!0,void 0!==this.examples&&void 0!==this.exampleSelector)throw Error("Only one of 'examples' and 'example_selector' should be provided");if(void 0===this.examples&&void 0===this.exampleSelector)throw Error("One of 'examples' and 'example_selector' should be provided");if(this.validateTemplate){let e=this.inputVariables;this.partialVariables&&(e=e.concat(Object.keys(this.partialVariables))),(0,i.Ns)(this.prefix+this.suffix,this.templateFormat,e)}}async getExamples(e){if(void 0!==this.examples)return this.examples;if(void 0!==this.exampleSelector)return this.exampleSelector.selectExamples(e);throw Error("One of 'examples' and 'example_selector' should be provided")}async formatMessages(e){let t=await this.mergePartialAndUserVariables(e),r=await this.getExamples(t);r=r.map(e=>{let t={};return this.examplePrompt.inputVariables.forEach(r=>{t[r]=e[r]}),t});let a=[];for(let e of r){let t=await this.examplePrompt.formatMessages(e);a.push(...t)}return a}async format(e){let t=await this.mergePartialAndUserVariables(e),r=await this.getExamples(t),a=(await Promise.all(r.map(e=>this.examplePrompt.formatMessages(e)))).flat().map(e=>e.content),s=[this.prefix,...a,this.suffix].join(this.exampleSeparator);return(0,i.Xm)(s,this.templateFormat,t)}async partial(e){let t=this.inputVariables.filter(t=>!(t in e)),r={...this.partialVariables??{},...e};return new l({...this,inputVariables:t,partialVariables:r})}}},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56534:(e,t,r)=>{"use strict";r.d(t,{Y:()=>c,w:()=>l});var a=r(55511),i=r.n(a);let s="aes-256-gcm",n=process.env.ROKEY_ENCRYPTION_KEY;if(!n||64!==n.length)throw Error("Invalid ROKEY_ENCRYPTION_KEY. Please set a 64-character hex string (32 bytes) for ROKEY_ENCRYPTION_KEY in your .env.local file.");let o=Buffer.from(n,"hex");function l(e){if("string"!=typeof e||0===e.length)throw Error("Encryption input must be a non-empty string.");let t=i().randomBytes(12),r=i().createCipheriv(s,o,t),a=r.update(e,"utf8","hex");a+=r.final("hex");let n=r.getAuthTag();return`${t.toString("hex")}:${n.toString("hex")}:${a}`}function c(e){if("string"!=typeof e||0===e.length)throw Error("Decryption input must be a non-empty string.");let t=e.split(":");if(3!==t.length)throw Error("Invalid encrypted text format. Expected iv:authTag:encryptedData");let r=Buffer.from(t[0],"hex"),a=Buffer.from(t[1],"hex"),n=t[2];if(12!==r.length)throw Error("Invalid IV length. Expected 12 bytes.");if(16!==a.length)throw Error("Invalid authTag length. Expected 16 bytes.");let l=i().createDecipheriv(s,o,r);l.setAuthTag(a);let c=l.update(n,"hex","utf8");return c+l.final("utf8")}},58111:(e,t,r)=>{"use strict";r.d(t,{L:()=>s});var a=r(62633),i=r(60799);class s extends i.m{async formatPromptValue(e){let t=await this.format(e);return new a.StringPromptValue(t)}}},58356:(e,t,r)=>{"use strict";r.d(t,{BJ:()=>_,FS:()=>b,GL:()=>p,OT:()=>d,RZ:()=>v,Wn:()=>m,pl:()=>h,qF:()=>f,sS:()=>y});var a=r(61751),i=r(62633),s=r(5589),n=r(58111),o=r(60799),l=r(33892),c=r(1025),u=r(99366);class h extends s.YN{constructor(){super(...arguments),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","prompts","chat"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0})}async invoke(e,t){return this._callWithConfig(e=>this.formatMessages(e),e,{...t,runType:"prompt"})}}class p extends h{static lc_name(){return"MessagesPlaceholder"}constructor(e){"string"==typeof e&&(e={variableName:e}),super(e),Object.defineProperty(this,"variableName",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"optional",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.variableName=e.variableName,this.optional=e.optional??!1}get inputVariables(){return[this.variableName]}async formatMessages(e){let t,r=e[this.variableName];if(this.optional&&!r)return[];if(!r){let e=Error(`Field "${this.variableName}" in prompt uses a MessagesPlaceholder, which expects an array of BaseMessages as an input value. Received: undefined`);throw e.name="InputFormatError",e}try{t=Array.isArray(r)?r.map(a.coerceMessageLikeToMessage):[(0,a.coerceMessageLikeToMessage)(r)]}catch(a){let e="string"==typeof r?r:JSON.stringify(r,null,2),t=Error(`Field "${this.variableName}" in prompt uses a MessagesPlaceholder, which expects an array of BaseMessages or coerceable values as input.

Received value: ${e}

Additional message: ${a.message}`);throw t.name="InputFormatError",t}return t}}class d extends h{constructor(e){"prompt"in e||(e={prompt:e}),super(e),Object.defineProperty(this,"prompt",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.prompt=e.prompt}get inputVariables(){return this.prompt.inputVariables}async formatMessages(e){return[await this.format(e)]}}class f extends o.m{constructor(e){super(e)}async format(e){return(await this.formatPromptValue(e)).toString()}async formatPromptValue(e){let t=await this.formatMessages(e);return new i.ChatPromptValue(t)}}class m extends d{static lc_name(){return"ChatMessagePromptTemplate"}constructor(e,t){"prompt"in e||(e={prompt:e,role:t}),super(e),Object.defineProperty(this,"role",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.role=e.role}async format(e){return new a.ChatMessage(await this.prompt.format(e),this.role)}static fromTemplate(e,t,r){return new this(l.PromptTemplate.fromTemplate(e,{templateFormat:r?.templateFormat}),t)}}class g extends h{static _messageClass(){throw Error("Can not invoke _messageClass from inside _StringImageMessagePromptTemplate")}constructor(e,t){if("prompt"in e||(e={prompt:e}),super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","prompts","chat"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"inputVariables",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"additionalOptions",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"prompt",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"messageClass",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"chatMessageClass",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.prompt=e.prompt,Array.isArray(this.prompt)){let e=[];this.prompt.forEach(t=>{"inputVariables"in t&&(e=e.concat(t.inputVariables))}),this.inputVariables=e}else this.inputVariables=this.prompt.inputVariables;this.additionalOptions=t??this.additionalOptions}createMessage(e){let t=this.constructor;if(t._messageClass())return new(t._messageClass())({content:e});if(t.chatMessageClass){let r=t.chatMessageClass();return new r({content:e,role:this.getRoleFromMessageClass(r.lc_name())})}throw Error("No message class defined")}getRoleFromMessageClass(e){switch(e){case"HumanMessage":return"human";case"AIMessage":return"ai";case"SystemMessage":return"system";case"ChatMessage":return"chat";default:throw Error("Invalid message class name")}}static fromTemplate(e,t){if("string"==typeof e)return new this(l.PromptTemplate.fromTemplate(e,t));let r=[];for(let a of e)if("string"==typeof a||"object"==typeof a&&"text"in a){let e="";"string"==typeof a?e=a:"string"==typeof a.text&&(e=a.text??"");let i={...t,..."string"!=typeof a?{additionalContentFields:a}:{}};r.push(l.PromptTemplate.fromTemplate(e,i))}else if("object"==typeof a&&"image_url"in a){let e,i=a.image_url??"",s=[];if("string"==typeof i){let r,n=(t?.templateFormat==="mustache"?(0,u.g2)(i):(0,u.D4)(i)).flatMap(e=>"variable"===e.type?[e.name]:[]);if((n?.length??0)>0){if(n.length>1)throw Error(`Only one format variable allowed per image template.
Got: ${n}
From: ${i}`);s=[n[0]]}else s=[];i={url:i},e=new c.C({template:i,inputVariables:s,templateFormat:t?.templateFormat,additionalContentFields:a})}else if("object"==typeof i){if("url"in i){let e;s=(t?.templateFormat==="mustache"?(0,u.g2)(i.url):(0,u.D4)(i.url)).flatMap(e=>"variable"===e.type?[e.name]:[])}else s=[];e=new c.C({template:i,inputVariables:s,templateFormat:t?.templateFormat,additionalContentFields:a})}else throw Error("Invalid image template");r.push(e)}return new this({prompt:r,additionalOptions:t})}async format(e){if(this.prompt instanceof n.L){let t=await this.prompt.format(e);return this.createMessage(t)}{let t=[];for(let r of this.prompt){let a={};if(!("inputVariables"in r))throw Error(`Prompt ${r} does not have inputVariables defined.`);for(let t of r.inputVariables)a||(a={[t]:e[t]}),a={...a,[t]:e[t]};if(r instanceof n.L){let e,i=await r.format(a);"additionalContentFields"in r&&(e=r.additionalContentFields),t.push({...e,type:"text",text:i})}else if(r instanceof c.C){let e,i=await r.format(a);"additionalContentFields"in r&&(e=r.additionalContentFields),t.push({...e,type:"image_url",image_url:i})}}return this.createMessage(t)}}async formatMessages(e){return[await this.format(e)]}}class b extends g{static _messageClass(){return a.HumanMessage}static lc_name(){return"HumanMessagePromptTemplate"}}class y extends g{static _messageClass(){return a.AIMessage}static lc_name(){return"AIMessagePromptTemplate"}}class _ extends g{static _messageClass(){return a.SystemMessage}static lc_name(){return"SystemMessagePromptTemplate"}}class v extends f{static lc_name(){return"ChatPromptTemplate"}get lc_aliases(){return{promptMessages:"messages"}}constructor(e){if(super(e),Object.defineProperty(this,"promptMessages",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"validateTemplate",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"templateFormat",{enumerable:!0,configurable:!0,writable:!0,value:"f-string"}),"mustache"===e.templateFormat&&void 0===e.validateTemplate&&(this.validateTemplate=!1),Object.assign(this,e),this.validateTemplate){let e=new Set;for(let t of this.promptMessages)if(!(t instanceof a.BaseMessage))for(let r of t.inputVariables)e.add(r);let t=this.inputVariables,r=new Set(this.partialVariables?t.concat(Object.keys(this.partialVariables)):t),i=new Set([...r].filter(t=>!e.has(t)));if(i.size>0)throw Error(`Input variables \`${[...i]}\` are not used in any of the prompt messages.`);let s=new Set([...e].filter(e=>!r.has(e)));if(s.size>0)throw Error(`Input variables \`${[...s]}\` are used in prompt messages but not in the prompt template.`)}}_getPromptType(){return"chat"}async _parseImagePrompts(e,t){if("string"==typeof e.content)return e;let r=await Promise.all(e.content.map(async e=>{if("image_url"!==e.type)return e;let r="";r="string"==typeof e.image_url?e.image_url:e.image_url.url;let a=l.PromptTemplate.fromTemplate(r,{templateFormat:this.templateFormat}),i=await a.format(t);return"string"!=typeof e.image_url&&"url"in e.image_url?e.image_url.url=i:e.image_url=i,e}));return e.content=r,e}async formatMessages(e){let t=await this.mergePartialAndUserVariables(e),r=[];for(let e of this.promptMessages)if(e instanceof a.BaseMessage)r.push(await this._parseImagePrompts(e,t));else{let a=e.inputVariables.reduce((r,a)=>{if(!(a in t)&&!("MessagesPlaceholder"===e.constructor.lc_name()&&e.optional))throw Error(`Missing value for input variable \`${a.toString()}\``);return r[a]=t[a],r},{}),i=await e.formatMessages(a);r=r.concat(i)}return r}async partial(e){let t=this.inputVariables.filter(t=>!(t in e)),r={...this.partialVariables??{},...e};return new v({...this,inputVariables:t,partialVariables:r})}static fromTemplate(e,t){let r=new b({prompt:l.PromptTemplate.fromTemplate(e,t)});return this.fromMessages([r])}static fromMessages(e,t){let r=e.reduce((e,r)=>e.concat(r instanceof v?r.promptMessages:[function(e,t){let r;if("function"==typeof e.formatMessages||(0,a.isBaseMessage)(e))return e;if(Array.isArray(e)&&"placeholder"===e[0]){let t=e[1];if("string"!=typeof t||"{"!==t[0]||"}"!==t[t.length-1])throw Error(`Invalid placeholder template: "${e[1]}". Expected a variable name surrounded by curly braces.`);return new p({variableName:t.slice(1,-1),optional:!0})}let i=(0,a.coerceMessageLikeToMessage)(e);if(r="string"==typeof i.content?i.content:i.content.map(e=>"text"in e?{...e,text:e.text}:"image_url"in e?{...e,image_url:e.image_url}:e),"human"===i._getType())return b.fromTemplate(r,t);if("ai"===i._getType())return y.fromTemplate(r,t);if("system"===i._getType())return _.fromTemplate(r,t);if(a.ChatMessage.isInstance(i))return m.fromTemplate(i.content,i.role,t);throw Error(`Could not coerce message prompt template from input. Received message type: "${i._getType()}".`)}(r,t)]),[]),i=e.reduce((e,t)=>t instanceof v?Object.assign(e,t.partialVariables):e,Object.create(null)),s=new Set;for(let e of r)if(!(e instanceof a.BaseMessage))for(let t of e.inputVariables)t in i||s.add(t);return new this({...t,inputVariables:[...s],promptMessages:r,partialVariables:i,templateFormat:t?.templateFormat})}static fromPromptMessages(e){return this.fromMessages(e)}}},60799:(e,t,r)=>{"use strict";r.d(t,{m:()=>i});var a=r(5589);class i extends a.YN{get lc_attributes(){return{partialVariables:void 0}}constructor(e){super(e),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","prompts",this._getPromptType()]}),Object.defineProperty(this,"inputVariables",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"outputParser",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"partialVariables",{enumerable:!0,configurable:!0,writable:!0,value:void 0});let{inputVariables:t}=e;if(t.includes("stop"))throw Error("Cannot have an input variable named 'stop', as it is used internally, please rename.");Object.assign(this,e)}async mergePartialAndUserVariables(e){let t=this.partialVariables??{},r={};for(let[e,a]of Object.entries(t))"string"==typeof a?r[e]=a:r[e]=await a();return{...r,...e}}async invoke(e,t){return this._callWithConfig(e=>this.formatPromptValue(e),e,{...t,runType:"prompt"})}serialize(){throw Error("Use .toJSON() instead")}static async deserialize(e){switch(e._type){case"prompt":{let{PromptTemplate:t}=await Promise.resolve().then(r.bind(r,33892));return t.deserialize(e)}case void 0:{let{PromptTemplate:t}=await Promise.resolve().then(r.bind(r,33892));return t.deserialize({...e,_type:"prompt"})}case"few_shot":{let{FewShotPromptTemplate:t}=await Promise.resolve().then(r.bind(r,46625));return t.deserialize(e)}default:throw Error(`Invalid prompt type in config: ${e._type}`)}}}},61751:(e,t,r)=>{"use strict";r.r(t),r.d(t,{AIMessage:()=>a.Od,AIMessageChunk:()=>a.H,BaseMessage:()=>i.XQ,BaseMessageChunk:()=>i.gj,ChatMessage:()=>s.c,ChatMessageChunk:()=>s.X,FunctionMessage:()=>n.m,FunctionMessageChunk:()=>n.F,HumanMessage:()=>o.x,HumanMessageChunk:()=>o.a,RemoveMessage:()=>h,SystemMessage:()=>l.t,SystemMessageChunk:()=>l.u,ToolMessage:()=>p.uf,ToolMessageChunk:()=>p.dr,_isMessageFieldWithRole:()=>i.dp,_mergeDicts:()=>i.ns,_mergeLists:()=>i.Vt,_mergeObj:()=>i.F7,_mergeStatus:()=>i.Iv,coerceMessageLikeToMessage:()=>c.K0,convertToChunk:()=>c.ih,defaultTextSplitter:()=>O,filterMessages:()=>f,getBufferString:()=>c.Sw,isAIMessage:()=>a.KX,isAIMessageChunk:()=>a.jm,isBaseMessage:()=>i.ny,isBaseMessageChunk:()=>i.AJ,isOpenAIToolCallArray:()=>i.Ao,mapChatMessagesToStoredMessages:()=>c.Js,mapStoredMessageToChatMessage:()=>c.Pz,mapStoredMessagesToChatMessages:()=>c.rf,mergeContent:()=>i._I,mergeMessageRuns:()=>g,trimMessages:()=>y});var a=r(16275),i=r(72892),s=r(28279),n=r(57161),o=r(75448),l=r(98488),c=r(81412),u=r(5589);class h extends i.XQ{constructor(e){super({...e,content:""}),Object.defineProperty(this,"id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.id=e.id}_getType(){return"remove"}get _printableFields(){return{...super._printableFields,id:this.id}}}var p=r(28895);let d=(e,t)=>{let r=[...new Set(t?.map(e=>{if("string"==typeof e)return e;let t=new e({});if(!("_getType"in t)||"function"!=typeof t._getType)throw Error("Invalid type provided.");return t._getType()}))],a=e._getType();return r.some(e=>e===a)};function f(e,t){return Array.isArray(e)?m(e,t):u.jY.from(t=>m(t,e))}function m(e,t={}){let{includeNames:r,excludeNames:a,includeTypes:i,excludeTypes:s,includeIds:n,excludeIds:o}=t,l=[];for(let t of e)if(!(a&&t.name&&a.includes(t.name))){if(s&&d(t,s))continue;if(o&&t.id&&o.includes(t.id))continue;i||n||r?r&&t.name&&r.some(e=>e===t.name)||i&&d(t,i)?l.push(t):n&&t.id&&n.some(e=>e===t.id)&&l.push(t):l.push(t)}return l}function g(e){return Array.isArray(e)?b(e):u.jY.from(b)}function b(e){if(!e.length)return[];let t=[];for(let r of e){let e=t.pop();if(e)if("tool"===r._getType()||r._getType()!==e._getType())t.push(e,r);else{let a=(0,c.ih)(e),i=(0,c.ih)(r),s=a.concat(i);"string"==typeof a.content&&"string"==typeof i.content&&(s.content=`${a.content}
${i.content}`),t.push(function(e){let t,r=e._getType(),a=Object.fromEntries(Object.entries(e).filter(([e])=>!["type","tool_call_chunks"].includes(e)&&!e.startsWith("lc_")));if(r in k&&(t=x(r,a)),!t)throw Error(`Unrecognized message chunk class ${r}. Supported classes are ${Object.keys(k)}`);return t}(s))}else t.push(r)}return t}function y(e,t){if(!Array.isArray(e))return u.jY.from(t=>_(t,e));if(!t)throw Error("Options parameter is required when providing messages.");return _(e,t)}async function _(e,t){let r,{maxTokens:a,tokenCounter:i,strategy:s="last",allowPartial:n=!1,endOn:o,startOn:l,includeSystem:c=!1,textSplitter:u}=t;if(l&&"first"===s)throw Error("`startOn` should only be specified if `strategy` is 'last'.");if(c&&"first"===s)throw Error("`includeSystem` should only be specified if `strategy` is 'last'.");r="getNumTokens"in i?async e=>(await Promise.all(e.map(e=>i.getNumTokens(e.content)))).reduce((e,t)=>e+t,0):async e=>i(e);let h=O;if(u&&(h="splitText"in u?u.splitText:async e=>u(e)),"first"===s)return v(e,{maxTokens:a,tokenCounter:r,textSplitter:h,partialStrategy:n?"first":void 0,endOn:o});if("last"===s)return w(e,{maxTokens:a,tokenCounter:r,textSplitter:h,allowPartial:n,includeSystem:c,startOn:l,endOn:o});throw Error(`Unrecognized strategy: '${s}'. Must be one of 'first' or 'last'.`)}async function v(e,t){let{maxTokens:r,tokenCounter:a,textSplitter:i,partialStrategy:s,endOn:n}=t,o=[...e],l=0;for(let e=0;e<o.length;e+=1){let t=e>0?o.slice(0,-e):o;if(await a(t)<=r){l=o.length-e;break}}if(l<o.length-1&&s){let e=!1;if(Array.isArray(o[l].content)){let t=o[l];if("string"==typeof t.content)throw Error("Expected content to be an array.");let i=t.content.length,n="last"===s?[...t.content].reverse():t.content;for(let c=1;c<=i;c+=1){let i="first"===s?n.slice(0,c):n.slice(-c),u=Object.fromEntries(Object.entries(t).filter(([e])=>"type"!==e&&!e.startsWith("lc_"))),h=x(t._getType(),{...u,content:i}),p=[...o.slice(0,l),h];if(await a(p)<=r)o=p,l+=1,e=!0;else break}e&&"last"===s&&(t.content=[...n].reverse())}if(!e){let e,t=o[l];if(Array.isArray(t.content)&&t.content.some(e=>"string"==typeof e||"text"===e.type)){let r=t.content.find(e=>"text"===e.type&&e.text);e=r?.text}else"string"==typeof t.content&&(e=t.content);if(e){let n=await i(e),c=n.length;"last"===s&&n.reverse();for(let e=0;e<c-1;e+=1)if(n.pop(),t.content=n.join(""),await a([...o.slice(0,l),t])<=r){"last"===s&&(t.content=[...n].reverse().join("")),o=[...o.slice(0,l),t],l+=1;break}}}}if(n){let e=Array.isArray(n)?n:[n];for(;l>0&&!d(o[l-1],e);)l-=1}return o.slice(0,l)}async function w(e,t){let{allowPartial:r=!1,includeSystem:a=!1,endOn:i,startOn:s,...n}=t;if(i){let t=Array.isArray(i)?i:[i];for(;e&&!d(e[e.length-1],t);)e.pop()}let o=a&&"system"===e[0]._getType(),l=o?e.slice(0,1).concat(e.slice(1).reverse()):e.reverse();return(l=await v(l,{...n,partialStrategy:r?"last":void 0,endOn:s}),o)?[l[0],...l.slice(1).reverse()]:l.reverse()}let k={human:{message:o.x,messageChunk:o.a},ai:{message:a.Od,messageChunk:a.H},system:{message:l.t,messageChunk:l.u},tool:{message:p.uf,messageChunk:p.dr},function:{message:n.m,messageChunk:n.F},generic:{message:s.c,messageChunk:s.X},remove:{message:h,messageChunk:h}};function x(e,t,r){let i,c;switch(e){case"human":r?i=new o.a(t):c=new o.x(t);break;case"ai":if(r){let e={...t};"tool_calls"in e&&(e={...e,tool_call_chunks:e.tool_calls?.map(e=>({...e,type:"tool_call_chunk",index:void 0,args:JSON.stringify(e.args)}))}),i=new a.H(e)}else c=new a.Od(t);break;case"system":r?i=new l.u(t):c=new l.t(t);break;case"tool":if("tool_call_id"in t)r?i=new p.dr(t):c=new p.uf(t);else throw Error("Can not convert ToolMessage to ToolMessageChunk if 'tool_call_id' field is not defined.");break;case"function":if(r)i=new n.F(t);else{if(!t.name)throw Error("FunctionMessage must have a 'name' field");c=new n.m(t)}break;case"generic":if("role"in t)r?i=new s.X(t):c=new s.c(t);else throw Error("Can not convert ChatMessage to ChatMessageChunk if 'role' field is not defined.");break;default:throw Error(`Unrecognized message type ${e}`)}if(r&&i)return i;if(c)return c;throw Error(`Unrecognized message type ${e}`)}function O(e){let t=e.split("\n");return Promise.resolve([...t.slice(0,-1).map(e=>`${e}
`),t[t.length-1]])}},62633:(e,t,r)=>{"use strict";r.r(t),r.d(t,{BasePromptValue:()=>n,ChatPromptValue:()=>l,ImagePromptValue:()=>c,StringPromptValue:()=>o});var a=r(84902),i=r(75448),s=r(81412);class n extends a.Serializable{}class o extends n{static lc_name(){return"StringPromptValue"}constructor(e){super({value:e}),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","prompt_values"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.value=e}toString(){return this.value}toChatMessages(){return[new i.x(this.value)]}}class l extends n{static lc_name(){return"ChatPromptValue"}constructor(e){Array.isArray(e)&&(e={messages:e}),super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","prompt_values"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"messages",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.messages=e.messages}toString(){return(0,s.Sw)(this.messages)}toChatMessages(){return this.messages}}class c extends n{static lc_name(){return"ImagePromptValue"}constructor(e){"imageUrl"in e||(e={imageUrl:e}),super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","prompt_values"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"imageUrl",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.imageUrl=e.imageUrl}toString(){return this.imageUrl.url}toChatMessages(){return[new i.x({content:[{type:"image_url",image_url:{detail:this.imageUrl.detail,url:this.imageUrl.url}}]})]}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},76549:(e,t,r)=>{"use strict";let a,i;r.r(t),r.d(t,{patchFetch:()=>sF,routeModule:()=>sM,serverHooks:()=>sD,workAsyncStorage:()=>sR,workUnitAsyncStorage:()=>sL});var s={};r.r(s);var n={};r.r(n),r.d(n,{insecureHash:()=>eP});var o={};r.r(o),r.d(o,{BaseCache:()=>ej,InMemoryCache:()=>eI,deserializeStoredGeneration:()=>eS,getCacheKey:()=>eE,serializeGeneration:()=>eC});var l={};r.r(l),r.d(l,{BaseChatMessageHistory:()=>eL,BaseListChatMessageHistory:()=>eD,InMemoryChatMessageHistory:()=>eF});var c={};r.r(c),r.d(c,{Embeddings:()=>eV});var u={};r.r(u),r.d(u,{BaseExampleSelector:()=>eK,BasePromptSelector:()=>eq,ConditionalPromptSelector:()=>eB,LengthBasedExampleSelector:()=>eJ,SemanticSimilarityExampleSelector:()=>eQ,isChatModel:()=>eG,isLLM:()=>eY});var h={};r.r(h),r.d(h,{BaseLangChain:()=>e6,BaseLanguageModel:()=>e7,calculateMaxTokens:()=>e3,getEmbeddingContextSize:()=>e5,getModelContextSize:()=>e9,getModelNameForTiktoken:()=>e2,isOpenAITool:()=>e4});var p={};r.r(p),r.d(p,{BaseChatModel:()=>tc,SimpleChatModel:()=>tu,createChatMessageChunkEncoderStream:()=>tl});var d={};r.r(d),r.d(d,{BaseLLM:()=>th,LLM:()=>tp});var f={};r.r(f),r.d(f,{BaseMemory:()=>td,getInputValue:()=>tm,getOutputValue:()=>tg,getPromptInputKey:()=>tb});var m={};r.r(m),r.d(m,{RouterRunnable:()=>ty,Runnable:()=>e1.YN,RunnableAssign:()=>e1.B2,RunnableBinding:()=>e1.fJ,RunnableBranch:()=>t_,RunnableEach:()=>e1.Vi,RunnableLambda:()=>e1.jY,RunnableMap:()=>e1.ck,RunnableParallel:()=>e1.Pq,RunnablePassthrough:()=>tn,RunnablePick:()=>e1.Fm,RunnableRetry:()=>e1.AO,RunnableSequence:()=>e1.zZ,RunnableToolLike:()=>e1.pG,RunnableWithFallbacks:()=>e1.lH,RunnableWithMessageHistory:()=>tv,_coerceToRunnable:()=>e1.Bp,ensureConfig:()=>ts.ZI,getCallbackManagerForConfig:()=>ts.kJ,mergeConfigs:()=>ts.SV,patchConfig:()=>ts.tn});var g={};r.r(g),r.d(g,{applyPatch:()=>t2.X6,compare:()=>t2.UD});var b={};r.r(b),r.d(b,{AsymmetricStructuredOutputParser:()=>t1,BaseCumulativeTransformOutputParser:()=>tB,BaseLLMOutputParser:()=>tw,BaseOutputParser:()=>tk,BaseTransformOutputParser:()=>tq,BytesOutputParser:()=>tY,CommaSeparatedListOutputParser:()=>tW,CustomListOutputParser:()=>tJ,JsonMarkdownStructuredOutputParser:()=>t0,JsonOutputParser:()=>t9,ListOutputParser:()=>tG,MarkdownListOutputParser:()=>tX,NumberedListOutputParser:()=>tH,OutputParserException:()=>tx,StringOutputParser:()=>tQ,StructuredOutputParser:()=>tZ,XMLOutputParser:()=>t8,XML_FORMAT_INSTRUCTIONS:()=>t3,parseJsonMarkdown:()=>t5.D,parsePartialJson:()=>t5.d,parseXMLMarkdown:()=>re});var y={};r.r(y),r.d(y,{AIMessagePromptTemplate:()=>rr.sS,BaseChatPromptTemplate:()=>rr.qF,BaseMessagePromptTemplate:()=>rr.pl,BaseMessageStringPromptTemplate:()=>rr.OT,BasePromptTemplate:()=>rt.m,BaseStringPromptTemplate:()=>rn.L,ChatMessagePromptTemplate:()=>rr.Wn,ChatPromptTemplate:()=>rr.RZ,DEFAULT_FORMATTER_MAPPING:()=>ro.ez,DEFAULT_PARSER_MAPPING:()=>ro.RG,FewShotChatMessagePromptTemplate:()=>ra.s,FewShotPromptTemplate:()=>ra.FewShotPromptTemplate,HumanMessagePromptTemplate:()=>rr.FS,ImagePromptTemplate:()=>rl.C,MessagesPlaceholder:()=>rr.GL,PipelinePromptTemplate:()=>ri,PromptTemplate:()=>rs.PromptTemplate,StructuredPrompt:()=>ru,SystemMessagePromptTemplate:()=>rr.BJ,checkValidTemplate:()=>ro.Ns,interpolateFString:()=>ro.Vt,interpolateMustache:()=>ro.Qs,parseFString:()=>ro.D4,parseMustache:()=>ro.g2,parseTemplate:()=>ro.QC,renderTemplate:()=>ro.Xm});var _={};r.r(_),r.d(_,{BaseRetriever:()=>rh});var v={};r.r(v),r.d(v,{BaseStore:()=>rp,InMemoryStore:()=>rd});var w={};r.r(w),r.d(w,{BaseToolkit:()=>rv,DynamicStructuredTool:()=>r_,DynamicTool:()=>ry,StructuredTool:()=>rg,Tool:()=>rb,ToolInputParsingException:()=>rm.q,tool:()=>rw});var k={};r.r(k),r.d(k,{LangChainTracerV1:()=>rT});var x={};r.r(x),r.d(x,{getTracingCallbackHandler:()=>rE,getTracingV2CallbackHandler:()=>rS});var O={};r.r(O),r.d(O,{RunCollectorCallbackHandler:()=>rC});var P={};r.r(P),r.d(P,{chunkArray:()=>rj});var T={};r.r(T),r.d(T,{convertToOpenAIFunction:()=>rA,convertToOpenAITool:()=>rI,isLangChainTool:()=>rR,isRunnableToolLike:()=>rN,isStructuredTool:()=>r$,isStructuredToolParams:()=>rM});var E={};r.r(E),r.d(E,{Validator:()=>tK,deepCompareStrict:()=>tP});var S={};r.r(S),r.d(S,{cosineSimilarity:()=>rV,euclideanDistance:()=>rq,innerProduct:()=>rK,matrixFunc:()=>rz,maximalMarginalRelevance:()=>rB,normalize:()=>rU});var C={};r.r(C),r.d(C,{SaveableVectorStore:()=>rJ,VectorStore:()=>rW,VectorStoreRetriever:()=>rG});var j={};r.r(j),r.d(j,{FakeChatMessageHistory:()=>r9,FakeChatModel:()=>r0,FakeEmbeddings:()=>r6,FakeLLM:()=>rQ,FakeListChatMessageHistory:()=>r4,FakeListChatModel:()=>r5,FakeRetriever:()=>r2,FakeRunnable:()=>rX,FakeSplitIntoListParser:()=>rH,FakeStreamingChatModel:()=>r1,FakeStreamingLLM:()=>rZ,FakeTool:()=>r8,FakeTracer:()=>r3,FakeVectorStore:()=>at,SingleRunExtractor:()=>ae,SyntheticEmbeddings:()=>r7});var A={};r.r(A),r.d(A,{isZodSchema:()=>to});var I={};r.r(I),r.d(I,{agents:()=>s,caches:()=>o,callbacks__base:()=>e$,callbacks__manager:()=>eN,callbacks__promises:()=>eM,chat_history:()=>l,documents:()=>ez,embeddings:()=>c,example_selectors:()=>u,language_models__base:()=>h,language_models__chat_models:()=>p,language_models__llms:()=>d,load__serializable:()=>eb,memory:()=>f,messages:()=>eR,output_parsers:()=>b,outputs:()=>tt,prompt_values:()=>eZ,prompts:()=>y,retrievers:()=>_,runnables:()=>m,stores:()=>v,tools:()=>w,tracers__base:()=>rk,tracers__console:()=>rx,tracers__initialize:()=>x,tracers__log_stream:()=>ta,tracers__run_collector:()=>O,tracers__tracer_langchain:()=>rO,tracers__tracer_langchain_v1:()=>k,utils__async_caller:()=>eU,utils__chunk_array:()=>P,utils__env:()=>rP,utils__function_calling:()=>T,utils__hash:()=>n,utils__json_patch:()=>g,utils__json_schema:()=>E,utils__math:()=>S,utils__stream:()=>ti,utils__testing:()=>j,utils__tiktoken:()=>e0,utils__types:()=>A,vectorstores:()=>C});var $={};r.r($),r.d($,{OPTIONS:()=>sN,POST:()=>s$,executeProviderRequest:()=>sI});var N=r(96559),M=r(48088),R=r(37719),L=r(32190),D=r(2507),F=r(39398),z=r(56534),U=r(507),V=r(45697);let K={defaultKeySuccess:(e=1)=>1===e?"default_key_success":`default_key_success_attempt_${e}`,allKeysFailed:e=>`default_all_${e}_attempts_failed`,roleRouting:e=>e,intelligentRoleRouting:e=>`intelligent_role_${e}`,fallbackRouting:e=>`fallback_position_${e}`};var q=r(24967);let B=require("node:async_hooks");class Y extends Error{constructor(e,t){let r=e??"";t?.lc_error_code&&(r=`${r}

Troubleshooting URL: https://js.langchain.com/docs/troubleshooting/errors/${t.lc_error_code}/
`),super(r),Object.defineProperty(this,"lc_error_code",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.lc_error_code=t?.lc_error_code}}class G extends Y{constructor(e,t){super(e,t),this.name="GraphRecursionError"}static get unminifiable_name(){return"GraphRecursionError"}}class W extends Y{constructor(e,t){super(e,t),this.name="GraphValueError"}static get unminifiable_name(){return"GraphValueError"}}class J extends Y{constructor(e,t){super(JSON.stringify(e,null,2),t),Object.defineProperty(this,"interrupts",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.name="GraphInterrupt",this.interrupts=e??[]}static get unminifiable_name(){return"GraphInterrupt"}}class H extends J{constructor(e,t){super([{value:e,when:"during"}],t),this.name="NodeInterrupt"}static get unminifiable_name(){return"NodeInterrupt"}}function X(e){return void 0!==e&&[J.unminifiable_name,H.unminifiable_name].includes(e.name)}class Q extends Y{constructor(e,t){super(e,t),this.name="EmptyInputError"}static get unminifiable_name(){return"EmptyInputError"}}class Z extends Y{constructor(e,t){super(e,t),this.name="EmptyChannelError"}static get unminifiable_name(){return"EmptyChannelError"}}class ee extends Y{constructor(e,t){super(e,t),this.name="InvalidUpdateError"}static get unminifiable_name(){return"InvalidUpdateError"}}class et extends Y{constructor(e,t){super(e,t),this.name="MultipleSubgraphError"}static get unminifiable_name(){return"MultipleSubgraphError"}}let er=()=>(void 0===globalThis[Symbol.for("LG_CHECKPOINT_SEEN_NS_SET")]&&(globalThis[Symbol.for("LG_CHECKPOINT_SEEN_NS_SET")]=new Set),globalThis[Symbol.for("LG_CHECKPOINT_SEEN_NS_SET")]),ea=[];for(let e=0;e<256;++e)ea.push((e+256).toString(16).slice(1));function ei(e,t=0){return(ea[e[t+0]]+ea[e[t+1]]+ea[e[t+2]]+ea[e[t+3]]+"-"+ea[e[t+4]]+ea[e[t+5]]+"-"+ea[e[t+6]]+ea[e[t+7]]+"-"+ea[e[t+8]]+ea[e[t+9]]+"-"+ea[e[t+10]]+ea[e[t+11]]+ea[e[t+12]]+ea[e[t+13]]+ea[e[t+14]]+ea[e[t+15]]).toLowerCase()}var es=r(77598),en=r.n(es);let eo=new Uint8Array(256),el=eo.length,ec=0,eu=0,eh=function(e,t,r){let s=t&&r||0,n=t||Array(16),o=(e=e||{}).node,l=e.clockseq;if(e._v6||(o||(o=a),null==l&&(l=i)),null==o||null==l){let t=e.random||(e.rng||function(){return el>eo.length-16&&(en().randomFillSync(eo),el=0),eo.slice(el,el+=16)})();null==o&&(o=[t[0],t[1],t[2],t[3],t[4],t[5]],a||e._v6||(o[0]|=1,a=o)),null==l&&(l=(t[6]<<8|t[7])&16383,void 0!==i||e._v6||(i=l))}let c=void 0!==e.msecs?e.msecs:Date.now(),u=void 0!==e.nsecs?e.nsecs:eu+1,h=c-ec+(u-eu)/1e4;if(h<0&&void 0===e.clockseq&&(l=l+1&16383),(h<0||c>ec)&&void 0===e.nsecs&&(u=0),u>=1e4)throw Error("uuid.v1(): Can't create more than 10M uuids/sec");ec=c,eu=u,i=l;let p=((0xfffffff&(c+=122192928e5))*1e4+u)%0x100000000;n[s++]=p>>>24&255,n[s++]=p>>>16&255,n[s++]=p>>>8&255,n[s++]=255&p;let d=c/0x100000000*1e4&0xfffffff;n[s++]=d>>>8&255,n[s++]=255&d,n[s++]=d>>>24&15|16,n[s++]=d>>>16&255,n[s++]=l>>>8|128,n[s++]=255&l;for(let e=0;e<6;++e)n[s+e]=o[e];return t||ei(n)},ep=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i,ed=function(e){let t;if(!("string"==typeof e&&ep.test(e)))throw TypeError("Invalid UUID");let r=new Uint8Array(16);return r[0]=(t=parseInt(e.slice(0,8),16))>>>24,r[1]=t>>>16&255,r[2]=t>>>8&255,r[3]=255&t,r[4]=(t=parseInt(e.slice(9,13),16))>>>8,r[5]=255&t,r[6]=(t=parseInt(e.slice(14,18),16))>>>8,r[7]=255&t,r[8]=(t=parseInt(e.slice(19,23),16))>>>8,r[9]=255&t,r[10]=(t=parseInt(e.slice(24,36),16))/0x10000000000&255,r[11]=t/0x100000000&255,r[12]=t>>>24&255,r[13]=t>>>16&255,r[14]=t>>>8&255,r[15]=255&t,r},ef=function(e,t,r){function a(e,t,a,i){var s;if("string"==typeof e&&(e=function(e){e=unescape(encodeURIComponent(e));let t=[];for(let r=0;r<e.length;++r)t.push(e.charCodeAt(r));return t}(e)),"string"==typeof t&&(t=ed(t)),(null==(s=t)?void 0:s.length)!==16)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");let n=new Uint8Array(16+e.length);if(n.set(t),n.set(e,t.length),(n=r(n))[6]=15&n[6]|80,n[8]=63&n[8]|128,a){i=i||0;for(let e=0;e<16;++e)a[i+e]=n[e];return a}return ei(n)}try{a.name="v5"}catch(e){}return a.DNS="6ba7b810-9dad-11d1-80b4-00c04fd430c8",a.URL="6ba7b811-9dad-11d1-80b4-00c04fd430c8",a}(0,0,function(e){return Array.isArray(e)?e=Buffer.from(e):"string"==typeof e&&(e=Buffer.from(e,"utf8")),en().createHash("sha1").update(e).digest()});function em(e){return function(e={},t,r=0){let a=eh({...e,_v6:!0},new Uint8Array(16));return a=function(e){let t=function(e,t=!1){return Uint8Array.of((15&e[6])<<4|e[7]>>4&15,(15&e[7])<<4|(240&e[4])>>4,(15&e[4])<<4|(240&e[5])>>4,(15&e[5])<<4|(240&e[0])>>4,(15&e[0])<<4|(240&e[1])>>4,(15&e[1])<<4|(240&e[2])>>4,96|15&e[2],e[3],e[8],e[9],e[10],e[11],e[12],e[13],e[14],e[15])}("string"==typeof e?ed(e):e);return"string"==typeof e?ei(t):t}(a),ei(a)}({clockseq:e})}function eg(e,t){return ef(e,new Uint8Array(t.replace(/-/g,"").match(/.{2}/g).map(e=>parseInt(e,16))))}var eb=r(84902);let ey=[];var e_="object"==typeof window?window:{},ev="0123456789abcdef".split(""),ew=[-0x80000000,8388608,32768,128],ek=[24,16,8,0],ex=[];function eO(e){e?(ex[0]=ex[16]=ex[1]=ex[2]=ex[3]=ex[4]=ex[5]=ex[6]=ex[7]=ex[8]=ex[9]=ex[10]=ex[11]=ex[12]=ex[13]=ex[14]=ex[15]=0,this.blocks=ex):this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],this.h0=0x67452301,this.h1=0xefcdab89,this.h2=0x98badcfe,this.h3=0x10325476,this.h4=0xc3d2e1f0,this.block=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}eO.prototype.update=function(e){if(!this.finalized){var t="string"!=typeof e;t&&e.constructor===e_.ArrayBuffer&&(e=new Uint8Array(e));for(var r,a,i=0,s=e.length||0,n=this.blocks;i<s;){if(this.hashed&&(this.hashed=!1,n[0]=this.block,n[16]=n[1]=n[2]=n[3]=n[4]=n[5]=n[6]=n[7]=n[8]=n[9]=n[10]=n[11]=n[12]=n[13]=n[14]=n[15]=0),t)for(a=this.start;i<s&&a<64;++i)n[a>>2]|=e[i]<<ek[3&a++];else for(a=this.start;i<s&&a<64;++i)(r=e.charCodeAt(i))<128?n[a>>2]|=r<<ek[3&a++]:(r<2048?n[a>>2]|=(192|r>>6)<<ek[3&a++]:(r<55296||r>=57344?n[a>>2]|=(224|r>>12)<<ek[3&a++]:(r=65536+((1023&r)<<10|1023&e.charCodeAt(++i)),n[a>>2]|=(240|r>>18)<<ek[3&a++],n[a>>2]|=(128|r>>12&63)<<ek[3&a++]),n[a>>2]|=(128|r>>6&63)<<ek[3&a++]),n[a>>2]|=(128|63&r)<<ek[3&a++]);this.lastByteIndex=a,this.bytes+=a-this.start,a>=64?(this.block=n[16],this.start=a-64,this.hash(),this.hashed=!0):this.start=a}return this.bytes>0xffffffff&&(this.hBytes+=this.bytes/0x100000000|0,this.bytes=this.bytes%0x100000000),this}},eO.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var e=this.blocks,t=this.lastByteIndex;e[16]=this.block,e[t>>2]|=ew[3&t],this.block=e[16],t>=56&&(this.hashed||this.hash(),e[0]=this.block,e[16]=e[1]=e[2]=e[3]=e[4]=e[5]=e[6]=e[7]=e[8]=e[9]=e[10]=e[11]=e[12]=e[13]=e[14]=e[15]=0),e[14]=this.hBytes<<3|this.bytes>>>29,e[15]=this.bytes<<3,this.hash()}},eO.prototype.hash=function(){var e,t,r,a=this.h0,i=this.h1,s=this.h2,n=this.h3,o=this.h4,l=this.blocks;for(t=16;t<80;++t)r=l[t-3]^l[t-8]^l[t-14]^l[t-16],l[t]=r<<1|r>>>31;for(t=0;t<20;t+=5)e=i&s|~i&n,o=(r=a<<5|a>>>27)+e+o+0x5a827999+l[t]|0,e=a&(i=i<<30|i>>>2)|~a&s,n=(r=o<<5|o>>>27)+e+n+0x5a827999+l[t+1]|0,e=o&(a=a<<30|a>>>2)|~o&i,s=(r=n<<5|n>>>27)+e+s+0x5a827999+l[t+2]|0,e=n&(o=o<<30|o>>>2)|~n&a,i=(r=s<<5|s>>>27)+e+i+0x5a827999+l[t+3]|0,e=s&(n=n<<30|n>>>2)|~s&o,a=(r=i<<5|i>>>27)+e+a+0x5a827999+l[t+4]|0,s=s<<30|s>>>2;for(;t<40;t+=5)e=i^s^n,o=(r=a<<5|a>>>27)+e+o+0x6ed9eba1+l[t]|0,e=a^(i=i<<30|i>>>2)^s,n=(r=o<<5|o>>>27)+e+n+0x6ed9eba1+l[t+1]|0,e=o^(a=a<<30|a>>>2)^i,s=(r=n<<5|n>>>27)+e+s+0x6ed9eba1+l[t+2]|0,e=n^(o=o<<30|o>>>2)^a,i=(r=s<<5|s>>>27)+e+i+0x6ed9eba1+l[t+3]|0,e=s^(n=n<<30|n>>>2)^o,a=(r=i<<5|i>>>27)+e+a+0x6ed9eba1+l[t+4]|0,s=s<<30|s>>>2;for(;t<60;t+=5)e=i&s|i&n|s&n,o=(r=a<<5|a>>>27)+e+o-0x70e44324+l[t]|0,e=a&(i=i<<30|i>>>2)|a&s|i&s,n=(r=o<<5|o>>>27)+e+n-0x70e44324+l[t+1]|0,e=o&(a=a<<30|a>>>2)|o&i|a&i,s=(r=n<<5|n>>>27)+e+s-0x70e44324+l[t+2]|0,e=n&(o=o<<30|o>>>2)|n&a|o&a,i=(r=s<<5|s>>>27)+e+i-0x70e44324+l[t+3]|0,e=s&(n=n<<30|n>>>2)|s&o|n&o,a=(r=i<<5|i>>>27)+e+a-0x70e44324+l[t+4]|0,s=s<<30|s>>>2;for(;t<80;t+=5)e=i^s^n,o=(r=a<<5|a>>>27)+e+o-0x359d3e2a+l[t]|0,e=a^(i=i<<30|i>>>2)^s,n=(r=o<<5|o>>>27)+e+n-0x359d3e2a+l[t+1]|0,e=o^(a=a<<30|a>>>2)^i,s=(r=n<<5|n>>>27)+e+s-0x359d3e2a+l[t+2]|0,e=n^(o=o<<30|o>>>2)^a,i=(r=s<<5|s>>>27)+e+i-0x359d3e2a+l[t+3]|0,e=s^(n=n<<30|n>>>2)^o,a=(r=i<<5|i>>>27)+e+a-0x359d3e2a+l[t+4]|0,s=s<<30|s>>>2;this.h0=this.h0+a|0,this.h1=this.h1+i|0,this.h2=this.h2+s|0,this.h3=this.h3+n|0,this.h4=this.h4+o|0},eO.prototype.hex=function(){this.finalize();var e=this.h0,t=this.h1,r=this.h2,a=this.h3,i=this.h4;return ev[e>>28&15]+ev[e>>24&15]+ev[e>>20&15]+ev[e>>16&15]+ev[e>>12&15]+ev[e>>8&15]+ev[e>>4&15]+ev[15&e]+ev[t>>28&15]+ev[t>>24&15]+ev[t>>20&15]+ev[t>>16&15]+ev[t>>12&15]+ev[t>>8&15]+ev[t>>4&15]+ev[15&t]+ev[r>>28&15]+ev[r>>24&15]+ev[r>>20&15]+ev[r>>16&15]+ev[r>>12&15]+ev[r>>8&15]+ev[r>>4&15]+ev[15&r]+ev[a>>28&15]+ev[a>>24&15]+ev[a>>20&15]+ev[a>>16&15]+ev[a>>12&15]+ev[a>>8&15]+ev[a>>4&15]+ev[15&a]+ev[i>>28&15]+ev[i>>24&15]+ev[i>>20&15]+ev[i>>16&15]+ev[i>>12&15]+ev[i>>8&15]+ev[i>>4&15]+ev[15&i]},eO.prototype.toString=eO.prototype.hex,eO.prototype.digest=function(){this.finalize();var e=this.h0,t=this.h1,r=this.h2,a=this.h3,i=this.h4;return[e>>24&255,e>>16&255,e>>8&255,255&e,t>>24&255,t>>16&255,t>>8&255,255&t,r>>24&255,r>>16&255,r>>8&255,255&r,a>>24&255,a>>16&255,a>>8&255,255&a,i>>24&255,i>>16&255,i>>8&255,255&i]},eO.prototype.array=eO.prototype.digest,eO.prototype.arrayBuffer=function(){this.finalize();var e=new ArrayBuffer(20),t=new DataView(e);return t.setUint32(0,this.h0),t.setUint32(4,this.h1),t.setUint32(8,this.h2),t.setUint32(12,this.h3),t.setUint32(16,this.h4),e};let eP=e=>new eO(!0).update(e).hex();var eT=r(81412);let eE=(...e)=>eP(e.join("_"));function eS(e){return void 0!==e.message?{text:e.text,message:(0,eT.Pz)(e.message)}:{text:e.text}}function eC(e){let t={text:e.text};return void 0!==e.message&&(t.message=e.message.toDict()),t}class ej{}let eA=new Map;class eI extends ej{constructor(e){super(),Object.defineProperty(this,"cache",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.cache=e??new Map}lookup(e,t){return Promise.resolve(this.cache.get(eE(e,t))??null)}async update(e,t,r){this.cache.set(eE(e,t),r)}static global(){return new eI(eA)}}var e$=r(60662),eN=r(1787),eM=r(45666),eR=r(61751);class eL extends eb.Serializable{async addMessages(e){for(let t of e)await this.addMessage(t)}}class eD extends eb.Serializable{addUserMessage(e){return this.addMessage(new eR.HumanMessage(e))}addAIChatMessage(e){return this.addMessage(new eR.AIMessage(e))}addAIMessage(e){return this.addMessage(new eR.AIMessage(e))}async addMessages(e){for(let t of e)await this.addMessage(t)}clear(){throw Error("Not implemented.")}}class eF extends eD{constructor(e){super(...arguments),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain","stores","message","in_memory"]}),Object.defineProperty(this,"messages",{enumerable:!0,configurable:!0,writable:!0,value:[]}),this.messages=e??[]}async getMessages(){return this.messages}async addMessage(e){this.messages.push(e)}async clear(){this.messages=[]}}var ez=r(76947),eU=r(15641);class eV{constructor(e){Object.defineProperty(this,"caller",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.caller=new eU.AsyncCaller(e??{})}}class eK extends eb.Serializable{constructor(){super(...arguments),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","example_selectors","base"]})}}class eq{async getPromptAsync(e,t){return this.getPrompt(e).partial(t?.partialVariables??{})}}class eB extends eq{constructor(e,t=[]){super(),Object.defineProperty(this,"defaultPrompt",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"conditionals",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.defaultPrompt=e,this.conditionals=t}getPrompt(e){for(let[t,r]of this.conditionals)if(t(e))return r;return this.defaultPrompt}}function eY(e){return"base_llm"===e._modelType()}function eG(e){return"base_chat_model"===e._modelType()}function eW(e){return e.split(/\n| /).length}class eJ extends eK{constructor(e){super(e),Object.defineProperty(this,"examples",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"examplePrompt",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"getTextLength",{enumerable:!0,configurable:!0,writable:!0,value:eW}),Object.defineProperty(this,"maxLength",{enumerable:!0,configurable:!0,writable:!0,value:2048}),Object.defineProperty(this,"exampleTextLengths",{enumerable:!0,configurable:!0,writable:!0,value:[]}),this.examplePrompt=e.examplePrompt,this.maxLength=e.maxLength??2048,this.getTextLength=e.getTextLength??eW}async addExample(e){this.examples.push(e);let t=await this.examplePrompt.format(e);this.exampleTextLengths.push(this.getTextLength(t))}async calculateExampleTextLengths(e,t){if(e.length>0)return e;let{examples:r,examplePrompt:a}=t;return(await Promise.all(r.map(e=>a.format(e)))).map(e=>this.getTextLength(e))}async selectExamples(e){let t=Object.values(e).join(" "),r=this.maxLength-this.getTextLength(t),a=0,i=[];for(;r>0&&a<this.examples.length;){let e=r-this.exampleTextLengths[a];if(e<0)break;i.push(this.examples[a]),r=e,a+=1}return i}static async fromExamples(e,t){let r=new eJ(t);return await Promise.all(e.map(e=>r.addExample(e))),r}}var eH=r(47948);function eX(e){return Object.keys(e).sort().map(t=>e[t])}class eQ extends eK{constructor(e){if(super(e),Object.defineProperty(this,"vectorStoreRetriever",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"exampleKeys",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"inputKeys",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.exampleKeys=e.exampleKeys,this.inputKeys=e.inputKeys,void 0!==e.vectorStore)this.vectorStoreRetriever=e.vectorStore.asRetriever({k:e.k??4,filter:e.filter});else if(e.vectorStoreRetriever)this.vectorStoreRetriever=e.vectorStoreRetriever;else throw Error('You must specify one of "vectorStore" and "vectorStoreRetriever".')}async addExample(e){let t=eX((this.inputKeys??Object.keys(e)).reduce((t,r)=>({...t,[r]:e[r]}),{})).join(" ");await this.vectorStoreRetriever.addDocuments([new eH.y({pageContent:t,metadata:e})])}async selectExamples(e){let t=eX((this.inputKeys??Object.keys(e)).reduce((t,r)=>({...t,[r]:e[r]}),{})).join(" "),r=(await this.vectorStoreRetriever.invoke(t)).map(e=>e.metadata);return this.exampleKeys?r.map(e=>this.exampleKeys.reduce((t,r)=>({...t,[r]:e[r]}),{})):r}static async fromExamples(e,t,r,a={}){let i=a.inputKeys??null,s=e.map(e=>eX(i?i.reduce((t,r)=>({...t,[r]:e[r]}),{}):e).join(" "));return new eQ({vectorStore:await r.fromTexts(s,e,t,a),k:a.k??4,exampleKeys:a.exampleKeys,inputKeys:a.inputKeys})}}var eZ=r(62633),e0=r(88595),e1=r(5589);let e2=e=>e.startsWith("gpt-3.5-turbo-16k")?"gpt-3.5-turbo-16k":e.startsWith("gpt-3.5-turbo-")?"gpt-3.5-turbo":e.startsWith("gpt-4-32k")?"gpt-4-32k":e.startsWith("gpt-4-")?"gpt-4":e.startsWith("gpt-4o")?"gpt-4o":e,e5=e=>"text-embedding-ada-002"===e?8191:2046,e9=e=>{switch(e2(e)){case"gpt-3.5-turbo-16k":return 16384;case"gpt-3.5-turbo":return 4096;case"gpt-4-32k":return 32768;case"gpt-4":return 8192;case"text-davinci-003":default:return 4097;case"text-curie-001":case"text-babbage-001":case"text-ada-001":case"code-cushman-001":return 2048;case"code-davinci-002":return 8e3}};function e4(e){return"object"==typeof e&&!!e&&("type"in e&&"function"===e.type&&"function"in e&&"object"==typeof e.function&&!!e.function&&"name"in e.function&&"parameters"in e.function||!1)}let e3=async({prompt:e,modelName:t})=>{let r;try{r=(await (0,e0.encodingForModel)(e2(t))).encode(e).length}catch(t){console.warn("Failed to calculate number of tokens, falling back to approximate count"),r=Math.ceil(e.length/4)}return e9(t)-r},e8=()=>!1;class e6 extends e1.YN{get lc_attributes(){return{callbacks:void 0,verbose:void 0}}constructor(e){super(e),Object.defineProperty(this,"verbose",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"callbacks",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"metadata",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.verbose=e.verbose??e8(),this.callbacks=e.callbacks,this.tags=e.tags??[],this.metadata=e.metadata??{}}}class e7 extends e6{get callKeys(){return["stop","timeout","signal","tags","metadata","callbacks"]}constructor({callbacks:e,callbackManager:t,...r}){super({callbacks:e??t,...r}),Object.defineProperty(this,"caller",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"cache",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_encoding",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),"object"==typeof r.cache?this.cache=r.cache:r.cache?this.cache=eI.global():this.cache=void 0,this.caller=new eU.AsyncCaller(r??{})}async getNumTokens(e){if("string"!=typeof e)return 0;let t=Math.ceil(e.length/4);if(!this._encoding)try{this._encoding=await (0,e0.encodingForModel)("modelName"in this?e2(this.modelName):"gpt2")}catch(e){console.warn("Failed to calculate number of tokens, falling back to approximate count",e)}if(this._encoding)try{t=this._encoding.encode(e).length}catch(e){console.warn("Failed to calculate number of tokens, falling back to approximate count",e)}return t}static _convertInputToPromptValue(e){return"string"==typeof e?new eZ.StringPromptValue(e):Array.isArray(e)?new eZ.ChatPromptValue(e.map(eT.K0)):e}_identifyingParams(){return{}}_getSerializedCacheKeyParametersForCall({config:e,...t}){return Object.entries({...this._identifyingParams(),...t,_type:this._llmType(),_model:this._modelType()}).filter(([e,t])=>void 0!==t).map(([e,t])=>`${e}:${JSON.stringify(t)}`).sort().join(",")}serialize(){return{...this._identifyingParams(),_type:this._llmType(),_model:this._modelType()}}static async deserialize(e){throw Error("Use .toJSON() instead")}}var te=r(3576),tt=r(38022),tr=r(5304),ta=r(28506),ti=r(72180),ts=r(27935);class tn extends e1.YN{static lc_name(){return"RunnablePassthrough"}constructor(e){super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"func",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),e&&(this.func=e.func)}async invoke(e,t){let r=(0,ts.ZI)(t);return this.func&&await this.func(e,r),this._callWithConfig(e=>Promise.resolve(e),e,r)}async *transform(e,t){let r,a=(0,ts.ZI)(t),i=!0;for await(let t of this._transformStreamWithConfig(e,e=>e,a))if(yield t,i)if(void 0===r)r=t;else try{r=(0,ti.concat)(r,t)}catch{r=void 0,i=!1}this.func&&void 0!==r&&await this.func(r,a)}static assign(e){return new e1.B2(new e1.ck({steps:e}))}}function to(e){return"function"==typeof e?.parse}function tl(){let e=new TextEncoder;return new TransformStream({transform(t,r){r.enqueue(e.encode("string"==typeof t.content?t.content:JSON.stringify(t.content)))}})}class tc extends e7{constructor(e){super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain","chat_models",this._llmType()]})}_separateRunnableConfigFromCallOptionsCompat(e){let[t,r]=super._separateRunnableConfigFromCallOptions(e);return r.signal=t.signal,[t,r]}async invoke(e,t){let r=tc._convertInputToPromptValue(e);return(await this.generatePrompt([r],t,t?.callbacks)).generations[0][0].message}async *_streamResponseChunks(e,t,r){throw Error("Not implemented.")}async *_streamIterator(e,t){if(this._streamResponseChunks===tc.prototype._streamResponseChunks)yield this.invoke(e,t);else{let r,a=tc._convertInputToPromptValue(e).toChatMessages(),[i,s]=this._separateRunnableConfigFromCallOptionsCompat(t),n={...i.metadata,...this.getLsParams(s)},o=await eN.CallbackManager.configure(i.callbacks,this.callbacks,i.tags,this.tags,n,this.metadata,{verbose:this.verbose}),l={options:s,invocation_params:this?.invocationParams(s),batch_size:1},c=await o?.handleChatModelStart(this.toJSON(),[a],i.runId,void 0,l,void 0,void 0,i.runName);try{for await(let e of this._streamResponseChunks(a,s,c?.[0])){if(null==e.message.id){let t=c?.at(0)?.runId;null!=t&&e.message._updateId(`run-${t}`)}e.message.response_metadata={...e.generationInfo,...e.message.response_metadata},yield e.message,r=r?r.concat(e):e}}catch(e){throw await Promise.all((c??[]).map(t=>t?.handleLLMError(e))),e}await Promise.all((c??[]).map(e=>e?.handleLLMEnd({generations:[[r]]})))}}getLsParams(e){return{ls_model_type:"chat",ls_stop:e.stop}}async _generateUncached(e,t,r){let a=e.map(e=>e.map(eR.coerceMessageLikeToMessage)),i={...r.metadata,...this.getLsParams(t)},s=await eN.CallbackManager.configure(r.callbacks,this.callbacks,r.tags,this.tags,i,this.metadata,{verbose:this.verbose}),n={options:t,invocation_params:this?.invocationParams(t),batch_size:1},o=await s?.handleChatModelStart(this.toJSON(),a,r.runId,void 0,n,void 0,void 0,r.runName),l=[],c=[];if(o?.[0].handlers.find(e=>(0,tr.K)(e)||(0,ta.isLogStreamHandler)(e))&&1===a.length&&this._streamResponseChunks!==tc.prototype._streamResponseChunks)try{let e;for await(let r of(await this._streamResponseChunks(a[0],t,o?.[0]))){if(null==r.message.id){let e=o?.at(0)?.runId;null!=e&&r.message._updateId(`run-${e}`)}e=void 0===e?r:(0,ti.concat)(e,r)}if(void 0===e)throw Error("Received empty response from chat model call.");l.push([e]),await o?.[0].handleLLMEnd({generations:l,llmOutput:{}})}catch(e){throw await o?.[0].handleLLMError(e),e}else{let e=await Promise.allSettled(a.map((e,r)=>this._generate(e,{...t,promptIndex:r},o?.[r])));await Promise.all(e.map(async(e,t)=>{if("fulfilled"!==e.status)return await o?.[t]?.handleLLMError(e.reason),Promise.reject(e.reason);{let r=e.value;for(let e of r.generations){if(null==e.message.id){let t=o?.at(0)?.runId;null!=t&&e.message._updateId(`run-${t}`)}e.message.response_metadata={...e.generationInfo,...e.message.response_metadata}}return 1===r.generations.length&&(r.generations[0].message.response_metadata={...r.llmOutput,...r.generations[0].message.response_metadata}),l[t]=r.generations,c[t]=r.llmOutput,o?.[t]?.handleLLMEnd({generations:[r.generations],llmOutput:r.llmOutput})}}))}let u={generations:l,llmOutput:c.length?this._combineLLMOutput?.(...c):void 0};return Object.defineProperty(u,tt.RUN_KEY,{value:o?{runIds:o?.map(e=>e.runId)}:void 0,configurable:!0}),u}async _generateCached({messages:e,cache:t,llmStringKey:r,parsedOptions:a,handledOptions:i}){let s=e.map(e=>e.map(eR.coerceMessageLikeToMessage)),n={...i.metadata,...this.getLsParams(a)},o=await eN.CallbackManager.configure(i.callbacks,this.callbacks,i.tags,this.tags,n,this.metadata,{verbose:this.verbose}),l={options:a,invocation_params:this?.invocationParams(a),batch_size:1,cached:!0},c=await o?.handleChatModelStart(this.toJSON(),s,i.runId,void 0,l,void 0,void 0,i.runName),u=[],h=(await Promise.allSettled(s.map(async(e,a)=>{let i=tc._convertInputToPromptValue(e).toString(),s=await t.lookup(i,r);return null==s&&u.push(a),s}))).map((e,t)=>({result:e,runManager:c?.[t]})).filter(({result:e})=>"fulfilled"===e.status&&null!=e.value||"rejected"===e.status),p=[];await Promise.all(h.map(async({result:e,runManager:t},r)=>{if("fulfilled"!==e.status)return await t?.handleLLMError(e.reason),Promise.reject(e.reason);{let a=e.value;return p[r]=a,a.length&&await t?.handleLLMNewToken(a[0].text),t?.handleLLMEnd({generations:[a]})}}));let d={generations:p,missingPromptIndices:u};return Object.defineProperty(d,tt.RUN_KEY,{value:c?{runIds:c?.map(e=>e.runId)}:void 0,configurable:!0}),d}async generate(e,t,r){let a;a=Array.isArray(t)?{stop:t}:t;let i=e.map(e=>e.map(eR.coerceMessageLikeToMessage)),[s,n]=this._separateRunnableConfigFromCallOptionsCompat(a);if(s.callbacks=s.callbacks??r,!this.cache)return this._generateUncached(i,n,s);let{cache:o}=this,l=this._getSerializedCacheKeyParametersForCall(n),{generations:c,missingPromptIndices:u}=await this._generateCached({messages:i,cache:o,llmStringKey:l,parsedOptions:n,handledOptions:s}),h={};if(u.length>0){let e=await this._generateUncached(u.map(e=>i[e]),n,s);await Promise.all(e.generations.map(async(e,t)=>{let r=u[t];c[r]=e;let a=tc._convertInputToPromptValue(i[r]).toString();return o.update(a,l,e)})),h=e.llmOutput??{}}return{generations:c,llmOutput:h}}invocationParams(e){return{}}_modelType(){return"base_chat_model"}serialize(){return{...this.invocationParams(),_type:this._llmType(),_model:this._modelType()}}async generatePrompt(e,t,r){let a=e.map(e=>e.toChatMessages());return this.generate(a,t,r)}async call(e,t,r){return(await this.generate([e.map(eR.coerceMessageLikeToMessage)],t,r)).generations[0][0].message}async callPrompt(e,t,r){let a=e.toChatMessages();return this.call(a,t,r)}async predictMessages(e,t,r){return this.call(e,t,r)}async predict(e,t,r){let a=new eR.HumanMessage(e),i=await this.call([a],t,r);if("string"!=typeof i.content)throw Error("Cannot use predict when output is not a string.");return i.content}withStructuredOutput(e,t){let r;if("function"!=typeof this.bindTools)throw Error('Chat model must implement ".bindTools()" to use withStructuredOutput.');let a=t?.name,i=e.description??"A function available to call.",s=t?.method,n=t?.includeRaw;if("jsonMode"===s)throw Error('Base withStructuredOutput implementation only supports "functionCalling" as a method.');let o=a??"extract";to(e)?r=[{type:"function",function:{name:o,description:i,parameters:(0,te.Ik)(e)}}]:("name"in e&&(o=e.name),r=[{type:"function",function:{name:o,description:i,parameters:e}}]);let l=this.bindTools(r),c=e1.jY.from(e=>{if(!e.tool_calls||0===e.tool_calls.length)throw Error("No tool calls found in the response.");let t=e.tool_calls.find(e=>e.name===o);if(!t)throw Error(`No tool call found with name ${o}.`);return t.args});if(!n)return l.pipe(c).withConfig({runName:"StructuredOutput"});let u=tn.assign({parsed:(e,t)=>c.invoke(e.raw,t)}),h=tn.assign({parsed:()=>null}),p=u.withFallbacks({fallbacks:[h]});return e1.zZ.from([{raw:l},p]).withConfig({runName:"StructuredOutputRunnable"})}}class tu extends tc{async _generate(e,t,r){let a=await this._call(e,t,r),i=new eR.AIMessage(a);if("string"!=typeof i.content)throw Error("Cannot generate with a simple chat model when output is not a string.");return{generations:[{text:i.content,message:i}]}}}class th extends e7{constructor({concurrency:e,...t}){super(e?{maxConcurrency:e,...t}:t),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain","llms",this._llmType()]})}async invoke(e,t){let r=th._convertInputToPromptValue(e);return(await this.generatePrompt([r],t,t?.callbacks)).generations[0][0].text}async *_streamResponseChunks(e,t,r){throw Error("Not implemented.")}_separateRunnableConfigFromCallOptionsCompat(e){let[t,r]=super._separateRunnableConfigFromCallOptions(e);return r.signal=t.signal,[t,r]}async *_streamIterator(e,t){if(this._streamResponseChunks===th.prototype._streamResponseChunks)yield this.invoke(e,t);else{let r=th._convertInputToPromptValue(e),[a,i]=this._separateRunnableConfigFromCallOptionsCompat(t),s=await eN.CallbackManager.configure(a.callbacks,this.callbacks,a.tags,this.tags,a.metadata,this.metadata,{verbose:this.verbose}),n={options:i,invocation_params:this?.invocationParams(i),batch_size:1},o=await s?.handleLLMStart(this.toJSON(),[r.toString()],a.runId,void 0,n,void 0,void 0,a.runName),l=new tt.GenerationChunk({text:""});try{for await(let e of this._streamResponseChunks(r.toString(),i,o?.[0]))l=l?l.concat(e):e,"string"==typeof e.text&&(yield e.text)}catch(e){throw await Promise.all((o??[]).map(t=>t?.handleLLMError(e))),e}await Promise.all((o??[]).map(e=>e?.handleLLMEnd({generations:[[l]]})))}}async generatePrompt(e,t,r){let a=e.map(e=>e.toString());return this.generate(a,t,r)}invocationParams(e){return{}}_flattenLLMResult(e){let t=[];for(let r=0;r<e.generations.length;r+=1){let a=e.generations[r];if(0===r)t.push({generations:[a],llmOutput:e.llmOutput});else{let r=e.llmOutput?{...e.llmOutput,tokenUsage:{}}:void 0;t.push({generations:[a],llmOutput:r})}}return t}async _generateUncached(e,t,r){let a,i=await eN.CallbackManager.configure(r.callbacks,this.callbacks,r.tags,this.tags,r.metadata,this.metadata,{verbose:this.verbose}),s={options:t,invocation_params:this?.invocationParams(t),batch_size:e.length},n=await i?.handleLLMStart(this.toJSON(),e,r.runId,void 0,s,void 0,void 0,r?.runName);if(n?.[0].handlers.find(e=>(0,tr.K)(e)||(0,ta.isLogStreamHandler)(e))&&1===e.length&&this._streamResponseChunks!==th.prototype._streamResponseChunks)try{let r;for await(let a of(await this._streamResponseChunks(e[0],t,n?.[0])))r=void 0===r?a:(0,ti.concat)(r,a);if(void 0===r)throw Error("Received empty response from chat model call.");a={generations:[[r]],llmOutput:{}},await n?.[0].handleLLMEnd(a)}catch(e){throw await n?.[0].handleLLMError(e),e}else{try{a=await this._generate(e,t,n?.[0])}catch(e){throw await Promise.all((n??[]).map(t=>t?.handleLLMError(e))),e}let r=this._flattenLLMResult(a);await Promise.all((n??[]).map((e,t)=>e?.handleLLMEnd(r[t])))}let o=n?.map(e=>e.runId)||void 0;return Object.defineProperty(a,tt.RUN_KEY,{value:o?{runIds:o}:void 0,configurable:!0}),a}async _generateCached({prompts:e,cache:t,llmStringKey:r,parsedOptions:a,handledOptions:i,runId:s}){let n=await eN.CallbackManager.configure(i.callbacks,this.callbacks,i.tags,this.tags,i.metadata,this.metadata,{verbose:this.verbose}),o={options:a,invocation_params:this?.invocationParams(a),batch_size:e.length,cached:!0},l=await n?.handleLLMStart(this.toJSON(),e,s,void 0,o,void 0,void 0,i?.runName),c=[],u=(await Promise.allSettled(e.map(async(e,a)=>{let i=await t.lookup(e,r);return null==i&&c.push(a),i}))).map((e,t)=>({result:e,runManager:l?.[t]})).filter(({result:e})=>"fulfilled"===e.status&&null!=e.value||"rejected"===e.status),h=[];await Promise.all(u.map(async({result:e,runManager:t},r)=>{if("fulfilled"!==e.status)return await t?.handleLLMError(e.reason),Promise.reject(e.reason);{let a=e.value;return h[r]=a,a.length&&await t?.handleLLMNewToken(a[0].text),t?.handleLLMEnd({generations:[a]})}}));let p={generations:h,missingPromptIndices:c};return Object.defineProperty(p,tt.RUN_KEY,{value:l?{runIds:l?.map(e=>e.runId)}:void 0,configurable:!0}),p}async generate(e,t,r){let a;if(!Array.isArray(e))throw Error("Argument 'prompts' is expected to be a string[]");a=Array.isArray(t)?{stop:t}:t;let[i,s]=this._separateRunnableConfigFromCallOptionsCompat(a);if(i.callbacks=i.callbacks??r,!this.cache)return this._generateUncached(e,s,i);let{cache:n}=this,o=this._getSerializedCacheKeyParametersForCall(s),{generations:l,missingPromptIndices:c}=await this._generateCached({prompts:e,cache:n,llmStringKey:o,parsedOptions:s,handledOptions:i,runId:i.runId}),u={};if(c.length>0){let t=await this._generateUncached(c.map(t=>e[t]),s,i);await Promise.all(t.generations.map(async(t,r)=>{let a=c[r];return l[a]=t,n.update(e[a],o,t)})),u=t.llmOutput??{}}return{generations:l,llmOutput:u}}async call(e,t,r){let{generations:a}=await this.generate([e],t,r);return a[0][0].text}async predict(e,t,r){return this.call(e,t,r)}async predictMessages(e,t,r){let a=(0,eR.getBufferString)(e),i=await this.call(a,t,r);return new eR.AIMessage(i)}_identifyingParams(){return{}}serialize(){return{...this._identifyingParams(),_type:this._llmType(),_model:this._modelType()}}_modelType(){return"base_llm"}}class tp extends th{async _generate(e,t,r){return{generations:await Promise.all(e.map((e,a)=>this._call(e,{...t,promptIndex:a},r).then(e=>[{text:e}])))}}}class td{}let tf=(e,t)=>{if(void 0!==t)return e[t];let r=Object.keys(e);if(1===r.length)return e[r[0]]},tm=(e,t)=>{let r=tf(e,t);if(!r){let t=Object.keys(e);throw Error(`input values have ${t.length} keys, you must specify an input key or pass only 1 key as input`)}return r},tg=(e,t)=>{let r=tf(e,t);if(!r){let t=Object.keys(e);throw Error(`output values have ${t.length} keys, you must specify an output key or pass only 1 key as output`)}return r};function tb(e,t){let r=Object.keys(e).filter(e=>!t.includes(e)&&"stop"!==e);if(1!==r.length)throw Error(`One input key expected, but got ${r.length}`);return r[0]}class ty extends e1.YN{static lc_name(){return"RouterRunnable"}constructor(e){super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"runnables",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.runnables=e.runnables}async invoke(e,t){let{key:r,input:a}=e,i=this.runnables[r];if(void 0===i)throw Error(`No runnable associated with key "${r}".`);return i.invoke(a,(0,ts.ZI)(t))}async batch(e,t,r){let a=e.map(e=>e.key),i=e.map(e=>e.input);if(void 0!==a.find(e=>void 0===this.runnables[e]))throw Error("One or more keys do not have a corresponding runnable.");let s=a.map(e=>this.runnables[e]),n=this._getOptionsList(t??{},e.length),o=n[0]?.maxConcurrency??r?.maxConcurrency,l=o&&o>0?o:e.length,c=[];for(let e=0;e<i.length;e+=l){let t=i.slice(e,e+l).map((e,t)=>s[t].invoke(e,n[t])),r=await Promise.all(t);c.push(r)}return c.flat()}async stream(e,t){let{key:r,input:a}=e,i=this.runnables[r];if(void 0===i)throw Error(`No runnable associated with key "${r}".`);return i.stream(a,t)}}class t_ extends e1.YN{static lc_name(){return"RunnableBranch"}constructor(e){super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"default",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"branches",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.branches=e.branches,this.default=e.default}static from(e){if(e.length<1)throw Error("RunnableBranch requires at least one branch");return new this({branches:e.slice(0,-1).map(([e,t])=>[(0,e1.Bp)(e),(0,e1.Bp)(t)]),default:(0,e1.Bp)(e[e.length-1])})}async _invoke(e,t,r){let a;for(let i=0;i<this.branches.length;i+=1){let[s,n]=this.branches[i];if(await s.invoke(e,(0,ts.tn)(t,{callbacks:r?.getChild(`condition:${i+1}`)}))){a=await n.invoke(e,(0,ts.tn)(t,{callbacks:r?.getChild(`branch:${i+1}`)}));break}}return a||(a=await this.default.invoke(e,(0,ts.tn)(t,{callbacks:r?.getChild("branch:default")}))),a}async invoke(e,t={}){return this._callWithConfig(this._invoke,e,t)}async *_streamIterator(e,t){let r,a,i=await (0,ts.kJ)(t),s=await i?.handleChainStart(this.toJSON(),(0,e1.GH)(e,"input"),t?.runId,void 0,void 0,void 0,t?.runName),n=!0;try{for(let i=0;i<this.branches.length;i+=1){let[o,l]=this.branches[i];if(await o.invoke(e,(0,ts.tn)(t,{callbacks:s?.getChild(`condition:${i+1}`)}))){for await(let o of a=await l.stream(e,(0,ts.tn)(t,{callbacks:s?.getChild(`branch:${i+1}`)})))if(yield o,n)if(void 0===r)r=o;else try{r=(0,ti.concat)(r,o)}catch(e){r=void 0,n=!1}break}}if(void 0===a){for await(let i of a=await this.default.stream(e,(0,ts.tn)(t,{callbacks:s?.getChild("branch:default")})))if(yield i,n)if(void 0===r)r=i;else try{r=(0,ti.concat)(r,i)}catch(e){r=void 0,n=!1}}}catch(e){throw await s?.handleChainError(e),e}await s?.handleChainEnd(r??{})}}class tv extends e1.fJ{constructor(e){let t=e1.jY.from((e,t)=>this._enterHistory(e,t??{})).withConfig({runName:"loadHistory"}),r=e.historyMessagesKey??e.inputMessagesKey;r&&(t=tn.assign({[r]:t}).withConfig({runName:"insertHistory"})),super({...e,config:e.config??{},bound:t.pipe(e.runnable.withListeners({onEnd:(e,t)=>this._exitHistory(e,t??{})})).withConfig({runName:"RunnableWithMessageHistory"})}),Object.defineProperty(this,"runnable",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"inputMessagesKey",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"outputMessagesKey",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"historyMessagesKey",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"getMessageHistory",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.runnable=e.runnable,this.getMessageHistory=e.getMessageHistory,this.inputMessagesKey=e.inputMessagesKey,this.outputMessagesKey=e.outputMessagesKey,this.historyMessagesKey=e.historyMessagesKey}_getInputMessages(e){let t;if("object"!=typeof e||Array.isArray(e)||(0,eR.isBaseMessage)(e))t=e;else{let r;r=this.inputMessagesKey?this.inputMessagesKey:1===Object.keys(e).length?Object.keys(e)[0]:"input",t=Array.isArray(e[r])&&Array.isArray(e[r][0])?e[r][0]:e[r]}if("string"==typeof t)return[new eR.HumanMessage(t)];if(Array.isArray(t))return t;if((0,eR.isBaseMessage)(t))return[t];throw Error(`Expected a string, BaseMessage, or array of BaseMessages.
Got ${JSON.stringify(t,null,2)}`)}_getOutputMessages(e){let t;if(Array.isArray(e)||(0,eR.isBaseMessage)(e)||"string"==typeof e)t=e;else{let r;r=void 0!==this.outputMessagesKey?this.outputMessagesKey:1===Object.keys(e).length?Object.keys(e)[0]:"output",t=void 0!==e.generations?e.generations[0][0].message:e[r]}if("string"==typeof t)return[new eR.AIMessage(t)];if(Array.isArray(t))return t;if((0,eR.isBaseMessage)(t))return[t];throw Error(`Expected a string, BaseMessage, or array of BaseMessages. Received: ${JSON.stringify(t,null,2)}`)}async _enterHistory(e,t){let r=t?.configurable?.messageHistory,a=await r.getMessages();return void 0===this.historyMessagesKey?a.concat(this._getInputMessages(e)):a}async _exitHistory(e,t){let r,a=t.configurable?.messageHistory;r=Array.isArray(e.inputs)&&Array.isArray(e.inputs[0])?e.inputs[0]:e.inputs;let i=this._getInputMessages(r);if(void 0===this.historyMessagesKey){let e=await a.getMessages();i=i.slice(e.length)}let s=e.outputs;if(!s)throw Error(`Output values from 'Run' undefined. Run: ${JSON.stringify(e,null,2)}`);let n=this._getOutputMessages(s);await a.addMessages([...i,...n])}async _mergeConfig(...e){let t=await super._mergeConfig(...e);if(!t.configurable||!t.configurable.sessionId){let e={[this.inputMessagesKey??"input"]:"foo"};throw Error(`sessionId is required. Pass it in as part of the config argument to .invoke() or .stream()
eg. chain.invoke(${JSON.stringify(e)}, ${JSON.stringify({configurable:{sessionId:"123"}})})`)}let{sessionId:r}=t.configurable;return t.configurable.messageHistory=await this.getMessageHistory(r),t}}class tw extends e1.YN{parseResultWithPrompt(e,t,r){return this.parseResult(e,r)}_baseMessageToString(e){return"string"==typeof e.content?e.content:this._baseMessageContentToString(e.content)}_baseMessageContentToString(e){return JSON.stringify(e)}async invoke(e,t){return"string"==typeof e?this._callWithConfig(async(e,t)=>this.parseResult([{text:e}],t?.callbacks),e,{...t,runType:"parser"}):this._callWithConfig(async(e,t)=>this.parseResult([{message:e,text:this._baseMessageToString(e)}],t?.callbacks),e,{...t,runType:"parser"})}}class tk extends tw{parseResult(e,t){return this.parse(e[0].text,t)}async parseWithPrompt(e,t,r){return this.parse(e,r)}_type(){throw Error("_type not implemented")}}class tx extends Error{constructor(e,t,r,a=!1){if(super(e),Object.defineProperty(this,"llmOutput",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"observation",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"sendToLLM",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.llmOutput=t,this.observation=r,this.sendToLLM=a,a&&(void 0===r||void 0===t))throw Error("Arguments 'observation' & 'llmOutput' are required if 'sendToLlm' is true")}}var tO=r(72892);function tP(e,t){let r=typeof e;if(r!==typeof t)return!1;if(Array.isArray(e)){if(!Array.isArray(t))return!1;let r=e.length;if(r!==t.length)return!1;for(let a=0;a<r;a++)if(!tP(e[a],t[a]))return!1;return!0}if("object"===r){if(!e||!t)return e===t;let r=Object.keys(e),a=Object.keys(t);if(r.length!==a.length)return!1;for(let a of r)if(!tP(e[a],t[a]))return!1;return!0}return e===t}function tT(e){return encodeURI(e.replace(/~/g,"~0").replace(/\//g,"~1"))}let tE={prefixItems:!0,items:!0,allOf:!0,anyOf:!0,oneOf:!0},tS={$defs:!0,definitions:!0,properties:!0,patternProperties:!0,dependentSchemas:!0},tC={id:!0,$id:!0,$ref:!0,$schema:!0,$anchor:!0,$vocabulary:!0,$comment:!0,default:!0,enum:!0,const:!0,required:!0,type:!0,maximum:!0,minimum:!0,exclusiveMaximum:!0,exclusiveMinimum:!0,multipleOf:!0,maxLength:!0,minLength:!0,pattern:!0,format:!0,maxItems:!0,minItems:!0,uniqueItems:!0,maxProperties:!0,minProperties:!0},tj="undefined"!=typeof self&&self.location&&"null"!==self.location.origin?new URL(self.location.origin+self.location.pathname+location.search):new URL("https://github.com/cfworker");function tA(e,t=Object.create(null),r=tj,a=""){if(e&&"object"==typeof e&&!Array.isArray(e)){let i=e.$id||e.id;if(i){let s=new URL(i,r.href);s.hash.length>1?t[s.href]=e:(s.hash="",""===a?r=s:tA(e,t,r))}}else if(!0!==e&&!1!==e)return t;let i=r.href+(a?"#"+a:"");if(void 0!==t[i])throw Error(`Duplicate schema URI "${i}".`);if(t[i]=e,!0===e||!1===e)return t;if(void 0===e.__absolute_uri__&&Object.defineProperty(e,"__absolute_uri__",{enumerable:!1,value:i}),e.$ref&&void 0===e.__absolute_ref__){let t=new URL(e.$ref,r.href);t.hash=t.hash,Object.defineProperty(e,"__absolute_ref__",{enumerable:!1,value:t.href})}if(e.$recursiveRef&&void 0===e.__absolute_recursive_ref__){let t=new URL(e.$recursiveRef,r.href);t.hash=t.hash,Object.defineProperty(e,"__absolute_recursive_ref__",{enumerable:!1,value:t.href})}for(let i in e.$anchor&&(t[new URL("#"+e.$anchor,r.href).href]=e),e){if(tC[i])continue;let s=`${a}/${tT(i)}`,n=e[i];if(Array.isArray(n)){if(tE[i]){let e=n.length;for(let a=0;a<e;a++)tA(n[a],t,r,`${s}/${a}`)}}else if(tS[i])for(let e in n)tA(n[e],t,r,`${s}/${tT(e)}`);else tA(n,t,r,s)}return t}let tI=/^(\d\d\d\d)-(\d\d)-(\d\d)$/,t$=[0,31,28,31,30,31,30,31,31,30,31,30,31],tN=/^(\d\d):(\d\d):(\d\d)(\.\d+)?(z|[+-]\d\d(?::?\d\d)?)?$/i;function tM(e){return e.test.bind(e)}let tR={...{date:tL,time:tD.bind(void 0,!1),"date-time":function(e){let t=e.split(tF);return 2==t.length&&tL(t[0])&&tD(!0,t[1])},duration:e=>e.length>1&&e.length<80&&(/^P\d+([.,]\d+)?W$/.test(e)||/^P[\dYMDTHS]*(\d[.,]\d+)?[YMDHS]$/.test(e)&&/^P([.,\d]+Y)?([.,\d]+M)?([.,\d]+D)?(T([.,\d]+H)?([.,\d]+M)?([.,\d]+S)?)?$/.test(e)),uri:function(e){return tz.test(e)&&tU.test(e)},"uri-reference":tM(/^(?:[a-z][a-z0-9+\-.]*:)?(?:\/?\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\.[a-z0-9\-._~!$&'()*+,;=:]+)\]|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)|(?:[a-z0-9\-._~!$&'"()*+,;=]|%[0-9a-f]{2})*)(?::\d*)?(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*|\/(?:(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?(?:\?(?:[a-z0-9\-._~!$&'"()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\-._~!$&'"()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i),"uri-template":tM(/^(?:(?:[^\x00-\x20"'<>%\\^`{|}]|%[0-9a-f]{2})|\{[+#./;?&=,!@|]?(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\*)?(?:,(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\*)?)*\})*$/i),url:tM(/^(?:(?:https?|ftp):\/\/)(?:\S+(?::\S*)?@)?(?:(?!10(?:\.\d{1,3}){3})(?!127(?:\.\d{1,3}){3})(?!169\.254(?:\.\d{1,3}){2})(?!192\.168(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z\u{00a1}-\u{ffff}0-9]+-?)*[a-z\u{00a1}-\u{ffff}0-9]+)(?:\.(?:[a-z\u{00a1}-\u{ffff}0-9]+-?)*[a-z\u{00a1}-\u{ffff}0-9]+)*(?:\.(?:[a-z\u{00a1}-\u{ffff}]{2,})))(?::\d{2,5})?(?:\/[^\s]*)?$/iu),email:e=>{if('"'===e[0])return!1;let[t,r,...a]=e.split("@");return!(!t||!r||0!==a.length||t.length>64||r.length>253||"."===t[0]||t.endsWith(".")||t.includes(".."))&&!!/^[a-z0-9.-]+$/i.test(r)&&!!/^[a-z0-9.!#$%&'*+/=?^_`{|}~-]+$/i.test(t)&&r.split(".").every(e=>/^[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?$/i.test(e))},hostname:tM(/^(?=.{1,253}\.?$)[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[-0-9a-z]{0,61}[0-9a-z])?)*\.?$/i),ipv4:tM(/^(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)$/),ipv6:tM(/^((([0-9a-f]{1,4}:){7}([0-9a-f]{1,4}|:))|(([0-9a-f]{1,4}:){6}(:[0-9a-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9a-f]{1,4}:){5}(((:[0-9a-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9a-f]{1,4}:){4}(((:[0-9a-f]{1,4}){1,3})|((:[0-9a-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){3}(((:[0-9a-f]{1,4}){1,4})|((:[0-9a-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){2}(((:[0-9a-f]{1,4}){1,5})|((:[0-9a-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9a-f]{1,4}:){1}(((:[0-9a-f]{1,4}){1,6})|((:[0-9a-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9a-f]{1,4}){1,7})|((:[0-9a-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))$/i),regex:function(e){if(tV.test(e))return!1;try{return new RegExp(e),!0}catch(e){return!1}},uuid:tM(/^(?:urn:uuid:)?[0-9a-f]{8}-(?:[0-9a-f]{4}-){3}[0-9a-f]{12}$/i),"json-pointer":tM(/^(?:\/(?:[^~/]|~0|~1)*)*$/),"json-pointer-uri-fragment":tM(/^#(?:\/(?:[a-z0-9_\-.!$&'()*+,;:=@]|%[0-9a-f]{2}|~0|~1)*)*$/i),"relative-json-pointer":tM(/^(?:0|[1-9][0-9]*)(?:#|(?:\/(?:[^~/]|~0|~1)*)*)$/)},date:tM(/^\d\d\d\d-[0-1]\d-[0-3]\d$/),time:tM(/^(?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)(?:\.\d+)?(?:z|[+-]\d\d(?::?\d\d)?)?$/i),"date-time":tM(/^\d\d\d\d-[0-1]\d-[0-3]\d[t\s](?:[0-2]\d:[0-5]\d:[0-5]\d|23:59:60)(?:\.\d+)?(?:z|[+-]\d\d(?::?\d\d)?)$/i),"uri-reference":tM(/^(?:(?:[a-z][a-z0-9+-.]*:)?\/?\/)?(?:[^\\\s#][^\s#]*)?(?:#[^\\\s]*)?$/i)};function tL(e){var t;let r=e.match(tI);if(!r)return!1;let a=+r[1],i=+r[2],s=+r[3];return i>=1&&i<=12&&s>=1&&s<=(2==i&&(t=a)%4==0&&(t%100!=0||t%400==0)?29:t$[i])}function tD(e,t){let r=t.match(tN);if(!r)return!1;let a=+r[1],i=+r[2],s=+r[3],n=!!r[5];return(a<=23&&i<=59&&s<=59||23==a&&59==i&&60==s)&&(!e||n)}let tF=/t|\s/i,tz=/\/|:/,tU=/^(?:[a-z][a-z0-9+\-.]*:)(?:\/?\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\.[a-z0-9\-._~!$&'()*+,;=:]+)\]|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)|(?:[a-z0-9\-._~!$&'()*+,;=]|%[0-9a-f]{2})*)(?::\d*)?(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*|\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)(?:\?(?:[a-z0-9\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i,tV=/[^\\]\\Z/;class tK{constructor(e,t="2019-09",r=!0){Object.defineProperty(this,"schema",{enumerable:!0,configurable:!0,writable:!0,value:e}),Object.defineProperty(this,"draft",{enumerable:!0,configurable:!0,writable:!0,value:t}),Object.defineProperty(this,"shortCircuit",{enumerable:!0,configurable:!0,writable:!0,value:r}),Object.defineProperty(this,"lookup",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.lookup=tA(e)}validate(e){return function e(t,r,a="2019-09",i=tA(r),s=!0,n=null,o="#",l="#",c=Object.create(null)){let u;if(!0===r)return{valid:!0,errors:[]};if(!1===r)return{valid:!1,errors:[{instanceLocation:o,keyword:"false",keywordLocation:o,error:"False boolean schema."}]};let h=typeof t;switch(h){case"boolean":case"number":case"string":u=h;break;case"object":u=null===t?"null":Array.isArray(t)?"array":"object";break;default:throw Error(`Instances of "${h}" type are not supported.`)}let{$ref:p,$recursiveRef:d,$recursiveAnchor:f,type:m,const:g,enum:b,required:y,not:_,anyOf:v,allOf:w,oneOf:k,if:x,then:O,else:P,format:T,properties:E,patternProperties:S,additionalProperties:C,unevaluatedProperties:j,minProperties:A,maxProperties:I,propertyNames:$,dependentRequired:N,dependentSchemas:M,dependencies:R,prefixItems:L,items:D,additionalItems:F,unevaluatedItems:z,contains:U,minContains:V,maxContains:K,minItems:q,maxItems:B,uniqueItems:Y,minimum:G,maximum:W,exclusiveMinimum:J,exclusiveMaximum:H,multipleOf:X,minLength:Q,maxLength:Z,pattern:ee,__absolute_ref__:et,__absolute_recursive_ref__:er}=r,ea=[];if(!0===f&&null===n&&(n=r),"#"===d){let u=null===n?i[er]:n,h=`${l}/$recursiveRef`,p=e(t,null===n?r:n,a,i,s,u,o,h,c);p.valid||ea.push({instanceLocation:o,keyword:"$recursiveRef",keywordLocation:h,error:"A subschema had errors."},...p.errors)}if(void 0!==p){let r=i[et||p];if(void 0===r){let e=`Unresolved $ref "${p}".`;throw et&&et!==p&&(e+=`  Absolute URI "${et}".`),Error(e+=`
Known schemas:
- ${Object.keys(i).join("\n- ")}`)}let u=`${l}/$ref`,h=e(t,r,a,i,s,n,o,u,c);if(h.valid||ea.push({instanceLocation:o,keyword:"$ref",keywordLocation:u,error:"A subschema had errors."},...h.errors),"4"===a||"7"===a)return{valid:0===ea.length,errors:ea}}if(Array.isArray(m)){let e=m.length,r=!1;for(let a=0;a<e;a++)if(u===m[a]||"integer"===m[a]&&"number"===u&&t%1==0&&t==t){r=!0;break}r||ea.push({instanceLocation:o,keyword:"type",keywordLocation:`${l}/type`,error:`Instance type "${u}" is invalid. Expected "${m.join('", "')}".`})}else"integer"===m?("number"!==u||t%1||t!=t)&&ea.push({instanceLocation:o,keyword:"type",keywordLocation:`${l}/type`,error:`Instance type "${u}" is invalid. Expected "${m}".`}):void 0!==m&&u!==m&&ea.push({instanceLocation:o,keyword:"type",keywordLocation:`${l}/type`,error:`Instance type "${u}" is invalid. Expected "${m}".`});if(void 0!==g&&("object"===u||"array"===u?tP(t,g)||ea.push({instanceLocation:o,keyword:"const",keywordLocation:`${l}/const`,error:`Instance does not match ${JSON.stringify(g)}.`}):t!==g&&ea.push({instanceLocation:o,keyword:"const",keywordLocation:`${l}/const`,error:`Instance does not match ${JSON.stringify(g)}.`})),void 0!==b&&("object"===u||"array"===u?b.some(e=>tP(t,e))||ea.push({instanceLocation:o,keyword:"enum",keywordLocation:`${l}/enum`,error:`Instance does not match any of ${JSON.stringify(b)}.`}):b.some(e=>t===e)||ea.push({instanceLocation:o,keyword:"enum",keywordLocation:`${l}/enum`,error:`Instance does not match any of ${JSON.stringify(b)}.`})),void 0!==_){let r=`${l}/not`;e(t,_,a,i,s,n,o,r).valid&&ea.push({instanceLocation:o,keyword:"not",keywordLocation:r,error:'Instance matched "not" schema.'})}let ei=[];if(void 0!==v){let r=`${l}/anyOf`,u=ea.length,h=!1;for(let l=0;l<v.length;l++){let u=v[l],p=Object.create(c),d=e(t,u,a,i,s,!0===f?n:null,o,`${r}/${l}`,p);ea.push(...d.errors),h=h||d.valid,d.valid&&ei.push(p)}h?ea.length=u:ea.splice(u,0,{instanceLocation:o,keyword:"anyOf",keywordLocation:r,error:"Instance does not match any subschemas."})}if(void 0!==w){let r=`${l}/allOf`,u=ea.length,h=!0;for(let l=0;l<w.length;l++){let u=w[l],p=Object.create(c),d=e(t,u,a,i,s,!0===f?n:null,o,`${r}/${l}`,p);ea.push(...d.errors),h=h&&d.valid,d.valid&&ei.push(p)}h?ea.length=u:ea.splice(u,0,{instanceLocation:o,keyword:"allOf",keywordLocation:r,error:"Instance does not match every subschema."})}if(void 0!==k){let r=`${l}/oneOf`,u=ea.length,h=k.filter((l,u)=>{let h=Object.create(c),p=e(t,l,a,i,s,!0===f?n:null,o,`${r}/${u}`,h);return ea.push(...p.errors),p.valid&&ei.push(h),p.valid}).length;1===h?ea.length=u:ea.splice(u,0,{instanceLocation:o,keyword:"oneOf",keywordLocation:r,error:`Instance does not match exactly one subschema (${h} matches).`})}if(("object"===u||"array"===u)&&Object.assign(c,...ei),void 0!==x){let r=`${l}/if`;if(e(t,x,a,i,s,n,o,r,c).valid){if(void 0!==O){let u=e(t,O,a,i,s,n,o,`${l}/then`,c);u.valid||ea.push({instanceLocation:o,keyword:"if",keywordLocation:r,error:'Instance does not match "then" schema.'},...u.errors)}}else if(void 0!==P){let u=e(t,P,a,i,s,n,o,`${l}/else`,c);u.valid||ea.push({instanceLocation:o,keyword:"if",keywordLocation:r,error:'Instance does not match "else" schema.'},...u.errors)}}if("object"===u){if(void 0!==y)for(let e of y)e in t||ea.push({instanceLocation:o,keyword:"required",keywordLocation:`${l}/required`,error:`Instance does not have required property "${e}".`});let r=Object.keys(t);if(void 0!==A&&r.length<A&&ea.push({instanceLocation:o,keyword:"minProperties",keywordLocation:`${l}/minProperties`,error:`Instance does not have at least ${A} properties.`}),void 0!==I&&r.length>I&&ea.push({instanceLocation:o,keyword:"maxProperties",keywordLocation:`${l}/maxProperties`,error:`Instance does not have at least ${I} properties.`}),void 0!==$){let r=`${l}/propertyNames`;for(let l in t){let t=`${o}/${tT(l)}`,c=e(l,$,a,i,s,n,t,r);c.valid||ea.push({instanceLocation:o,keyword:"propertyNames",keywordLocation:r,error:`Property name "${l}" does not match schema.`},...c.errors)}}if(void 0!==N){let e=`${l}/dependantRequired`;for(let r in N)if(r in t)for(let a of N[r])a in t||ea.push({instanceLocation:o,keyword:"dependentRequired",keywordLocation:e,error:`Instance has "${r}" but does not have "${a}".`})}if(void 0!==M)for(let r in M){let u=`${l}/dependentSchemas`;if(r in t){let l=e(t,M[r],a,i,s,n,o,`${u}/${tT(r)}`,c);l.valid||ea.push({instanceLocation:o,keyword:"dependentSchemas",keywordLocation:u,error:`Instance has "${r}" but does not match dependant schema.`},...l.errors)}}if(void 0!==R){let r=`${l}/dependencies`;for(let l in R)if(l in t){let c=R[l];if(Array.isArray(c))for(let e of c)e in t||ea.push({instanceLocation:o,keyword:"dependencies",keywordLocation:r,error:`Instance has "${l}" but does not have "${e}".`});else{let u=e(t,c,a,i,s,n,o,`${r}/${tT(l)}`);u.valid||ea.push({instanceLocation:o,keyword:"dependencies",keywordLocation:r,error:`Instance has "${l}" but does not match dependant schema.`},...u.errors)}}}let u=Object.create(null),h=!1;if(void 0!==E){let r=`${l}/properties`;for(let l in E){if(!(l in t))continue;let p=`${o}/${tT(l)}`,d=e(t[l],E[l],a,i,s,n,p,`${r}/${tT(l)}`);if(d.valid)c[l]=u[l]=!0;else if(h=s,ea.push({instanceLocation:o,keyword:"properties",keywordLocation:r,error:`Property "${l}" does not match schema.`},...d.errors),h)break}}if(!h&&void 0!==S){let r=`${l}/patternProperties`;for(let l in S){let p=new RegExp(l),d=S[l];for(let f in t){if(!p.test(f))continue;let m=`${o}/${tT(f)}`,g=e(t[f],d,a,i,s,n,m,`${r}/${tT(l)}`);g.valid?c[f]=u[f]=!0:(h=s,ea.push({instanceLocation:o,keyword:"patternProperties",keywordLocation:r,error:`Property "${f}" matches pattern "${l}" but does not match associated schema.`},...g.errors))}}}if(h||void 0===C){if(!h&&void 0!==j){let r=`${l}/unevaluatedProperties`;for(let l in t)if(!c[l]){let u=`${o}/${tT(l)}`,h=e(t[l],j,a,i,s,n,u,r);h.valid?c[l]=!0:ea.push({instanceLocation:o,keyword:"unevaluatedProperties",keywordLocation:r,error:`Property "${l}" does not match unevaluated properties schema.`},...h.errors)}}}else{let r=`${l}/additionalProperties`;for(let l in t){if(u[l])continue;let p=`${o}/${tT(l)}`,d=e(t[l],C,a,i,s,n,p,r);d.valid?c[l]=!0:(h=s,ea.push({instanceLocation:o,keyword:"additionalProperties",keywordLocation:r,error:`Property "${l}" does not match additional properties schema.`},...d.errors))}}}else if("array"===u){void 0!==B&&t.length>B&&ea.push({instanceLocation:o,keyword:"maxItems",keywordLocation:`${l}/maxItems`,error:`Array has too many items (${t.length} > ${B}).`}),void 0!==q&&t.length<q&&ea.push({instanceLocation:o,keyword:"minItems",keywordLocation:`${l}/minItems`,error:`Array has too few items (${t.length} < ${q}).`});let r=t.length,u=0,h=!1;if(void 0!==L){let p=`${l}/prefixItems`,d=Math.min(L.length,r);for(;u<d;u++){let r=e(t[u],L[u],a,i,s,n,`${o}/${u}`,`${p}/${u}`);if(c[u]=!0,!r.valid&&(h=s,ea.push({instanceLocation:o,keyword:"prefixItems",keywordLocation:p,error:"Items did not match schema."},...r.errors),h))break}}if(void 0!==D){let p=`${l}/items`;if(Array.isArray(D)){let l=Math.min(D.length,r);for(;u<l;u++){let r=e(t[u],D[u],a,i,s,n,`${o}/${u}`,`${p}/${u}`);if(c[u]=!0,!r.valid&&(h=s,ea.push({instanceLocation:o,keyword:"items",keywordLocation:p,error:"Items did not match schema."},...r.errors),h))break}}else for(;u<r;u++){let r=e(t[u],D,a,i,s,n,`${o}/${u}`,p);if(c[u]=!0,!r.valid&&(h=s,ea.push({instanceLocation:o,keyword:"items",keywordLocation:p,error:"Items did not match schema."},...r.errors),h))break}if(!h&&void 0!==F){let p=`${l}/additionalItems`;for(;u<r;u++){let r=e(t[u],F,a,i,s,n,`${o}/${u}`,p);c[u]=!0,r.valid||(h=s,ea.push({instanceLocation:o,keyword:"additionalItems",keywordLocation:p,error:"Items did not match additional items schema."},...r.errors))}}}if(void 0!==U)if(0===r&&void 0===V)ea.push({instanceLocation:o,keyword:"contains",keywordLocation:`${l}/contains`,error:"Array is empty. It must contain at least one item matching the schema."});else if(void 0!==V&&r<V)ea.push({instanceLocation:o,keyword:"minContains",keywordLocation:`${l}/minContains`,error:`Array has less items (${r}) than minContains (${V}).`});else{let u=`${l}/contains`,h=ea.length,p=0;for(let l=0;l<r;l++){let r=e(t[l],U,a,i,s,n,`${o}/${l}`,u);r.valid?(c[l]=!0,p++):ea.push(...r.errors)}p>=(V||0)&&(ea.length=h),void 0===V&&void 0===K&&0===p?ea.splice(h,0,{instanceLocation:o,keyword:"contains",keywordLocation:u,error:"Array does not contain item matching schema."}):void 0!==V&&p<V?ea.push({instanceLocation:o,keyword:"minContains",keywordLocation:`${l}/minContains`,error:`Array must contain at least ${V} items matching schema. Only ${p} items were found.`}):void 0!==K&&p>K&&ea.push({instanceLocation:o,keyword:"maxContains",keywordLocation:`${l}/maxContains`,error:`Array may contain at most ${K} items matching schema. ${p} items were found.`})}if(!h&&void 0!==z){let h=`${l}/unevaluatedItems`;for(;u<r;u++){if(c[u])continue;let r=e(t[u],z,a,i,s,n,`${o}/${u}`,h);c[u]=!0,r.valid||ea.push({instanceLocation:o,keyword:"unevaluatedItems",keywordLocation:h,error:"Items did not match unevaluated items schema."},...r.errors)}}if(Y)for(let e=0;e<r;e++){let a=t[e],i="object"==typeof a&&null!==a;for(let s=0;s<r;s++){if(e===s)continue;let r=t[s],n="object"==typeof r&&null!==r;(a===r||i&&n&&tP(a,r))&&(ea.push({instanceLocation:o,keyword:"uniqueItems",keywordLocation:`${l}/uniqueItems`,error:`Duplicate items at indexes ${e} and ${s}.`}),e=Number.MAX_SAFE_INTEGER,s=Number.MAX_SAFE_INTEGER)}}}else if("number"===u){if("4"===a?(void 0!==G&&(!0===J&&t<=G||t<G)&&ea.push({instanceLocation:o,keyword:"minimum",keywordLocation:`${l}/minimum`,error:`${t} is less than ${J?"or equal to ":""} ${G}.`}),void 0!==W&&(!0===H&&t>=W||t>W)&&ea.push({instanceLocation:o,keyword:"maximum",keywordLocation:`${l}/maximum`,error:`${t} is greater than ${H?"or equal to ":""} ${W}.`})):(void 0!==G&&t<G&&ea.push({instanceLocation:o,keyword:"minimum",keywordLocation:`${l}/minimum`,error:`${t} is less than ${G}.`}),void 0!==W&&t>W&&ea.push({instanceLocation:o,keyword:"maximum",keywordLocation:`${l}/maximum`,error:`${t} is greater than ${W}.`}),void 0!==J&&t<=J&&ea.push({instanceLocation:o,keyword:"exclusiveMinimum",keywordLocation:`${l}/exclusiveMinimum`,error:`${t} is less than ${J}.`}),void 0!==H&&t>=H&&ea.push({instanceLocation:o,keyword:"exclusiveMaximum",keywordLocation:`${l}/exclusiveMaximum`,error:`${t} is greater than or equal to ${H}.`})),void 0!==X){let e=t%X;Math.abs(0-e)>=11920929e-14&&Math.abs(X-e)>=11920929e-14&&ea.push({instanceLocation:o,keyword:"multipleOf",keywordLocation:`${l}/multipleOf`,error:`${t} is not a multiple of ${X}.`})}}else if("string"===u){let e=void 0===Q&&void 0===Z?0:function(e){let t,r=0,a=e.length,i=0;for(;i<a;)r++,(t=e.charCodeAt(i++))>=55296&&t<=56319&&i<a&&(64512&(t=e.charCodeAt(i)))==56320&&i++;return r}(t);void 0!==Q&&e<Q&&ea.push({instanceLocation:o,keyword:"minLength",keywordLocation:`${l}/minLength`,error:`String is too short (${e} < ${Q}).`}),void 0!==Z&&e>Z&&ea.push({instanceLocation:o,keyword:"maxLength",keywordLocation:`${l}/maxLength`,error:`String is too long (${e} > ${Z}).`}),void 0===ee||new RegExp(ee).test(t)||ea.push({instanceLocation:o,keyword:"pattern",keywordLocation:`${l}/pattern`,error:"String does not match pattern."}),void 0!==T&&tR[T]&&!tR[T](t)&&ea.push({instanceLocation:o,keyword:"format",keywordLocation:`${l}/format`,error:`String does not match format "${T}".`})}return{valid:0===ea.length,errors:ea}}(e,this.schema,this.draft,this.lookup,this.shortCircuit)}addSchema(e,t){t&&(e={...e,$id:t}),tA(e,this.lookup)}}class tq extends tk{async *_transform(e){for await(let t of e)"string"==typeof t?yield this.parseResult([{text:t}]):yield this.parseResult([{message:t,text:this._baseMessageToString(t)}])}async *transform(e,t){yield*this._transformStreamWithConfig(e,this._transform.bind(this),{...t,runType:"parser"})}}class tB extends tq{constructor(e){super(e),Object.defineProperty(this,"diff",{enumerable:!0,configurable:!0,writable:!0,value:!1}),this.diff=e?.diff??this.diff}async *_transform(e){let t,r;for await(let a of e){let e;if("string"!=typeof a&&"string"!=typeof a.content)throw Error("Cannot handle non-string output.");if((0,tO.AJ)(a)){if("string"!=typeof a.content)throw Error("Cannot handle non-string message output.");e=new tt.ChatGenerationChunk({message:a,text:a.content})}else if((0,tO.ny)(a)){if("string"!=typeof a.content)throw Error("Cannot handle non-string message output.");e=new tt.ChatGenerationChunk({message:(0,eT.ih)(a),text:a.content})}else e=new tt.GenerationChunk({text:a});r=void 0===r?e:r.concat(e);let i=await this.parsePartialResult([r]);null==i||tP(i,t)||(this.diff?yield this._diff(t,i):yield i,t=i)}}getFormatInstructions(){return""}}class tY extends tq{constructor(){super(...arguments),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","output_parsers","bytes"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"textEncoder",{enumerable:!0,configurable:!0,writable:!0,value:new TextEncoder})}static lc_name(){return"BytesOutputParser"}parse(e){return Promise.resolve(this.textEncoder.encode(e))}getFormatInstructions(){return""}}class tG extends tq{constructor(){super(...arguments),Object.defineProperty(this,"re",{enumerable:!0,configurable:!0,writable:!0,value:void 0})}async *_transform(e){let t="";for await(let r of e)if("string"==typeof r?t+=r:t+=r.content,this.re){let e=[...t.matchAll(this.re)];if(e.length>1){let r=0;for(let t of e.slice(0,-1))yield[t[1]],r+=(t.index??0)+t[0].length;t=t.slice(r)}}else{let e=await this.parse(t);if(e.length>1){for(let t of e.slice(0,-1))yield[t];t=e[e.length-1]}}for(let e of(await this.parse(t)))yield[e]}}class tW extends tG{constructor(){super(...arguments),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","output_parsers","list"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0})}static lc_name(){return"CommaSeparatedListOutputParser"}async parse(e){try{return e.trim().split(",").map(e=>e.trim())}catch(t){throw new tx(`Could not parse output: ${e}`,e)}}getFormatInstructions(){return"Your response should be a list of comma separated values, eg: `foo, bar, baz`"}}class tJ extends tG{constructor({length:e,separator:t}){super(...arguments),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","output_parsers","list"]}),Object.defineProperty(this,"length",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"separator",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.length=e,this.separator=t||","}async parse(e){try{let t=e.trim().split(this.separator).map(e=>e.trim());if(void 0!==this.length&&t.length!==this.length)throw new tx(`Incorrect number of items. Expected ${this.length}, got ${t.length}.`);return t}catch(t){if(Object.getPrototypeOf(t)===tx.prototype)throw t;throw new tx(`Could not parse output: ${e}`)}}getFormatInstructions(){return`Your response should be a list of ${void 0===this.length?"":`${this.length} `}items separated by "${this.separator}" (eg: \`foo${this.separator} bar${this.separator} baz\`)`}}class tH extends tG{constructor(){super(...arguments),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","output_parsers","list"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"re",{enumerable:!0,configurable:!0,writable:!0,value:/\d+\.\s([^\n]+)/g})}static lc_name(){return"NumberedListOutputParser"}getFormatInstructions(){return`Your response should be a numbered list with each item on a new line. For example: 

1. foo

2. bar

3. baz`}async parse(e){return[...e.matchAll(this.re)??[]].map(e=>e[1])}}class tX extends tG{constructor(){super(...arguments),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","output_parsers","list"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"re",{enumerable:!0,configurable:!0,writable:!0,value:/^\s*[-*]\s([^\n]+)$/gm})}static lc_name(){return"NumberedListOutputParser"}getFormatInstructions(){return`Your response should be a numbered list with each item on a new line. For example: 

1. foo

2. bar

3. baz`}async parse(e){return[...e.matchAll(this.re)??[]].map(e=>e[1])}}class tQ extends tq{constructor(){super(...arguments),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","output_parsers","string"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0})}static lc_name(){return"StrOutputParser"}parse(e){return Promise.resolve(e)}getFormatInstructions(){return""}_textContentToString(e){return e.text}_imageUrlContentToString(e){throw Error('Cannot coerce a multimodal "image_url" message part into a string.')}_messageContentComplexToString(e){switch(e.type){case"text":case"text_delta":if("text"in e)return this._textContentToString(e);break;case"image_url":if("image_url"in e)return this._imageUrlContentToString(e);break;default:throw Error(`Cannot coerce "${e.type}" message part into a string.`)}throw Error(`Invalid content type: ${e.type}`)}_baseMessageContentToString(e){return e.reduce((e,t)=>e+this._messageContentComplexToString(t),"")}}class tZ extends tk{static lc_name(){return"StructuredOutputParser"}toJSON(){return this.toJSONNotImplemented()}constructor(e){super(e),Object.defineProperty(this,"schema",{enumerable:!0,configurable:!0,writable:!0,value:e}),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain","output_parsers","structured"]})}static fromZodSchema(e){return new this(e)}static fromNamesAndDescriptions(e){return new this(V.z.object(Object.fromEntries(Object.entries(e).map(([e,t])=>[e,V.z.string().describe(t)]))))}getFormatInstructions(){return`You must format your output as a JSON value that adheres to a given "JSON Schema" instance.

"JSON Schema" is a declarative language that allows you to annotate and validate JSON documents.

For example, the example "JSON Schema" instance {{"properties": {{"foo": {{"description": "a list of test words", "type": "array", "items": {{"type": "string"}}}}}}, "required": ["foo"]}}}}
would match an object with one required property, "foo". The "type" property specifies "foo" must be an "array", and the "description" property semantically describes it as "a list of test words". The items within "foo" must be strings.
Thus, the object {{"foo": ["bar", "baz"]}} is a well-formatted instance of this example "JSON Schema". The object {{"properties": {{"foo": ["bar", "baz"]}}}} is not well-formatted.

Your output will be parsed and type-checked according to the provided schema instance, so make sure all fields in your output match the schema exactly and there are no trailing commas!

Here is the JSON Schema instance your output must adhere to. Include the enclosing markdown codeblock:
\`\`\`json
${JSON.stringify((0,te.Ik)(this.schema))}
\`\`\`
`}async parse(e){try{let t=(e.includes("```")?e.trim().split(/```(?:json)?/)[1]:e.trim()).replace(/"([^"\\]*(\\.[^"\\]*)*)"/g,(e,t)=>{let r=t.replace(/\n/g,"\\n");return`"${r}"`}).replace(/\n/g,"");return await this.schema.parseAsync(JSON.parse(t))}catch(t){throw new tx(`Failed to parse. Text: "${e}". Error: ${t}`,e)}}}class t0 extends tZ{static lc_name(){return"JsonMarkdownStructuredOutputParser"}getFormatInstructions(e){let t=e?.interpolationDepth??1;if(t<1)throw Error("f string interpolation depth must be at least 1");return`Return a markdown code snippet with a JSON object formatted to look like:
\`\`\`json
${this._schemaToInstruction((0,te.Ik)(this.schema)).replaceAll("{","{".repeat(t)).replaceAll("}","}".repeat(t))}
\`\`\``}_schemaToInstruction(e,t=2){if("type"in e){let r,a=!1;if(Array.isArray(e.type)){let t=e.type.findIndex(e=>"null"===e);-1!==t&&(a=!0,e.type.splice(t,1)),r=e.type.join(" | ")}else r=e.type;if("object"===e.type&&e.properties){let r=e.description?` // ${e.description}`:"",a=Object.entries(e.properties).map(([r,a])=>{let i=e.required?.includes(r)?"":" (optional)";return`${" ".repeat(t)}"${r}": ${this._schemaToInstruction(a,t+2)}${i}`}).join("\n");return`{
${a}
${" ".repeat(t-2)}}${r}`}if("array"===e.type&&e.items){let r=e.description?` // ${e.description}`:"";return`array[
${" ".repeat(t)}${this._schemaToInstruction(e.items,t+2)}
${" ".repeat(t-2)}] ${r}`}let i=a?" (nullable)":"",s=e.description?` // ${e.description}`:"";return`${r}${s}${i}`}if("anyOf"in e)return e.anyOf.map(e=>this._schemaToInstruction(e,t)).join(`
${" ".repeat(t-2)}`);throw Error("unsupported schema type")}static fromZodSchema(e){return new this(e)}static fromNamesAndDescriptions(e){return new this(V.z.object(Object.fromEntries(Object.entries(e).map(([e,t])=>[e,V.z.string().describe(t)]))))}}class t1 extends tk{constructor({inputSchema:e}){super(...arguments),Object.defineProperty(this,"structuredInputParser",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.structuredInputParser=new t0(e)}async parse(e){let t;try{t=await this.structuredInputParser.parse(e)}catch(t){throw new tx(`Failed to parse. Text: "${e}". Error: ${t}`,e)}return this.outputProcessor(t)}getFormatInstructions(){return this.structuredInputParser.getFormatInstructions()}}var t2=r(91405),t5=r(13250);class t9 extends tB{constructor(){super(...arguments),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","output_parsers"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0})}static lc_name(){return"JsonOutputParser"}_diff(e,t){if(t)return e?(0,t2.UD)(e,t):[{op:"replace",path:"",value:t}]}async parsePartialResult(e){return(0,t5.D)(e[0].text)}async parse(e){return(0,t5.D)(e,JSON.parse)}getFormatInstructions(){return""}}let t4=function(){let e={};e.parser=function(e,t){return new r(e,t)},e.SAXParser=r,e.SAXStream=l,e.createStream=function(e,t){return new l(e,t)},e.MAX_BUFFER_LENGTH=65536;let t=["comment","sgmlDecl","textNode","tagName","doctype","procInstName","procInstBody","entity","attribName","attribValue","cdata","script"];function r(a,i){if(!(this instanceof r))return new r(a,i);(function(e){for(var r=0,a=t.length;r<a;r++)e[t[r]]=""})(this),this.q=this.c="",this.bufferCheckPosition=e.MAX_BUFFER_LENGTH,this.opt=i||{},this.opt.lowercase=this.opt.lowercase||this.opt.lowercasetags,this.looseCase=this.opt.lowercase?"toLowerCase":"toUpperCase",this.tags=[],this.closed=this.closedRoot=this.sawRoot=!1,this.tag=this.error=null,this.strict=!!a,this.noscript=!!(a||this.opt.noscript),this.state=_.BEGIN,this.strictEntities=this.opt.strictEntities,this.ENTITIES=this.strictEntities?Object.create(e.XML_ENTITIES):Object.create(e.ENTITIES),this.attribList=[],this.opt.xmlns&&(this.ns=Object.create(h)),this.trackPosition=!1!==this.opt.position,this.trackPosition&&(this.position=this.line=this.column=0),w(this,"onready")}e.EVENTS=["text","processinginstruction","sgmldeclaration","doctype","comment","opentagstart","attribute","opentag","closetag","opencdata","cdata","closecdata","error","end","ready","script","opennamespace","closenamespace"],Object.create||(Object.create=function(e){function t(){}return t.prototype=e,new t}),Object.keys||(Object.keys=function(e){var t=[];for(var r in e)e.hasOwnProperty(r)&&t.push(r);return t}),r.prototype={end:function(){T(this)},write:function(r){if(this.error)throw this.error;if(this.closed)return P(this,"Cannot write after close. Assign an onready handler.");if(null===r)return T(this);"object"==typeof r&&(r=r.toString());for(var a=0,i="";i=N(r,a++),this.c=i,i;)switch(this.trackPosition&&(this.position++,"\n"===i?(this.line++,this.column=0):this.column++),this.state){case _.BEGIN:if(this.state=_.BEGIN_WHITESPACE,"\uFEFF"===i)continue;$(this,i);continue;case _.BEGIN_WHITESPACE:$(this,i);continue;case _.TEXT:if(this.sawRoot&&!this.closedRoot){for(var s=a-1;i&&"<"!==i&&"&"!==i;)(i=N(r,a++))&&this.trackPosition&&(this.position++,"\n"===i?(this.line++,this.column=0):this.column++);this.textNode+=r.substring(s,a-1)}"<"!==i||this.sawRoot&&this.closedRoot&&!this.strict?(g(i)||this.sawRoot&&!this.closedRoot||E(this,"Text data outside of root node."),"&"===i?this.state=_.TEXT_ENTITY:this.textNode+=i):(this.state=_.OPEN_WAKA,this.startTagPosition=this.position);continue;case _.SCRIPT:"<"===i?this.state=_.SCRIPT_ENDING:this.script+=i;continue;case _.SCRIPT_ENDING:"/"===i?this.state=_.CLOSE_TAG:(this.script+="<"+i,this.state=_.SCRIPT);continue;case _.OPEN_WAKA:"!"===i?(this.state=_.SGML_DECL,this.sgmlDecl=""):g(i)||(y(p,i)?(this.state=_.OPEN_TAG,this.tagName=i):"/"===i?(this.state=_.CLOSE_TAG,this.tagName=""):"?"===i?(this.state=_.PROC_INST,this.procInstName=this.procInstBody=""):(E(this,"Unencoded <"),this.startTagPosition+1<this.position&&(i=Array(this.position-this.startTagPosition).join(" ")+i),this.textNode+="<"+i,this.state=_.TEXT));continue;case _.SGML_DECL:"[CDATA["===(this.sgmlDecl+i).toUpperCase()?(k(this,"onopencdata"),this.state=_.CDATA,this.sgmlDecl="",this.cdata=""):this.sgmlDecl+i==="--"?(this.state=_.COMMENT,this.comment="",this.sgmlDecl=""):"DOCTYPE"===(this.sgmlDecl+i).toUpperCase()?(this.state=_.DOCTYPE,(this.doctype||this.sawRoot)&&E(this,"Inappropriately located doctype declaration"),this.doctype="",this.sgmlDecl=""):">"===i?(k(this,"onsgmldeclaration",this.sgmlDecl),this.sgmlDecl="",this.state=_.TEXT):(b(i)&&(this.state=_.SGML_DECL_QUOTED),this.sgmlDecl+=i);continue;case _.SGML_DECL_QUOTED:i===this.q&&(this.state=_.SGML_DECL,this.q=""),this.sgmlDecl+=i;continue;case _.DOCTYPE:">"===i?(this.state=_.TEXT,k(this,"ondoctype",this.doctype),this.doctype=!0):(this.doctype+=i,"["===i?this.state=_.DOCTYPE_DTD:b(i)&&(this.state=_.DOCTYPE_QUOTED,this.q=i));continue;case _.DOCTYPE_QUOTED:this.doctype+=i,i===this.q&&(this.q="",this.state=_.DOCTYPE);continue;case _.DOCTYPE_DTD:this.doctype+=i,"]"===i?this.state=_.DOCTYPE:b(i)&&(this.state=_.DOCTYPE_DTD_QUOTED,this.q=i);continue;case _.DOCTYPE_DTD_QUOTED:this.doctype+=i,i===this.q&&(this.state=_.DOCTYPE_DTD,this.q="");continue;case _.COMMENT:"-"===i?this.state=_.COMMENT_ENDING:this.comment+=i;continue;case _.COMMENT_ENDING:"-"===i?(this.state=_.COMMENT_ENDED,this.comment=O(this.opt,this.comment),this.comment&&k(this,"oncomment",this.comment),this.comment=""):(this.comment+="-"+i,this.state=_.COMMENT);continue;case _.COMMENT_ENDED:">"!==i?(E(this,"Malformed comment"),this.comment+="--"+i,this.state=_.COMMENT):this.state=_.TEXT;continue;case _.CDATA:"]"===i?this.state=_.CDATA_ENDING:this.cdata+=i;continue;case _.CDATA_ENDING:"]"===i?this.state=_.CDATA_ENDING_2:(this.cdata+="]"+i,this.state=_.CDATA);continue;case _.CDATA_ENDING_2:">"===i?(this.cdata&&k(this,"oncdata",this.cdata),k(this,"onclosecdata"),this.cdata="",this.state=_.TEXT):"]"===i?this.cdata+="]":(this.cdata+="]]"+i,this.state=_.CDATA);continue;case _.PROC_INST:"?"===i?this.state=_.PROC_INST_ENDING:g(i)?this.state=_.PROC_INST_BODY:this.procInstName+=i;continue;case _.PROC_INST_BODY:!this.procInstBody&&g(i)||("?"===i?this.state=_.PROC_INST_ENDING:this.procInstBody+=i);continue;case _.PROC_INST_ENDING:">"===i?(k(this,"onprocessinginstruction",{name:this.procInstName,body:this.procInstBody}),this.procInstName=this.procInstBody="",this.state=_.TEXT):(this.procInstBody+="?"+i,this.state=_.PROC_INST_BODY);continue;case _.OPEN_TAG:y(d,i)?this.tagName+=i:(!function(e){e.strict||(e.tagName=e.tagName[e.looseCase]());var t=e.tags[e.tags.length-1]||e,r=e.tag={name:e.tagName,attributes:{}};e.opt.xmlns&&(r.ns=t.ns),e.attribList.length=0,k(e,"onopentagstart",r)}(this),">"===i?j(this):"/"===i?this.state=_.OPEN_TAG_SLASH:(g(i)||E(this,"Invalid character in tag name"),this.state=_.ATTRIB));continue;case _.OPEN_TAG_SLASH:">"===i?(j(this,!0),A(this)):(E(this,"Forward-slash in opening tag not followed by >"),this.state=_.ATTRIB);continue;case _.ATTRIB:g(i)||(">"===i?j(this):"/"===i?this.state=_.OPEN_TAG_SLASH:y(p,i)?(this.attribName=i,this.attribValue="",this.state=_.ATTRIB_NAME):E(this,"Invalid attribute name"));continue;case _.ATTRIB_NAME:"="===i?this.state=_.ATTRIB_VALUE:">"===i?(E(this,"Attribute without value"),this.attribValue=this.attribName,C(this),j(this)):g(i)?this.state=_.ATTRIB_NAME_SAW_WHITE:y(d,i)?this.attribName+=i:E(this,"Invalid attribute name");continue;case _.ATTRIB_NAME_SAW_WHITE:if("="===i)this.state=_.ATTRIB_VALUE;else{if(g(i))continue;E(this,"Attribute without value"),this.tag.attributes[this.attribName]="",this.attribValue="",k(this,"onattribute",{name:this.attribName,value:""}),this.attribName="",">"===i?j(this):y(p,i)?(this.attribName=i,this.state=_.ATTRIB_NAME):(E(this,"Invalid attribute name"),this.state=_.ATTRIB)}continue;case _.ATTRIB_VALUE:g(i)||(b(i)?(this.q=i,this.state=_.ATTRIB_VALUE_QUOTED):(E(this,"Unquoted attribute value"),this.state=_.ATTRIB_VALUE_UNQUOTED,this.attribValue=i));continue;case _.ATTRIB_VALUE_QUOTED:if(i!==this.q){"&"===i?this.state=_.ATTRIB_VALUE_ENTITY_Q:this.attribValue+=i;continue}C(this),this.q="",this.state=_.ATTRIB_VALUE_CLOSED;continue;case _.ATTRIB_VALUE_CLOSED:g(i)?this.state=_.ATTRIB:">"===i?j(this):"/"===i?this.state=_.OPEN_TAG_SLASH:y(p,i)?(E(this,"No whitespace between attributes"),this.attribName=i,this.attribValue="",this.state=_.ATTRIB_NAME):E(this,"Invalid attribute name");continue;case _.ATTRIB_VALUE_UNQUOTED:if(!(">"===(n=i)||g(n))){"&"===i?this.state=_.ATTRIB_VALUE_ENTITY_U:this.attribValue+=i;continue}C(this),">"===i?j(this):this.state=_.ATTRIB;continue;case _.CLOSE_TAG:this.tagName?">"===i?A(this):y(d,i)?this.tagName+=i:this.script?(this.script+="</"+this.tagName,this.tagName="",this.state=_.SCRIPT):(g(i)||E(this,"Invalid tagname in closing tag"),this.state=_.CLOSE_TAG_SAW_WHITE):g(i)||(y(p,i)?this.tagName=i:this.script?(this.script+="</"+i,this.state=_.SCRIPT):E(this,"Invalid tagname in closing tag."));continue;case _.CLOSE_TAG_SAW_WHITE:if(g(i))continue;">"===i?A(this):E(this,"Invalid characters in closing tag");continue;case _.TEXT_ENTITY:case _.ATTRIB_VALUE_ENTITY_Q:case _.ATTRIB_VALUE_ENTITY_U:switch(this.state){case _.TEXT_ENTITY:o=_.TEXT,l="textNode";break;case _.ATTRIB_VALUE_ENTITY_Q:o=_.ATTRIB_VALUE_QUOTED,l="attribValue";break;case _.ATTRIB_VALUE_ENTITY_U:o=_.ATTRIB_VALUE_UNQUOTED,l="attribValue"}if(";"===i)if(this.opt.unparsedEntities){var n,o,l,c=I(this);this.entity="",this.state=o,this.write(c)}else this[l]+=I(this),this.entity="",this.state=o;else y(this.entity.length?m:f,i)?this.entity+=i:(E(this,"Invalid character in entity name"),this[l]+="&"+this.entity+i,this.entity="",this.state=o);continue;default:throw Error(this,"Unknown state: "+this.state)}return this.position>=this.bufferCheckPosition&&function(r){for(var a=Math.max(e.MAX_BUFFER_LENGTH,10),i=0,s=0,n=t.length;s<n;s++){var o=r[t[s]].length;if(o>a)switch(t[s]){case"textNode":x(r);break;case"cdata":k(r,"oncdata",r.cdata),r.cdata="";break;case"script":k(r,"onscript",r.script),r.script="";break;default:P(r,"Max buffer length exceeded: "+t[s])}i=Math.max(i,o)}r.bufferCheckPosition=e.MAX_BUFFER_LENGTH-i+r.position}(this),this},resume:function(){return this.error=null,this},close:function(){return this.write(null)},flush:function(){x(this),""!==this.cdata&&(k(this,"oncdata",this.cdata),this.cdata=""),""!==this.script&&(k(this,"onscript",this.script),this.script="")}};var a,i,s,n=ReadableStream;n||(n=function(){});var o=e.EVENTS.filter(function(e){return"error"!==e&&"end"!==e});function l(e,t){if(!(this instanceof l))return new l(e,t);n.apply(this),this._parser=new r(e,t),this.writable=!0,this.readable=!0;var a=this;this._parser.onend=function(){a.emit("end")},this._parser.onerror=function(e){a.emit("error",e),a._parser.error=null},this._decoder=null,o.forEach(function(e){Object.defineProperty(a,"on"+e,{get:function(){return a._parser["on"+e]},set:function(t){if(!t)return a.removeAllListeners(e),a._parser["on"+e]=t,t;a.on(e,t)},enumerable:!0,configurable:!1})})}l.prototype=Object.create(n.prototype,{constructor:{value:l}}),l.prototype.write=function(e){return this._parser.write(e.toString()),this.emit("data",e),!0},l.prototype.end=function(e){return e&&e.length&&this.write(e),this._parser.end(),!0},l.prototype.on=function(e,t){var r=this;return r._parser["on"+e]||-1===o.indexOf(e)||(r._parser["on"+e]=function(){var t=1==arguments.length?[arguments[0]]:Array.apply(null,arguments);t.splice(0,0,e),r.emit.apply(r,t)}),n.prototype.on.call(r,e,t)};var c="http://www.w3.org/XML/1998/namespace",u="http://www.w3.org/2000/xmlns/",h={xml:c,xmlns:u},p=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,d=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040.\d-]/,f=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,m=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040.\d-]/;function g(e){return" "===e||"\n"===e||"\r"===e||"	"===e}function b(e){return'"'===e||"'"===e}function y(e,t){return e.test(t)}var _=0;for(var v in e.STATE={BEGIN:_++,BEGIN_WHITESPACE:_++,TEXT:_++,TEXT_ENTITY:_++,OPEN_WAKA:_++,SGML_DECL:_++,SGML_DECL_QUOTED:_++,DOCTYPE:_++,DOCTYPE_QUOTED:_++,DOCTYPE_DTD:_++,DOCTYPE_DTD_QUOTED:_++,COMMENT_STARTING:_++,COMMENT:_++,COMMENT_ENDING:_++,COMMENT_ENDED:_++,CDATA:_++,CDATA_ENDING:_++,CDATA_ENDING_2:_++,PROC_INST:_++,PROC_INST_BODY:_++,PROC_INST_ENDING:_++,OPEN_TAG:_++,OPEN_TAG_SLASH:_++,ATTRIB:_++,ATTRIB_NAME:_++,ATTRIB_NAME_SAW_WHITE:_++,ATTRIB_VALUE:_++,ATTRIB_VALUE_QUOTED:_++,ATTRIB_VALUE_CLOSED:_++,ATTRIB_VALUE_UNQUOTED:_++,ATTRIB_VALUE_ENTITY_Q:_++,ATTRIB_VALUE_ENTITY_U:_++,CLOSE_TAG:_++,CLOSE_TAG_SAW_WHITE:_++,SCRIPT:_++,SCRIPT_ENDING:_++},e.XML_ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'"},e.ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'",AElig:198,Aacute:193,Acirc:194,Agrave:192,Aring:197,Atilde:195,Auml:196,Ccedil:199,ETH:208,Eacute:201,Ecirc:202,Egrave:200,Euml:203,Iacute:205,Icirc:206,Igrave:204,Iuml:207,Ntilde:209,Oacute:211,Ocirc:212,Ograve:210,Oslash:216,Otilde:213,Ouml:214,THORN:222,Uacute:218,Ucirc:219,Ugrave:217,Uuml:220,Yacute:221,aacute:225,acirc:226,aelig:230,agrave:224,aring:229,atilde:227,auml:228,ccedil:231,eacute:233,ecirc:234,egrave:232,eth:240,euml:235,iacute:237,icirc:238,igrave:236,iuml:239,ntilde:241,oacute:243,ocirc:244,ograve:242,oslash:248,otilde:245,ouml:246,szlig:223,thorn:254,uacute:250,ucirc:251,ugrave:249,uuml:252,yacute:253,yuml:255,copy:169,reg:174,nbsp:160,iexcl:161,cent:162,pound:163,curren:164,yen:165,brvbar:166,sect:167,uml:168,ordf:170,laquo:171,not:172,shy:173,macr:175,deg:176,plusmn:177,sup1:185,sup2:178,sup3:179,acute:180,micro:181,para:182,middot:183,cedil:184,ordm:186,raquo:187,frac14:188,frac12:189,frac34:190,iquest:191,times:215,divide:247,OElig:338,oelig:339,Scaron:352,scaron:353,Yuml:376,fnof:402,circ:710,tilde:732,Alpha:913,Beta:914,Gamma:915,Delta:916,Epsilon:917,Zeta:918,Eta:919,Theta:920,Iota:921,Kappa:922,Lambda:923,Mu:924,Nu:925,Xi:926,Omicron:927,Pi:928,Rho:929,Sigma:931,Tau:932,Upsilon:933,Phi:934,Chi:935,Psi:936,Omega:937,alpha:945,beta:946,gamma:947,delta:948,epsilon:949,zeta:950,eta:951,theta:952,iota:953,kappa:954,lambda:955,mu:956,nu:957,xi:958,omicron:959,pi:960,rho:961,sigmaf:962,sigma:963,tau:964,upsilon:965,phi:966,chi:967,psi:968,omega:969,thetasym:977,upsih:978,piv:982,ensp:8194,emsp:8195,thinsp:8201,zwnj:8204,zwj:8205,lrm:8206,rlm:8207,ndash:8211,mdash:8212,lsquo:8216,rsquo:8217,sbquo:8218,ldquo:8220,rdquo:8221,bdquo:8222,dagger:8224,Dagger:8225,bull:8226,hellip:8230,permil:8240,prime:8242,Prime:8243,lsaquo:8249,rsaquo:8250,oline:8254,frasl:8260,euro:8364,image:8465,weierp:8472,real:8476,trade:8482,alefsym:8501,larr:8592,uarr:8593,rarr:8594,darr:8595,harr:8596,crarr:8629,lArr:8656,uArr:8657,rArr:8658,dArr:8659,hArr:8660,forall:8704,part:8706,exist:8707,empty:8709,nabla:8711,isin:8712,notin:8713,ni:8715,prod:8719,sum:8721,minus:8722,lowast:8727,radic:8730,prop:8733,infin:8734,ang:8736,and:8743,or:8744,cap:8745,cup:8746,int:8747,there4:8756,sim:8764,cong:8773,asymp:8776,ne:8800,equiv:8801,le:8804,ge:8805,sub:8834,sup:8835,nsub:8836,sube:8838,supe:8839,oplus:8853,otimes:8855,perp:8869,sdot:8901,lceil:8968,rceil:8969,lfloor:8970,rfloor:8971,lang:9001,rang:9002,loz:9674,spades:9824,clubs:9827,hearts:9829,diams:9830},Object.keys(e.ENTITIES).forEach(function(t){var r=e.ENTITIES[t],a="number"==typeof r?String.fromCharCode(r):r;e.ENTITIES[t]=a}),e.STATE)e.STATE[e.STATE[v]]=v;function w(e,t,r){e[t]&&e[t](r)}function k(e,t,r){e.textNode&&x(e),w(e,t,r)}function x(e){e.textNode=O(e.opt,e.textNode),e.textNode&&w(e,"ontext",e.textNode),e.textNode=""}function O(e,t){return e.trim&&(t=t.trim()),e.normalize&&(t=t.replace(/\s+/g," ")),t}function P(e,t){return x(e),e.trackPosition&&(t+="\nLine: "+e.line+"\nColumn: "+e.column+"\nChar: "+e.c),e.error=t=Error(t),w(e,"onerror",t),e}function T(e){return e.sawRoot&&!e.closedRoot&&E(e,"Unclosed root tag"),e.state!==_.BEGIN&&e.state!==_.BEGIN_WHITESPACE&&e.state!==_.TEXT&&P(e,"Unexpected end"),x(e),e.c="",e.closed=!0,w(e,"onend"),r.call(e,e.strict,e.opt),e}function E(e,t){if("object"!=typeof e||!(e instanceof r))throw Error("bad call to strictFail");e.strict&&P(e,t)}function S(e,t){var r=0>e.indexOf(":")?["",e]:e.split(":"),a=r[0],i=r[1];return t&&"xmlns"===e&&(a="xmlns",i=""),{prefix:a,local:i}}function C(e){if(e.strict||(e.attribName=e.attribName[e.looseCase]()),-1!==e.attribList.indexOf(e.attribName)||e.tag.attributes.hasOwnProperty(e.attribName)){e.attribName=e.attribValue="";return}if(e.opt.xmlns){var t=S(e.attribName,!0),r=t.prefix,a=t.local;if("xmlns"===r)if("xml"===a&&e.attribValue!==c)E(e,"xml: prefix must be bound to "+c+"\nActual: "+e.attribValue);else if("xmlns"===a&&e.attribValue!==u)E(e,"xmlns: prefix must be bound to "+u+"\nActual: "+e.attribValue);else{var i=e.tag,s=e.tags[e.tags.length-1]||e;i.ns===s.ns&&(i.ns=Object.create(s.ns)),i.ns[a]=e.attribValue}e.attribList.push([e.attribName,e.attribValue])}else e.tag.attributes[e.attribName]=e.attribValue,k(e,"onattribute",{name:e.attribName,value:e.attribValue});e.attribName=e.attribValue=""}function j(e,t){if(e.opt.xmlns){var r=e.tag,a=S(e.tagName);r.prefix=a.prefix,r.local=a.local,r.uri=r.ns[a.prefix]||"",r.prefix&&!r.uri&&(E(e,"Unbound namespace prefix: "+JSON.stringify(e.tagName)),r.uri=a.prefix);var i=e.tags[e.tags.length-1]||e;r.ns&&i.ns!==r.ns&&Object.keys(r.ns).forEach(function(t){k(e,"onopennamespace",{prefix:t,uri:r.ns[t]})});for(var s=0,n=e.attribList.length;s<n;s++){var o=e.attribList[s],l=o[0],c=o[1],u=S(l,!0),h=u.prefix,p=u.local,d=""===h?"":r.ns[h]||"",f={name:l,value:c,prefix:h,local:p,uri:d};h&&"xmlns"!==h&&!d&&(E(e,"Unbound namespace prefix: "+JSON.stringify(h)),f.uri=h),e.tag.attributes[l]=f,k(e,"onattribute",f)}e.attribList.length=0}e.tag.isSelfClosing=!!t,e.sawRoot=!0,e.tags.push(e.tag),k(e,"onopentag",e.tag),t||(e.noscript||"script"!==e.tagName.toLowerCase()?e.state=_.TEXT:e.state=_.SCRIPT,e.tag=null,e.tagName=""),e.attribName=e.attribValue="",e.attribList.length=0}function A(e){if(!e.tagName){E(e,"Weird empty close tag."),e.textNode+="</>",e.state=_.TEXT;return}if(e.script){if("script"!==e.tagName){e.script+="</"+e.tagName+">",e.tagName="",e.state=_.SCRIPT;return}k(e,"onscript",e.script),e.script=""}var t=e.tags.length,r=e.tagName;e.strict||(r=r[e.looseCase]());for(var a=r;t--;)if(e.tags[t].name!==a)E(e,"Unexpected close tag");else break;if(t<0){E(e,"Unmatched closing tag: "+e.tagName),e.textNode+="</"+e.tagName+">",e.state=_.TEXT;return}e.tagName=r;for(var i=e.tags.length;i-- >t;){var s=e.tag=e.tags.pop();e.tagName=e.tag.name,k(e,"onclosetag",e.tagName);var n={};for(var o in s.ns)n[o]=s.ns[o];var l=e.tags[e.tags.length-1]||e;e.opt.xmlns&&s.ns!==l.ns&&Object.keys(s.ns).forEach(function(t){var r=s.ns[t];k(e,"onclosenamespace",{prefix:t,uri:r})})}0===t&&(e.closedRoot=!0),e.tagName=e.attribValue=e.attribName="",e.attribList.length=0,e.state=_.TEXT}function I(e){var t,r=e.entity,a=r.toLowerCase(),i="";return e.ENTITIES[r]?e.ENTITIES[r]:e.ENTITIES[a]?e.ENTITIES[a]:("#"===(r=a).charAt(0)&&(i="x"===r.charAt(1)?(t=parseInt(r=r.slice(2),16)).toString(16):(t=parseInt(r=r.slice(1),10)).toString(10)),r=r.replace(/^0+/,""),isNaN(t)||i.toLowerCase()!==r)?(E(e,"Invalid character entity"),"&"+e.entity+";"):String.fromCodePoint(t)}function $(e,t){"<"===t?(e.state=_.OPEN_WAKA,e.startTagPosition=e.position):g(t)||(E(e,"Non-whitespace before first tag."),e.textNode=t,e.state=_.TEXT)}function N(e,t){var r="";return t<e.length&&(r=e.charAt(t)),r}return _=e.STATE,String.fromCodePoint||(a=String.fromCharCode,i=Math.floor,s=function(){var e,t,r=[],s=-1,n=arguments.length;if(!n)return"";for(var o="";++s<n;){var l=Number(arguments[s]);if(!isFinite(l)||l<0||l>1114111||i(l)!==l)throw RangeError("Invalid code point: "+l);l<=65535?r.push(l):(l-=65536,e=(l>>10)+55296,t=l%1024+56320,r.push(e,t)),(s+1===n||r.length>16384)&&(o+=a.apply(null,r),r.length=0)}return o},Object.defineProperty?Object.defineProperty(String,"fromCodePoint",{value:s,configurable:!0,writable:!0}):String.fromCodePoint=s),e}(),t3=`The output should be formatted as a XML file.
1. Output should conform to the tags below. 
2. If tags are not given, make them on your own.
3. Remember to always open and close all the tags.

As an example, for the tags ["foo", "bar", "baz"]:
1. String "<foo>
   <bar>
      <baz></baz>
   </bar>
</foo>" is a well-formatted instance of the schema. 
2. String "<foo>
   <bar>
   </foo>" is a badly-formatted instance.
3. String "<foo>
   <tag>
   </tag>
</foo>" is a badly-formatted instance.

Here are the output tags:
\`\`\`
{tags}
\`\`\``;class t8 extends tB{constructor(e){super(e),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","output_parsers"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),this.tags=e?.tags}static lc_name(){return"XMLOutputParser"}_diff(e,t){if(t)return e?(0,t2.UD)(e,t):[{op:"replace",path:"",value:t}]}async parsePartialResult(e){return re(e[0].text)}async parse(e){return re(e)}getFormatInstructions(){return this.tags&&this.tags.length>0?t3.replace("{tags}",this.tags?.join(", ")??""):t3}}let t6=e=>e.split("\n").map(e=>e.replace(/^\s+/,"")).join("\n").trim(),t7=e=>{if(0===Object.keys(e).length)return{};let t={};return e.children.length>0?t[e.name]=e.children.map(t7):t[e.name]=e.text??void 0,t};function re(e){let t=t6(e),r=t4.parser(!0),a={},i=[];r.onopentag=e=>{let t={name:e.name,attributes:e.attributes,children:[],text:"",isSelfClosing:e.isSelfClosing};i.length>0?i[i.length-1].children.push(t):a=t,e.isSelfClosing||i.push(t)},r.onclosetag=()=>{if(i.length>0){let e=i.pop();0===i.length&&e&&(a=e)}},r.ontext=e=>{if(i.length>0){let t=i[i.length-1];t.text+=e}},r.onattribute=e=>{i.length>0&&(i[i.length-1].attributes[e.name]=e.value)};let s=/```(xml)?(.*)```/s.exec(t),n=s?s[2]:t;return r.write(n).close(),a&&"?xml"===a.name&&(a=a.children[0]),t7(a)}var rt=r(60799),rr=r(58356),ra=r(46625);class ri extends rt.m{static lc_name(){return"PipelinePromptTemplate"}constructor(e){super({...e,inputVariables:[]}),Object.defineProperty(this,"pipelinePrompts",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"finalPrompt",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.pipelinePrompts=e.pipelinePrompts,this.finalPrompt=e.finalPrompt,this.inputVariables=this.computeInputValues()}computeInputValues(){let e=this.pipelinePrompts.map(e=>e.name);return[...new Set(this.pipelinePrompts.map(t=>t.prompt.inputVariables.filter(t=>!e.includes(t))).flat())]}static extractRequiredInputValues(e,t){return t.reduce((t,r)=>(t[r]=e[r],t),{})}async formatPipelinePrompts(e){let t=await this.mergePartialAndUserVariables(e);for(let{name:e,prompt:r}of this.pipelinePrompts){let a=ri.extractRequiredInputValues(t,r.inputVariables);r instanceof rr.RZ?t[e]=await r.formatMessages(a):t[e]=await r.format(a)}return ri.extractRequiredInputValues(t,this.finalPrompt.inputVariables)}async formatPromptValue(e){return this.finalPrompt.formatPromptValue(await this.formatPipelinePrompts(e))}async format(e){return this.finalPrompt.format(await this.formatPipelinePrompts(e))}async partial(e){let t={...this};return t.inputVariables=this.inputVariables.filter(t=>!(t in e)),t.partialVariables={...this.partialVariables??{},...e},new ri(t)}serialize(){throw Error("Not implemented.")}_getPromptType(){return"pipeline"}}var rs=r(33892),rn=r(58111),ro=r(99366),rl=r(1025);function rc(e){return"object"==typeof e&&null!=e&&"withStructuredOutput"in e&&"function"==typeof e.withStructuredOutput}class ru extends rr.RZ{get lc_aliases(){return{...super.lc_aliases,schema:"schema_"}}constructor(e){super(e),Object.defineProperty(this,"schema",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","prompts","structured"]}),this.schema=e.schema}pipe(e){if(rc(e))return super.pipe(e.withStructuredOutput(this.schema));if("object"==typeof e&&null!=e&&"lc_id"in e&&Array.isArray(e.lc_id)&&"langchain_core/runnables/RunnableBinding"===e.lc_id.join("/")&&rc(e.bound))return super.pipe(e.bound.withStructuredOutput(this.schema).bind(e.kwargs??{}).withConfig(e.config));throw Error('Structured prompts need to be piped to a language model that supports the "withStructuredOutput()" method.')}static fromMessagesAndSchema(e,t){return ru.fromMessages(e,{schema:t})}}class rh extends e1.YN{constructor(e){super(e),Object.defineProperty(this,"callbacks",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"metadata",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"verbose",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.callbacks=e?.callbacks,this.tags=e?.tags??[],this.metadata=e?.metadata??{},this.verbose=e?.verbose??!1}_getRelevantDocuments(e,t){throw Error("Not implemented!")}async invoke(e,t){return this.getRelevantDocuments(e,(0,ts.ZI)(t))}async getRelevantDocuments(e,t){let r=(0,ts.ZI)((0,eN.parseCallbackConfigArg)(t)),a=await eN.CallbackManager.configure(r.callbacks,this.callbacks,r.tags,this.tags,r.metadata,this.metadata,{verbose:this.verbose}),i=await a?.handleRetrieverStart(this.toJSON(),e,r.runId,void 0,void 0,void 0,r.runName);try{let t=await this._getRelevantDocuments(e,i);return await i?.handleRetrieverEnd(t),t}catch(e){throw await i?.handleRetrieverError(e),e}}}class rp extends eb.Serializable{}class rd extends rp{constructor(){super(...arguments),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain","storage"]}),Object.defineProperty(this,"store",{enumerable:!0,configurable:!0,writable:!0,value:{}})}async mget(e){return e.map(e=>this.store[e])}async mset(e){for(let[t,r]of e)this.store[t]=r}async mdelete(e){for(let t of e)delete this.store[t]}async *yieldKeys(e){for(let t of Object.keys(this.store))(void 0===e||t.startsWith(e))&&(yield t)}}var rf=r(28895),rm=r(48457);class rg extends e6{get lc_namespace(){return["langchain","tools"]}constructor(e){super(e??{}),Object.defineProperty(this,"returnDirect",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"verboseParsingErrors",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"responseFormat",{enumerable:!0,configurable:!0,writable:!0,value:"content"}),this.verboseParsingErrors=e?.verboseParsingErrors??this.verboseParsingErrors,this.responseFormat=e?.responseFormat??this.responseFormat}async invoke(e,t){let r,a;(0,rm.K)(e)?(r=e.id,a=e.args):a=e;let i=(0,ts.ZI)(t);return this.call(a,{...i,configurable:{...i.configurable,tool_call_id:r}})}async call(e,t,r){let a,i,s,n,o;try{a=await this.schema.parseAsync(e)}catch(r){let t="Received tool input did not match expected schema";throw this.verboseParsingErrors&&(t=`${t}
Details: ${r.message}`),new rm.q(t,JSON.stringify(e))}let l=(0,eN.parseCallbackConfigArg)(t),c=await eN.CallbackManager.configure(l.callbacks,this.callbacks,l.tags||r,this.tags,l.metadata,this.metadata,{verbose:this.verbose}),u=await c?.handleToolStart(this.toJSON(),"string"==typeof a?a:JSON.stringify(a),l.runId,void 0,void 0,void 0,l.runName);delete l.runId;try{i=await this._call(a,u,l)}catch(e){throw await u?.handleToolError(e),e}if("content_and_artifact"===this.responseFormat)if(Array.isArray(i)&&2===i.length)[s,n]=i;else throw Error(`Tool response format is "content_and_artifact" but the output was not a two-tuple.
Result: ${JSON.stringify(i)}`);else s=i;l&&"configurable"in l&&(o=l.configurable.tool_call_id);let h=function(e){let{content:t,artifact:r,toolCallId:a}=e;return a?new rf.uf("string"==typeof t||Array.isArray(t)&&t.every(e=>"object"==typeof e)?{content:t,artifact:r,tool_call_id:a,name:e.name}:{content:function(e){try{return JSON.stringify(e,null,2)}catch(t){return`${e}`}}(t),artifact:r,tool_call_id:a,name:e.name}):t}({content:s,artifact:n,toolCallId:o,name:this.name});return await u?.handleToolEnd(h),h}}class rb extends rg{constructor(e){super(e),Object.defineProperty(this,"schema",{enumerable:!0,configurable:!0,writable:!0,value:V.z.object({input:V.z.string().optional()}).transform(e=>e.input)})}call(e,t){return super.call("string"!=typeof e&&e?e:{input:e},t)}}class ry extends rb{static lc_name(){return"DynamicTool"}constructor(e){super(e),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"description",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"func",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.name=e.name,this.description=e.description,this.func=e.func,this.returnDirect=e.returnDirect??this.returnDirect}async call(e,t){let r=(0,eN.parseCallbackConfigArg)(t);return void 0===r.runName&&(r.runName=this.name),super.call(e,r)}async _call(e,t,r){return this.func(e,t,r)}}class r_ extends rg{static lc_name(){return"DynamicStructuredTool"}constructor(e){super(e),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"description",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"func",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"schema",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.name=e.name,this.description=e.description,this.func=e.func,this.returnDirect=e.returnDirect??this.returnDirect,this.schema=to(e.schema)?e.schema:V.z.object({}).passthrough()}async call(e,t,r){let a=(0,eN.parseCallbackConfigArg)(t);return void 0===a.runName&&(a.runName=this.name),super.call(e,a,r)}_call(e,t,r){return this.func(e,t,r)}}class rv{getTools(){return this.tools}}function rw(e,t){if(!t.schema||to(t.schema)&&(!("shape"in t.schema)||!t.schema.shape))return new ry({...t,description:t.description??t.schema?.description??`${t.name} tool`,func:e});let r=t.description??t.schema.description??`${t.name} tool`;return new r_({...t,description:r,schema:t.schema,func:async(t,r,a)=>new Promise((i,s)=>{let n=(0,ts.tn)(a,{callbacks:r?.getChild()});q.N.getInstance().run(n,async()=>{try{i(e(t,n))}catch(e){s(e)}})})})}var rk=r(65164),rx=r(52832),rO=r(19503),rP=r(56301);class rT extends rk.BaseTracer{constructor(){super(),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"langchain_tracer"}),Object.defineProperty(this,"endpoint",{enumerable:!0,configurable:!0,writable:!0,value:(0,rP.getEnvironmentVariable)("LANGCHAIN_ENDPOINT")||"http://localhost:1984"}),Object.defineProperty(this,"headers",{enumerable:!0,configurable:!0,writable:!0,value:{"Content-Type":"application/json"}}),Object.defineProperty(this,"session",{enumerable:!0,configurable:!0,writable:!0,value:void 0});let e=(0,rP.getEnvironmentVariable)("LANGCHAIN_API_KEY");e&&(this.headers["x-api-key"]=e)}async newSession(e){let t={start_time:Date.now(),name:e},r=await this.persistSession(t);return this.session=r,r}async loadSession(e){let t=`${this.endpoint}/sessions?name=${e}`;return this._handleSessionResponse(t)}async loadDefaultSession(){let e=`${this.endpoint}/sessions?name=default`;return this._handleSessionResponse(e)}async convertV2RunToRun(e){let t,r=this.session??await this.loadDefaultSession(),a=e.serialized;if("llm"===e.run_type){let i=e.inputs.prompts?e.inputs.prompts:e.inputs.messages.map(e=>(0,eT.Sw)(e));t={uuid:e.id,start_time:e.start_time,end_time:e.end_time,execution_order:e.execution_order,child_execution_order:e.child_execution_order,serialized:a,type:e.run_type,session_id:r.id,prompts:i,response:e.outputs}}else if("chain"===e.run_type){let i=await Promise.all(e.child_runs.map(e=>this.convertV2RunToRun(e)));t={uuid:e.id,start_time:e.start_time,end_time:e.end_time,execution_order:e.execution_order,child_execution_order:e.child_execution_order,serialized:a,type:e.run_type,session_id:r.id,inputs:e.inputs,outputs:e.outputs,child_llm_runs:i.filter(e=>"llm"===e.type),child_chain_runs:i.filter(e=>"chain"===e.type),child_tool_runs:i.filter(e=>"tool"===e.type)}}else if("tool"===e.run_type){let i=await Promise.all(e.child_runs.map(e=>this.convertV2RunToRun(e)));t={uuid:e.id,start_time:e.start_time,end_time:e.end_time,execution_order:e.execution_order,child_execution_order:e.child_execution_order,serialized:a,type:e.run_type,session_id:r.id,tool_input:e.inputs.input,output:e.outputs?.output,action:JSON.stringify(a),child_llm_runs:i.filter(e=>"llm"===e.type),child_chain_runs:i.filter(e=>"chain"===e.type),child_tool_runs:i.filter(e=>"tool"===e.type)}}else throw Error(`Unknown run type: ${e.run_type}`);return t}async persistRun(e){let t,r;t="llm"===(r=void 0!==e.run_type?await this.convertV2RunToRun(e):e).type?`${this.endpoint}/llm-runs`:"chain"===r.type?`${this.endpoint}/chain-runs`:`${this.endpoint}/tool-runs`;let a=await fetch(t,{method:"POST",headers:this.headers,body:JSON.stringify(r)});a.ok||console.error(`Failed to persist run: ${a.status} ${a.statusText}`)}async persistSession(e){let t=`${this.endpoint}/sessions`,r=await fetch(t,{method:"POST",headers:this.headers,body:JSON.stringify(e)});return r.ok?{id:(await r.json()).id,...e}:(console.error(`Failed to persist session: ${r.status} ${r.statusText}, using default session.`),{id:1,...e})}async _handleSessionResponse(e){let t,r=await fetch(e,{method:"GET",headers:this.headers});if(!r.ok)return console.error(`Failed to load session: ${r.status} ${r.statusText}`),t={id:1,start_time:Date.now()},this.session=t,t;let a=await r.json();return 0===a.length?t={id:1,start_time:Date.now()}:[t]=a,this.session=t,t}}async function rE(e){let t=new rT;return e?await t.loadSession(e):await t.loadDefaultSession(),t}async function rS(){return new rO.LangChainTracer}class rC extends rk.BaseTracer{constructor({exampleId:e}={}){super({_awaitHandler:!0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"run_collector"}),Object.defineProperty(this,"exampleId",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tracedRuns",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.exampleId=e,this.tracedRuns=[]}async persistRun(e){let t={...e};t.reference_example_id=this.exampleId,this.tracedRuns.push(t)}}let rj=(e,t)=>e.reduce((e,r,a)=>{let i=Math.floor(a/t),s=e[i]||[];return e[i]=s.concat([r]),e},[]);function rA(e,t){let r="number"==typeof t?void 0:t;return{name:e.name,description:e.description,parameters:(0,te.Ik)(e.schema),...r?.strict!==void 0?{strict:r.strict}:{}}}function rI(e,t){let r,a="number"==typeof t?void 0:t;return r=rR(e)?{type:"function",function:rA(e)}:e,a?.strict!==void 0&&(r.function.strict=a.strict),r}function r$(e){return void 0!==e&&Array.isArray(e.lc_namespace)}function rN(e){return void 0!==e&&e1.YN.isRunnable(e)&&"lc_name"in e.constructor&&"function"==typeof e.constructor.lc_name&&"RunnableToolLike"===e.constructor.lc_name()}function rM(e){return!!e&&"object"==typeof e&&"name"in e&&"schema"in e&&to(e.schema)}function rR(e){return rM(e)||rN(e)||r$(e)}function rL(e,t){let r=0,a=0,i=0;for(let s=0;s<e.length;s++)r+=e[s]*t[s],a+=e[s]*e[s],i+=t[s]*t[s];return r/(Math.sqrt(a)*Math.sqrt(i))}function rD(e,t){let r=0;for(let a=0;a<e.length;a++)r+=e[a]*t[a];return r}function rF(e,t){return Math.sqrt(function(e,t){let r=0;for(let a=0;a<e.length;a++)r+=(e[a]-t[a])*(e[a]-t[a]);return r}(e,t))}function rz(e,t,r){if(0===e.length||0===e[0].length||0===t.length||0===t[0].length)return[[]];if(e[0].length!==t[0].length)throw Error(`Number of columns in X and Y must be the same. X has shape ${[e.length,e[0].length]} and Y has shape ${[t.length,t[0].length]}.`);return e.map(e=>t.map(t=>r(e,t)).map(e=>Number.isNaN(e)?0:e))}function rU(e,t=!1){let r=e.reduce((e,t)=>Math.max(e,rY(t).maxValue),0);return e.map(e=>e.map(e=>t?1-e/r:e/r))}function rV(e,t){return rz(e,t,rL)}function rK(e,t){return rz(e,t,rD)}function rq(e,t){return rz(e,t,rF)}function rB(e,t,r=.5,a=4){if(0>=Math.min(a,t.length))return[];let i=rV(Array.isArray(e[0])?e:[e],t)[0],s=rY(i).maxIndex,n=[t[s]],o=[s];for(;o.length<Math.min(a,t.length);){let e=-1/0,a=-1,s=rV(t,n);i.forEach((t,i)=>{if(o.includes(i))return;let n=r*t-(1-r)*Math.max(...s[i]);n>e&&(e=n,a=i)}),n.push(t[a]),o.push(a)}return o}function rY(e){if(0===e.length)return{maxIndex:-1,maxValue:NaN};let t=e[0],r=0;for(let a=1;a<e.length;a+=1)e[a]>t&&(r=a,t=e[a]);return{maxIndex:r,maxValue:t}}class rG extends rh{static lc_name(){return"VectorStoreRetriever"}get lc_namespace(){return["langchain_core","vectorstores"]}_vectorstoreType(){return this.vectorStore._vectorstoreType()}constructor(e){super(e),Object.defineProperty(this,"vectorStore",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"k",{enumerable:!0,configurable:!0,writable:!0,value:4}),Object.defineProperty(this,"searchType",{enumerable:!0,configurable:!0,writable:!0,value:"similarity"}),Object.defineProperty(this,"searchKwargs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"filter",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.vectorStore=e.vectorStore,this.k=e.k??this.k,this.searchType=e.searchType??this.searchType,this.filter=e.filter,"mmr"===e.searchType&&(this.searchKwargs=e.searchKwargs)}async _getRelevantDocuments(e,t){if("mmr"===this.searchType){if("function"!=typeof this.vectorStore.maxMarginalRelevanceSearch)throw Error(`The vector store backing this retriever, ${this._vectorstoreType()} does not support max marginal relevance search.`);return this.vectorStore.maxMarginalRelevanceSearch(e,{k:this.k,filter:this.filter,...this.searchKwargs},t?.getChild("vectorstore"))}return this.vectorStore.similaritySearch(e,this.k,this.filter,t?.getChild("vectorstore"))}async addDocuments(e,t){return this.vectorStore.addDocuments(e,t)}}class rW extends eb.Serializable{constructor(e,t){super(t),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain","vectorstores",this._vectorstoreType()]}),Object.defineProperty(this,"embeddings",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.embeddings=e}async delete(e){throw Error("Not implemented.")}async similaritySearch(e,t=4,r,a){return(await this.similaritySearchVectorWithScore(await this.embeddings.embedQuery(e),t,r)).map(e=>e[0])}async similaritySearchWithScore(e,t=4,r,a){return this.similaritySearchVectorWithScore(await this.embeddings.embedQuery(e),t,r)}static fromTexts(e,t,r,a){throw Error("the Langchain vectorstore implementation you are using forgot to override this, please report a bug")}static fromDocuments(e,t,r){throw Error("the Langchain vectorstore implementation you are using forgot to override this, please report a bug")}asRetriever(e,t,r,a,i,s){if("number"==typeof e)return new rG({vectorStore:this,k:e,filter:t,tags:[...a??[],this._vectorstoreType()],metadata:i,verbose:s,callbacks:r});{let t={vectorStore:this,k:e?.k,filter:e?.filter,tags:[...e?.tags??[],this._vectorstoreType()],metadata:e?.metadata,verbose:e?.verbose,callbacks:e?.callbacks,searchType:e?.searchType};return new rG(e?.searchType==="mmr"?{...t,searchKwargs:e.searchKwargs}:{...t})}}}class rJ extends rW{static load(e,t){throw Error("Not implemented")}}class rH extends tk{constructor(){super(...arguments),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["tests","fake"]})}getFormatInstructions(){return""}async parse(e){return e.split(",").map(e=>e.trim())}}class rX extends e1.YN{constructor(e){super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["tests","fake"]}),Object.defineProperty(this,"returnOptions",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.returnOptions=e.returnOptions}async invoke(e,t){return this.returnOptions?t??{}:{input:e}}}class rQ extends tp{constructor(e){super(e),Object.defineProperty(this,"response",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"thrownErrorString",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.response=e.response,this.thrownErrorString=e.thrownErrorString}_llmType(){return"fake"}async _call(e,t,r){if(this.thrownErrorString)throw Error(this.thrownErrorString);let a=this.response??e;return await r?.handleLLMNewToken(a),a}}class rZ extends tp{constructor(e){super(e),Object.defineProperty(this,"sleep",{enumerable:!0,configurable:!0,writable:!0,value:50}),Object.defineProperty(this,"responses",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"thrownErrorString",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.sleep=e.sleep??this.sleep,this.responses=e.responses,this.thrownErrorString=e.thrownErrorString}_llmType(){return"fake"}async _call(e){if(this.thrownErrorString)throw Error(this.thrownErrorString);let t=this.responses?.[0];return this.responses=this.responses?.slice(1),t??e}async *_streamResponseChunks(e,t,r){if(this.thrownErrorString)throw Error(this.thrownErrorString);let a=this.responses?.[0];for(let t of(this.responses=this.responses?.slice(1),a??e))await new Promise(e=>setTimeout(e,this.sleep)),yield{text:t,generationInfo:{}},await r?.handleLLMNewToken(t)}}class r0 extends tc{_combineLLMOutput(){return[]}_llmType(){return"fake"}async _generate(e,t,r){if(t?.stop?.length)return{generations:[{message:new eR.AIMessage(t.stop[0]),text:t.stop[0]}]};let a=e.map(e=>"string"==typeof e.content?e.content:JSON.stringify(e.content,null,2)).join("\n");return await r?.handleLLMNewToken(a),{generations:[{message:new eR.AIMessage(a),text:a}],llmOutput:{}}}}class r1 extends tc{constructor(e){super(e),Object.defineProperty(this,"sleep",{enumerable:!0,configurable:!0,writable:!0,value:50}),Object.defineProperty(this,"responses",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"thrownErrorString",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.sleep=e.sleep??this.sleep,this.responses=e.responses,this.thrownErrorString=e.thrownErrorString}_llmType(){return"fake"}async _generate(e,t,r){if(this.thrownErrorString)throw Error(this.thrownErrorString);let a=this.responses?.[0].content??e[0].content;return{generations:[{text:"",message:new eR.AIMessage({content:a})}]}}async *_streamResponseChunks(e,t,r){if(this.thrownErrorString)throw Error(this.thrownErrorString);let a=this.responses?.[0].content??e[0].content;if("string"!=typeof a)for(let t of this.responses??e)yield new tt.ChatGenerationChunk({text:"",message:new eR.AIMessageChunk({content:a})});else for(let t of this.responses??e)yield new tt.ChatGenerationChunk({text:a,message:new eR.AIMessageChunk({content:a})})}}class r2 extends rh{constructor(e){super(),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["test","fake"]}),Object.defineProperty(this,"output",{enumerable:!0,configurable:!0,writable:!0,value:[new eH.y({pageContent:"foo"}),new eH.y({pageContent:"bar"})]}),this.output=e?.output??this.output}async _getRelevantDocuments(e){return this.output}}class r5 extends tc{static lc_name(){return"FakeListChatModel"}constructor({responses:e,sleep:t,emitCustomEvent:r}){super({}),Object.defineProperty(this,"responses",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"i",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,"sleep",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"emitCustomEvent",{enumerable:!0,configurable:!0,writable:!0,value:!1}),this.responses=e,this.sleep=t,this.emitCustomEvent=r??this.emitCustomEvent}_combineLLMOutput(){return[]}_llmType(){return"fake-list"}async _generate(e,t,r){if(await this._sleepIfRequested(),t?.thrownErrorString)throw Error(t.thrownErrorString);if(this.emitCustomEvent&&await r?.handleCustomEvent("some_test_event",{someval:!0}),t?.stop?.length)return{generations:[this._formatGeneration(t.stop[0])]};{let e=this._currentResponse();return this._incrementResponse(),{generations:[this._formatGeneration(e)],llmOutput:{}}}}_formatGeneration(e){return{message:new eR.AIMessage(e),text:e}}async *_streamResponseChunks(e,t,r){let a=this._currentResponse();for await(let e of(this._incrementResponse(),this.emitCustomEvent&&await r?.handleCustomEvent("some_test_event",{someval:!0}),a)){if(await this._sleepIfRequested(),t?.thrownErrorString)throw Error(t.thrownErrorString);let a=this._createResponseChunk(e);yield a,r?.handleLLMNewToken(e)}}async _sleepIfRequested(){void 0!==this.sleep&&await this._sleep()}async _sleep(){return new Promise(e=>{setTimeout(()=>e(),this.sleep)})}_createResponseChunk(e){return new tt.ChatGenerationChunk({message:new eR.AIMessageChunk({content:e}),text:e})}_currentResponse(){return this.responses[this.i]}_incrementResponse(){this.i<this.responses.length-1?this.i+=1:this.i=0}withStructuredOutput(e,t){return e1.jY.from(async e=>JSON.parse((await this.invoke(e)).content))}}class r9 extends eL{constructor(){super(),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","message","fake"]}),Object.defineProperty(this,"messages",{enumerable:!0,configurable:!0,writable:!0,value:[]})}async getMessages(){return this.messages}async addMessage(e){this.messages.push(e)}async addUserMessage(e){this.messages.push(new eR.HumanMessage(e))}async addAIChatMessage(e){this.messages.push(new eR.AIMessage(e))}async clear(){this.messages=[]}}class r4 extends eD{constructor(){super(),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","message","fake"]}),Object.defineProperty(this,"messages",{enumerable:!0,configurable:!0,writable:!0,value:[]})}async addMessage(e){this.messages.push(e)}async getMessages(){return this.messages}}class r3 extends rk.BaseTracer{constructor(){super(),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"fake_tracer"}),Object.defineProperty(this,"runs",{enumerable:!0,configurable:!0,writable:!0,value:[]})}persistRun(e){return this.runs.push(e),Promise.resolve()}}class r8 extends rg{constructor(e){super(e),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"description",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"schema",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.name=e.name,this.description=e.description,this.schema=e.schema}async _call(e,t){return JSON.stringify(e)}}class r6 extends eV{constructor(e){super(e??{})}embedDocuments(e){return Promise.resolve(e.map(()=>[.1,.2,.3,.4]))}embedQuery(e){return Promise.resolve([.1,.2,.3,.4])}}class r7 extends eV{constructor(e){super(e??{}),Object.defineProperty(this,"vectorSize",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.vectorSize=e?.vectorSize??4}async embedDocuments(e){return Promise.all(e.map(e=>this.embedQuery(e)))}async embedQuery(e){let t=e,r=(t=t.toLowerCase().replaceAll(/[^a-z ]/g,"")).length%this.vectorSize,a=0===r?0:this.vectorSize-r,i=t.length+a,s=(t=t.padEnd(i," ")).length/this.vectorSize,n=[];for(let e=0;e<t.length;e+=s)n.push(t.slice(e,e+s));return n.map(e=>{let t=0;for(let r=0;r<e.length;r+=1)t+=" "===e?0:e.charCodeAt(r);return t%26/26})}}class ae extends rk.BaseTracer{constructor(){super(),Object.defineProperty(this,"runPromiseResolver",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"runPromise",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"single_run_extractor"}),this.runPromise=new Promise(e=>{this.runPromiseResolver=e})}async persistRun(e){this.runPromiseResolver(e)}async extract(){return this.runPromise}}class at extends rW{_vectorstoreType(){return"memory"}constructor(e,{similarity:t,...r}={}){super(e,r),Object.defineProperty(this,"memoryVectors",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"similarity",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.similarity=t??rL}async addDocuments(e){let t=e.map(({pageContent:e})=>e);return this.addVectors(await this.embeddings.embedDocuments(t),e)}async addVectors(e,t){let r=e.map((e,r)=>({content:t[r].pageContent,embedding:e,metadata:t[r].metadata}));this.memoryVectors=this.memoryVectors.concat(r)}async similaritySearchVectorWithScore(e,t,r){let a=this.memoryVectors.filter(e=>!r||r(new eH.y({metadata:e.metadata,pageContent:e.content})));return a.map((t,r)=>({similarity:this.similarity(e,t.embedding),index:r})).sort((e,t)=>e.similarity>t.similarity?-1:0).slice(0,t).map(e=>[new eH.y({metadata:a[e.index].metadata,pageContent:a[e.index].content}),e.similarity])}static async fromTexts(e,t,r,a){let i=[];for(let r=0;r<e.length;r+=1){let a=Array.isArray(t)?t[r]:t,s=new eH.y({pageContent:e[r],metadata:a});i.push(s)}return at.fromDocuments(i,r,a)}static async fromDocuments(e,t,r){let a=new this(t,r);return await a.addDocuments(e),a}static async fromExistingIndex(e,t){return new this(e,t)}}var ar=r(97386);async function aa(e){let{optionalImportsMap:t={},optionalImportEntrypoints:r=[],importMap:a={},secretsMap:i={},path:s=["$"]}=this,n=s.join(".");if("object"==typeof e&&null!==e&&!Array.isArray(e)&&"lc"in e&&"type"in e&&"id"in e&&1===e.lc&&"secret"===e.type){let[t]=e.id;if(t in i)return i[t];{let e=(0,rP.getEnvironmentVariable)(t);if(e)return e;throw Error(`Missing key "${t}" for ${n} in load(secretsMap={})`)}}if("object"==typeof e&&null!==e&&!Array.isArray(e)&&"lc"in e&&"type"in e&&"id"in e&&1===e.lc&&"not_implemented"===e.type){let t=JSON.stringify(e);throw Error(`Trying to load an object that doesn't implement serialization: ${n} -> ${t}`)}if("object"==typeof e&&null!==e&&!Array.isArray(e)&&"lc"in e&&"type"in e&&"id"in e&&"kwargs"in e&&1===e.lc){let i=JSON.stringify(e),[o,...l]=e.id.slice().reverse(),c=l.reverse(),u=null,h=[c.join("/")];"langchain_community"===c[0]&&h.push(["langchain",...c.slice(1)].join("/"));let p=h.find(e=>e in t);if(ey.concat(r).includes(c.join("/"))||p)if(void 0!==p)u=await t[p];else throw Error(`Missing key "${c.join("/")}" for ${n} in load(optionalImportsMap={})`);else{let e,t;if("langchain"===c[0]||"langchain_core"===c[0])e=({langchain_core:I,langchain:a})[c[0]],c.shift();else throw Error(`Invalid namespace: ${n} -> ${i}`);if(0===c.length)throw Error(`Invalid namespace: ${n} -> ${i}`);do{if((t=c.join("__"))in e)break;c.pop()}while(c.length>0);t in e&&(u=e[t])}if("object"!=typeof u||null===u)throw Error(`Invalid namespace: ${n} -> ${i}`);let d=u[o]??Object.values(u).find(e=>"function"==typeof e&&(0,eb.get_lc_unique_name)(e)===o);if("function"!=typeof d)throw Error(`Invalid identifer: ${n} -> ${i}`);let f=await aa.call({...this,path:[...s,"kwargs"]},e.kwargs);if("constructor"===e.type){let e=new d((0,ar.d4)(f,ar.O3,function(e){let t={};for(let r=e;r&&r.prototype;r=Object.getPrototypeOf(r))Object.assign(t,Reflect.get(r.prototype,"lc_aliases"));return Object.entries(t).reduce((e,[t,r])=>(e[r]=t,e),{})}(d)));return Object.defineProperty(e.constructor,"name",{value:o}),e}throw Error(`Invalid type: ${n} -> ${i}`)}if("object"==typeof e&&null!==e)if(Array.isArray(e))return Promise.all(e.map((e,t)=>aa.call({...this,path:[...s,`${t}`]},e)));else return Object.fromEntries(await Promise.all(Object.entries(e).map(async([e,t])=>[e,await aa.call({...this,path:[...s,e]},t)])));return e}var ai=[],as=[];function an(e){if("object"!=typeof e||null===e)return e;let t=Array.isArray(e)?[]:{};for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=an(e[r]));return t}function ao(){return{v:1,id:em(-2),ts:new Date().toISOString(),channel_values:{},channel_versions:{},versions_seen:{},pending_sends:[]}}function al(e){return{v:e.v,id:e.id,ts:e.ts,channel_values:{...e.channel_values},channel_versions:{...e.channel_versions},versions_seen:an(e.versions_seen),pending_sends:[...e.pending_sends]}}function ac(e,t){return"number"==typeof e&&"number"==typeof t?Math.sign(e-t):String(e).localeCompare(String(t))}function au(...e){return e.reduce((e,t,r)=>0===r?t:ac(e,t)>=0?e:t)}class ah extends Error{constructor(e){super(e),this.name="InvalidNamespaceError"}}class ap{async get(e,t){return(await this.batch([{namespace:e,key:t}]))[0]}async search(e,t={}){let{filter:r,limit:a=10,offset:i=0,query:s}=t;return(await this.batch([{namespacePrefix:e,filter:r,limit:a,offset:i,query:s}]))[0]}async put(e,t,r,a){if(0===e.length)throw new ah("Namespace cannot be empty.");for(let t of e){if("string"!=typeof t)throw new ah(`Invalid namespace label '${t}' found in ${e}. Namespace labels must be strings, but got ${typeof t}.`);if(t.includes("."))throw new ah(`Invalid namespace label '${t}' found in ${e}. Namespace labels cannot contain periods ('.').`);if(""===t)throw new ah(`Namespace labels cannot be empty strings. Got ${t} in ${e}`)}if("langgraph"===e[0])throw new ah(`Root label for namespace cannot be "langgraph". Got: ${e}`);await this.batch([{namespace:e,key:t,value:r,index:a}])}async delete(e,t){await this.batch([{namespace:e,key:t,value:null}])}async listNamespaces(e={}){let{prefix:t,suffix:r,maxDepth:a,limit:i=100,offset:s=0}=e,n=[];return t&&n.push({matchType:"prefix",path:t}),r&&n.push({matchType:"suffix",path:r}),(await this.batch([{matchConditions:n.length?n:void 0,maxDepth:a,limit:i,offset:s}]))[0]}start(){}stop(){}}let ad=e=>"lg_name"in e&&"AsyncBatchedStore"===e.lg_name?e.store:e;class af extends ap{constructor(e){super(),Object.defineProperty(this,"lg_name",{enumerable:!0,configurable:!0,writable:!0,value:"AsyncBatchedStore"}),Object.defineProperty(this,"store",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"queue",{enumerable:!0,configurable:!0,writable:!0,value:new Map}),Object.defineProperty(this,"nextKey",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,"running",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"processingTask",{enumerable:!0,configurable:!0,writable:!0,value:null}),this.store=ad(e)}get isRunning(){return this.running}async batch(e){throw Error("The `batch` method is not implemented on `AsyncBatchedStore`.\n Instead, it calls the `batch` method on the wrapped store.\n If you are seeing this error, something is wrong.")}async get(e,t){return this.enqueueOperation({namespace:e,key:t})}async search(e,t){let{filter:r,limit:a=10,offset:i=0,query:s}=t||{};return this.enqueueOperation({namespacePrefix:e,filter:r,limit:a,offset:i,query:s})}async put(e,t,r){return this.enqueueOperation({namespace:e,key:t,value:r})}async delete(e,t){return this.enqueueOperation({namespace:e,key:t,value:null})}start(){this.running||(this.running=!0,this.processingTask=this.processBatchQueue())}async stop(){this.running=!1,this.processingTask&&await this.processingTask}enqueueOperation(e){return new Promise((t,r)=>{let a=this.nextKey;this.nextKey+=1,this.queue.set(a,{operation:e,resolve:t,reject:r})})}async processBatchQueue(){for(;this.running;){if(await new Promise(e=>{setTimeout(e,0)}),0===this.queue.size)continue;let e=new Map(this.queue);this.queue.clear();try{let t=Array.from(e.values()).map(({operation:e})=>e),r=await this.store.batch(t);e.forEach(({resolve:t},a)=>{let i=Array.from(e.keys()).indexOf(a);t(r[i])})}catch(t){e.forEach(({reject:e})=>{e(t)})}}}toJSON(){return{queue:this.queue,nextKey:this.nextKey,running:this.running,store:"[LangGraphStore]"}}}function am(e){return null!=e&&!0===e.lg_is_channel}class ag{constructor(){Object.defineProperty(this,"ValueType",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"UpdateType",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"lg_is_channel",{enumerable:!0,configurable:!0,writable:!0,value:!0})}consume(){return!1}}function ab(e,t){let r=Object.fromEntries(Object.entries(e).filter(([,e])=>am(e))),a={};for(let e in r)if(Object.prototype.hasOwnProperty.call(r,e)){let i=t.channel_values[e];a[e]=r[e].fromCheckpoint(i)}return a}function ay(e,t,r){let a;if(void 0===t)a=e.channel_values;else for(let e of(a={},Object.keys(t)))try{a[e]=t[e].checkpoint()}catch(e){if(e.name===Z.unminifiable_name);else throw e}return{v:1,id:em(r),ts:new Date().toISOString(),channel_values:a,channel_versions:{...e.channel_versions},versions_seen:an(e.versions_seen),pending_sends:e.pending_sends??[]}}class a_ extends ag{constructor(e,t){super(),Object.defineProperty(this,"lc_graph_name",{enumerable:!0,configurable:!0,writable:!0,value:"BinaryOperatorAggregate"}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"operator",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"initialValueFactory",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.operator=e,this.initialValueFactory=t,this.value=t?.()}fromCheckpoint(e){let t=new a_(this.operator,this.initialValueFactory);return e&&(t.value=e),t}update(e){let t=e;if(!t.length)return!1;for(let e of(void 0===this.value&&([this.value]=t,t=t.slice(1)),t))void 0!==this.value&&(this.value=this.operator(this.value,e));return!0}get(){if(void 0===this.value)throw new Z;return this.value}checkpoint(){if(void 0===this.value)throw new Z;return this.value}}class av extends ag{constructor(){super(...arguments),Object.defineProperty(this,"lc_graph_name",{enumerable:!0,configurable:!0,writable:!0,value:"LastValue"}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,writable:!0,value:void 0})}fromCheckpoint(e){let t=new av;return e&&(t.value=e),t}update(e){if(0===e.length)return!1;if(1!==e.length)throw new ee("LastValue can only receive one value per step.",{lc_error_code:"INVALID_CONCURRENT_GRAPH_UPDATE"});return this.value=e[e.length-1],!0}get(){if(void 0===this.value)throw new Z;return this.value}checkpoint(){if(void 0===this.value)throw new Z;return this.value}}let aw="__input__",ak="__error__",ax="__pregel_send",aO="__pregel_read",aP="__pregel_checkpointer",aT="__pregel_resuming",aE="__pregel_task_id",aS="__pregel_stream",aC="checkpoint_map",aj="__interrupt__",aA="__pregel_runtime_placeholder__",aI="langsmith:hidden",a$="__pregel_tasks",aN="__pregel_push",aM="__pregel_pull",aR=[aj,ak,a$,ax,aO,aP,aT,aE,aS,aC,aw];function aL(e){return"Send"===e.lg_name}class aD{constructor(e,t){Object.defineProperty(this,"runtime",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"config",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_promises",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"lg_is_managed_value",{enumerable:!0,configurable:!0,writable:!0,value:!0}),this.config=e}static async initialize(e,t){throw Error("Not implemented")}async promises(){return Promise.all(this._promises)}addPromise(e){this._promises.push(e)}}class aF extends Map{constructor(e){super(e?Array.from(e):void 0)}replaceRuntimeValues(e,t){if(0!==this.size&&t&&!Array.from(this.values()).every(e=>!e.runtime))if("object"!=typeof t||Array.isArray(t)){if("object"==typeof t&&"constructor"in t)for(let r of Object.getOwnPropertyNames(Object.getPrototypeOf(t)))try{let a=t[r];for(let[i,s]of this.entries())s.runtime&&s.call(e)===a&&(t[r]={[aA]:i})}catch(e){if(e.name!==TypeError.name)throw e}}else for(let[r,a]of Object.entries(t))for(let[i,s]of this.entries())s.runtime&&s.call(e)===a&&(t[r]={[aA]:i})}replaceRuntimePlaceholders(e,t){if(0!==this.size&&t&&!Array.from(this.values()).every(e=>!e.runtime)){if("object"!=typeof t||Array.isArray(t)){if("object"==typeof t&&"constructor"in t)for(let r of Object.getOwnPropertyNames(Object.getPrototypeOf(t)))try{let a=t[r];if("object"==typeof a&&null!==a&&aA in a){let i=this.get(a[aA]);i&&(t[r]=i.call(e))}}catch(e){if(e.name!==TypeError.name)throw e}}else for(let[r,a]of Object.entries(t))if("object"==typeof a&&null!==a&&aA in a){let i=a[aA];"string"==typeof i&&(t[r]=this.get(i)?.call(e))}}}}function az(e){return"object"==typeof e&&!!e&&"cls"in e&&"params"in e}class aU extends aD{call(){}static async initialize(e,t){return Promise.resolve(new aU(e))}}class aV{constructor(e){Object.defineProperty(this,"lc_graph_name",{enumerable:!0,configurable:!0,writable:!0,value:"AnnotationRoot"}),Object.defineProperty(this,"spec",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.spec=e}}let aK=function(e){return az(e)?e:e?aq(e):new av};function aq(e){return"object"==typeof e&&e&&"reducer"in e&&e.reducer?new a_(e.reducer,e.default):"object"==typeof e&&e&&"value"in e&&e.value?new a_(e.value,e.default):new av}aK.Root=e=>new aV(e);var aB=r(59915);let aY=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i,aG=function(e){return"string"==typeof e&&aY.test(e)},aW=["tags","metadata","callbacks","configurable"],aJ=["tags","metadata","callbacks","runName","maxConcurrency","recursionLimit","configurable","runId","outputKeys","streamMode","store"];function aH(...e){let t={tags:[],metadata:{},callbacks:void 0,recursionLimit:25,configurable:{}},r=q.N.getRunnableConfig();if(void 0!==r){for(let[e,a]of Object.entries(r))if(void 0!==a)if(aW.includes(e)){let r;r=Array.isArray(a)?[...a]:"object"==typeof a?"callbacks"===e&&"copy"in a&&"function"==typeof a.copy?a.copy():{...a}:a,t[e]=r}else t[e]=a}for(let r of e)if(void 0!==r)for(let[e,a]of Object.entries(r))void 0!==a&&aJ.includes(e)&&(t[e]=a);for(let[e,r]of Object.entries(t.configurable))t.metadata=t.metadata??{},e.startsWith("__")||"string"!=typeof r&&"number"!=typeof r&&"boolean"!=typeof r||e in t.metadata||(t.metadata[e]=r);return t}class aX extends e1.YN{constructor(e){super(),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langgraph"]}),Object.defineProperty(this,"func",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"config",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"trace",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"recurse",{enumerable:!0,configurable:!0,writable:!0,value:!0}),this.name=e.name??e.func.name,this.func=e.func,this.config=e.tags?{tags:e.tags}:void 0,this.trace=e.trace??this.trace,this.recurse=e.recurse??this.recurse}async _tracedInvoke(e,t,r){return new Promise((a,i)=>{let s=(0,ts.tn)(t,{callbacks:r?.getChild()});q.N.runWithConfig(s,async()=>{try{let t=await this.func(e,s);a(t)}catch(e){i(e)}})})}async invoke(e,t){let r,a=aH(t),i=(0,ts.SV)(this.config,a);return(r=this.trace?await this._callWithConfig(this._tracedInvoke,e,i):await q.N.runWithConfig(i,async()=>this.func(e,i)),e1.YN.isRunnable(r)&&this.recurse)?await q.N.runWithConfig(i,async()=>r.invoke(e,i)):r}}function*aQ(e,t){if(void 0===t)yield*e;else for(let r of e)yield[t,r]}async function aZ(e){let t=[];for await(let r of(await e))t.push(r);return t}function a0(e){let t=[];for(let r of e)t.push(r);return t}function a1(e,t){return e?"configurable"in e?{...e,configurable:{...e.configurable,...t}}:{...e,configurable:t}:{configurable:t}}let a2={[Symbol.for("LG_SKIP_WRITE")]:!0},a5={[Symbol.for("LG_PASSTHROUGH")]:!0},a9=Symbol("IS_WRITER");class a4 extends aX{constructor(e,t){super({writes:e,name:`ChannelWrite<${e.map(e=>aL(e)?e.node:e.channel).join(",")}>`,tags:t,func:async(e,t)=>this._write(e,t??{})}),Object.defineProperty(this,"writes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.writes=e}async _getWriteValues(e,t){let r=this.writes.filter(aL).map(e=>[a$,e]),a=this.writes.filter(e=>!aL(e));if(a.find(e=>e.channel===a$))throw new ee(`Cannot write to the reserved channel ${a$}`);return[...r,...await Promise.all(a.map(async r=>{var a;let i;i="object"==typeof(a=r.value)&&a?.[Symbol.for("LG_PASSTHROUGH")]!==void 0?e:r.value;let s=r.mapper?await r.mapper.invoke(i,t):i;return{...r,value:s}})).then(e=>e.filter(e=>!e.skipNone||null!==e.value).map(e=>[e.channel,e.value]))]}async _write(e,t){let r=await this._getWriteValues(e,t);return a4.doWrite(t,r),e}static doWrite(e,t){(e.configurable?.[ax])(t.filter(([e,t])=>"object"!=typeof t||t?.[Symbol.for("LG_SKIP_WRITE")]===void 0))}static isWriter(e){return e instanceof a4||a9 in e&&!!e[a9]}static registerWriter(e){return Object.defineProperty(e,a9,{value:!0})}}class a3 extends aX{constructor(e,t,r=!1){super({func:(e,t)=>a3.doRead(t,this.channel,this.fresh,this.mapper)}),Object.defineProperty(this,"lc_graph_name",{enumerable:!0,configurable:!0,writable:!0,value:"ChannelRead"}),Object.defineProperty(this,"channel",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"fresh",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"mapper",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.fresh=r,this.mapper=t,this.channel=e,this.name=Array.isArray(e)?`ChannelRead<${e.join(",")}>`:`ChannelRead<${e}>`}static doRead(e,t,r,a){let i=e.configurable?.[aO];if(!i)throw Error(`Runnable ${this} is not configured with a read function. Make sure to call in the context of a Pregel process`);return a?a(i(t,r)):i(t,r)}}let a8=new tn;class a6 extends e1.fJ{constructor(e){let{channels:t,triggers:r,mapper:a,writers:i,bound:s,kwargs:n,metadata:o,retryPolicy:l,tags:c,subgraphs:u}=e,h=[...e.config?.tags?e.config.tags:[],...c??[]];super({...e,bound:e.bound??a8,config:{...e.config?e.config:{},tags:h}}),Object.defineProperty(this,"lc_graph_name",{enumerable:!0,configurable:!0,writable:!0,value:"PregelNode"}),Object.defineProperty(this,"channels",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"triggers",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"mapper",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"writers",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"bound",{enumerable:!0,configurable:!0,writable:!0,value:a8}),Object.defineProperty(this,"kwargs",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"metadata",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"retryPolicy",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"subgraphs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.channels=t,this.triggers=r,this.mapper=a,this.writers=i??this.writers,this.bound=s??this.bound,this.kwargs=n??this.kwargs,this.metadata=o??this.metadata,this.tags=h,this.retryPolicy=l,this.subgraphs=u}getWriters(){let e=[...this.writers];for(;e.length>1&&e[e.length-1]instanceof a4&&e[e.length-2]instanceof a4;){let t=e.slice(-2),r=t[0].writes.concat(t[1].writes);e[e.length-2]=new a4(r,t[0].config?.tags),e.pop()}return e}getNode(){let e=this.getWriters();if(this.bound!==a8||0!==e.length)return this.bound===a8&&1===e.length?e[0]:this.bound===a8?new e1.zZ({first:e[0],middle:e.slice(1,e.length-1),last:e[e.length-1],omitSequenceTags:!0}):e.length>0?new e1.zZ({first:this.bound,middle:e.slice(0,e.length-1),last:e[e.length-1],omitSequenceTags:!0}):this.bound}join(e){if(!Array.isArray(e))throw Error("channels must be a list");if("object"!=typeof this.channels)throw Error("all channels must be named when using .join()");return new a6({channels:{...this.channels,...Object.fromEntries(e.map(e=>[e,e]))},triggers:this.triggers,mapper:this.mapper,writers:this.writers,bound:this.bound,kwargs:this.kwargs,config:this.config,retryPolicy:this.retryPolicy})}pipe(e){return new a6(a4.isWriter(e)?{channels:this.channels,triggers:this.triggers,mapper:this.mapper,writers:[...this.writers,e],bound:this.bound,config:this.config,kwargs:this.kwargs,retryPolicy:this.retryPolicy}:this.bound===a8?{channels:this.channels,triggers:this.triggers,mapper:this.mapper,writers:this.writers,bound:(0,e1.Bp)(e),config:this.config,kwargs:this.kwargs,retryPolicy:this.retryPolicy}:{channels:this.channels,triggers:this.triggers,mapper:this.mapper,writers:this.writers,bound:this.bound.pipe(e),config:this.config,kwargs:this.kwargs,retryPolicy:this.retryPolicy})}}var a7=r(19784);class ie extends Error{constructor(e){super(e),this.name="GraphValidationError"}}function it(e,t){if(Array.isArray(e)){for(let r of e)if(!(r in t))throw Error(`Key ${String(r)} not found in channels`)}else if(!(e in t))throw Error(`Key ${String(e)} not found in channels`)}function ir(e,t,r=!0,a=!1){try{return e[t].get()}catch(e){if(e.name===Z.unminifiable_name){if(a)return e;else if(r)return null}throw e}}function ia(e,t,r=!0){if(!Array.isArray(t))return ir(e,t);{let a={};for(let i of t)try{a[i]=ir(e,i,!r)}catch(e){if(e.name===Z.unminifiable_name)continue}return a}}function*ii(e,t,r){Array.isArray(e)?(!0===t||t.find(([t,r])=>e.includes(t)))&&(yield ia(r,e)):(!0===t||t.some(([t,r])=>t===e))&&(yield ir(r,e))}function*is(e,t,r){let a,i=t.filter(([e])=>void 0===e.config||!e.config.tags?.includes(aI));if(!i.length)return;a=Array.isArray(e)?i.filter(([t])=>t.writes.some(([t])=>e.includes(t))).map(([t])=>[t.name,Object.fromEntries(t.writes.filter(([t])=>e.includes(t)))]):i.flatMap(([t])=>t.writes.filter(([t,r])=>t===e).map(([e,r])=>[t.name,r]));let s=Object.fromEntries(i.map(([e])=>[e.name,[]]));for(let[e,t]of a)s[e].push(t);for(let[e,t]of Object.entries(s))0===t.length?delete s[e]:1===t.length&&(s[e]=t[0]);r&&(s.__metadata__={cached:r}),yield s}function io(e){return"lg_is_pregel"in e&&!0===e.lg_is_pregel}function il(e){let t=[e];for(let e of t)if(io(e))return e;else"steps"in e&&Array.isArray(e.steps)&&t.push(...e.steps)}let ic={blue:{start:"\x1b[34m",end:"\x1b[0m"},green:{start:"\x1b[32m",end:"\x1b[0m"},yellow:{start:"\x1b[33;1m",end:"\x1b[0m"}},iu=(e,t)=>`${e.start}${t}${e.end}`;function ih(e,t,r){return e.map(e=>{let a=t.find(([t,r])=>t===e.id&&r===ak)?.[2],i=t.filter(([t,r])=>t===e.id&&r===aj).map(([,,e])=>e);return a?{id:e.id,name:e.name,path:e.path,error:a,interrupts:i}:{id:e.id,name:e.name,path:e.path,interrupts:i,state:r?.[e.id]}})}function ip(e){let t,r=Object.values(e),a=r.length>0?typeof r[0]:void 0;return"number"===a?t=0:"string"===a&&(t=""),t}function id(e,t){if(!(Object.keys(e).length>0))return t;{let r=ip(t);return Object.fromEntries(Object.entries(t).filter(([t,a])=>a>(e[t]??r)))}}function im(e,t){return null===e?{configurable:t}:e?.configurable===void 0?{...e,configurable:t}:{...e,configurable:{...e.configurable,...t}}}function ig(e,t){let r=t?.parents??{};return Object.keys(r).length>0?im(e,{[aC]:{...r,[e.configurable?.checkpoint_ns??""]:e.configurable?.checkpoint_id}}):e}let ib=e=>void 0!==e?e+1:1;function iy(e,t,r){let a,i=Object.values(e.channel_versions),s=i.length>0?typeof i[0]:void 0;"number"===s?a=0:"string"===s&&(a="");let n=e.versions_seen[aj]??{},o=Object.entries(e.channel_versions).some(([e,t])=>t>(n[e]??a)),l=r.some(e=>"*"===t?!e.config?.tags?.includes(aI):t.includes(e.name));return o&&l}function i_(e,t,r,a,i,s,n=!1){let o,l=[],c=new Set;if(Array.isArray(s))l=s.filter(e=>a.get(e)),c=new Set((s=s.filter(e=>!a.get(e))).filter(e=>i.writes.some(([t,r])=>t===e)));else{for(let[e]of i.writes)if(e===s){c=new Set([e]);break}c=c||new Set}if(n&&c.size>0){let e=Object.fromEntries(Object.entries(r).filter(([e,t])=>c.has(e))),a=ay(t,e,-1),n=ab(e,a);iw(al(a),n,[i]),o=ia({...r,...n},s)}else o=ia(r,s);if(l.length>0)for(let t of l){let r=a.get(t);if(r){let a=r.call(e);o[t]=a}}return o}function iv(e,t,r,a,i,s){for(let[t,n]of s)if(t===a$){if(!aL(n))throw new ee(`Invalid packet type, expected SendProtocol, got ${JSON.stringify(n)}`);if(!(n.node in r))throw new ee(`Invalid node name "${n.node}" in Send packet`);i.replaceRuntimeValues(e,n.args)}else t in a||i.get(t)||console.warn(`Skipping write for channel '${t}' which has no readers`);t(s)}function iw(e,t,r,a){let i,s=Object.fromEntries(Object.entries(t).filter(([e,t])=>am(t)));for(let t of r)for(let r of(void 0===e.versions_seen[t.name]&&(e.versions_seen[t.name]={}),t.triggers))r in e.channel_versions&&(e.versions_seen[t.name][r]=e.channel_versions[r]);for(let t of(Object.keys(e.channel_versions).length>0&&(i=au(...Object.values(e.channel_versions))),new Set(r.flatMap(e=>e.triggers).filter(e=>!aR.includes(e)))))t in s&&s[t].consume()&&void 0!==a&&(e.channel_versions[t]=a(i,s[t]));e.pending_sends&&(e.pending_sends=[]);let n={},o={};for(let t of r)for(let[r,a]of t.writes)r===a$?e.pending_sends.push({node:a.node,args:a.args}):r in s?r in n?n[r].push(a):n[r]=[a]:r in o?o[r].push(a):o[r]=[a];i=void 0,Object.keys(e.channel_versions).length>0&&(i=au(...Object.values(e.channel_versions)));let l=new Set;for(let[t,r]of Object.entries(n))if(t in s){let n;try{n=s[t].update(r)}catch(e){if(e.name===ee.unminifiable_name){let a=new ee(`Invalid update for channel "${t}" with values ${JSON.stringify(r)}: ${e.message}`);throw a.lc_error_code=e.lc_error_code,a}throw e}n&&void 0!==a&&(e.channel_versions[t]=a(i,s[t])),l.add(t)}for(let t of Object.keys(s))!l.has(t)&&s[t].update([])&&void 0!==a&&(e.channel_versions[t]=a(i,s[t]));return o}function ik(e,t,r,a,i,s,n){let o={};for(let l=0;l<e.pending_sends.length;l+=1){let c=ix([aN,l],e,t,r,a,i,s,n);void 0!==c&&(o[c.id]=c)}for(let l of Object.keys(t)){let c=ix([aM,l],e,t,r,a,i,s,n);void 0!==c&&(o[c.id]=c)}return o}function ix(e,t,r,a,i,s,n,o){let{step:l,checkpointer:c,manager:u}=o,h=s.configurable??{},p=h.checkpoint_ns??"";if(e[0]===aN){let d="number"==typeof e[1]?e[1]:parseInt(e[1],10);if(d>=t.pending_sends.length)return;let f=t.pending_sends[d];if("string"!=typeof f.node||void 0===f.args)return void console.warn(`Ignoring invalid packet ${JSON.stringify(f)} in pending sends.`);if(!(f.node in r))return void console.warn(`Ignoring unknown node name ${f.node} in pending sends.`);let m=[aN],g=""===p?f.node:`${p}|${f.node}`,b=eg(JSON.stringify([g,l.toString(),f.node,aN,d.toString()]),t.id),y=`${g}:${b}`,_={langgraph_step:l,langgraph_node:f.node,langgraph_triggers:m,langgraph_path:e,langgraph_checkpoint_ns:y};if(!n)return{id:b,name:f.node,interrupts:[],path:e};{let n=r[f.node],d=n.getNode();if(void 0!==d){i.replaceRuntimePlaceholders(l,f.args),void 0!==n.metadata&&(_={..._,...n.metadata});let g=[];return{name:f.node,input:f.args,proc:d,subgraphs:n.subgraphs,writes:g,config:(0,ts.tn)((0,ts.SV)(s,{metadata:_,tags:n.tags,store:o.store??s.store}),{runName:f.node,callbacks:u?.getChild(`graph:step:${l}`),configurable:{[aE]:b,[ax]:e=>iv(l,e=>g.push(...e),r,a,i,e),[aO]:(e,r=!1)=>i_(l,t,a,i,{name:f.node,writes:g,triggers:m},e,r),[aP]:c??h[aP],[aC]:{...h[aC],[p]:t.id},checkpoint_id:void 0,checkpoint_ns:y}}),triggers:m,retry_policy:n.retryPolicy,id:b,path:e}}}}else if(e[0]===aM){let d=e[1].toString(),f=r[d];if(void 0===f)return;let m=ip(t.channel_versions);if(void 0===m)return;let g=t.versions_seen[d]??{},b=f.triggers.filter(e=>{let r=ir(a,e,!1,!0);return!(r instanceof Error&&r.name===Z.unminifiable_name)&&(t.channel_versions[e]??m)>(g[e]??m)}).sort();if(b.length>0){let m=function(e,t,r,a,i){let s;if("object"!=typeof t.channels||Array.isArray(t.channels))if(Array.isArray(t.channels)){let e=!1;for(let r of t.channels)try{s=ir(a,r,!1),e=!0;break}catch(e){if(e.name===Z.unminifiable_name)continue;throw e}if(!e)return}else throw Error(`Invalid channels type, expected list or dict, got ${t.channels}`);else for(let[i,n]of(s={},Object.entries(t.channels)))if(t.triggers.includes(n))try{s[i]=ir(a,n,!1)}catch(e){if(e.name===Z.unminifiable_name)return;throw e}else if(n in a)try{s[i]=ir(a,n,!0)}catch(e){if(e.name===Z.unminifiable_name)continue;throw e}else s[i]=r.get(i)?.call(e);return i&&void 0!==t.mapper&&(s=t.mapper(s)),s}(l,f,i,a,n);if(void 0===m)return;let g=""===p?d:`${p}|${d}`,y=eg(JSON.stringify([g,l.toString(),d,aM,b]),t.id),_={langgraph_step:l,langgraph_node:d,langgraph_triggers:b,langgraph_path:e,langgraph_checkpoint_ns:`${g}:${y}`};if(!n)return{id:y,name:d,interrupts:[],path:e};{let n=f.getNode();if(void 0!==n){void 0!==f.metadata&&(_={..._,...f.metadata});let v=[],w=`${g}:${y}`;return{name:d,input:m,proc:n,subgraphs:f.subgraphs,writes:v,config:(0,ts.tn)((0,ts.SV)(s,{metadata:_,tags:f.tags,store:o.store??s?.store}),{runName:d,callbacks:u?.getChild(`graph:step:${l}`),configurable:{[aE]:y,[ax]:e=>iv(l,e=>{v.push(...e)},r,a,i,e),[aO]:(e,r=!1)=>i_(l,t,a,i,{name:d,writes:v,triggers:b},e,r),[aP]:c??h[aP],[aC]:{...h[aC],[p]:t.id},checkpoint_id:void 0,checkpoint_ns:w}}),triggers:b,retry_policy:f.retryPolicy,id:y,path:e}}}}}}let iO=Symbol.for("INPUT_DONE"),iP=Symbol.for("INPUT_RESUMING"),iT=[ak,aj];class iE{constructor(e,t){Object.defineProperty(this,"push",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"modes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.push=e,this.modes=t}}class iS{constructor(e){Object.defineProperty(this,"input",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"output",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"config",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"checkpointer",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"checkpointerGetNextVersion",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"channels",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"managed",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"checkpoint",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"checkpointConfig",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"checkpointMetadata",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"checkpointNamespace",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"checkpointPendingWrites",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"checkpointPreviousVersions",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"step",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"stop",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"outputKeys",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"streamKeys",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"nodes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"skipDoneTasks",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"taskWritesLeft",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,"prevCheckpointConfig",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"status",{enumerable:!0,configurable:!0,writable:!0,value:"pending"}),Object.defineProperty(this,"tasks",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"stream",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"checkpointerPromises",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"isNested",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_checkpointerChainedPromise",{enumerable:!0,configurable:!0,writable:!0,value:Promise.resolve()}),Object.defineProperty(this,"store",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.input=e.input,this.checkpointer=e.checkpointer,void 0!==this.checkpointer?this.checkpointerGetNextVersion=this.checkpointer.getNextVersion.bind(this.checkpointer):this.checkpointerGetNextVersion=ib,this.checkpoint=e.checkpoint,this.checkpointMetadata=e.checkpointMetadata,this.checkpointPreviousVersions=e.checkpointPreviousVersions,this.channels=e.channels,this.managed=e.managed,this.checkpointPendingWrites=e.checkpointPendingWrites,this.step=e.step,this.stop=e.stop,this.config=e.config,this.checkpointConfig=e.checkpointConfig,this.isNested=e.isNested,this.outputKeys=e.outputKeys,this.streamKeys=e.streamKeys,this.nodes=e.nodes,this.skipDoneTasks=e.skipDoneTasks,this.store=e.store,this.stream=e.stream,this.checkpointNamespace=e.checkpointNamespace,this.prevCheckpointConfig=e.prevCheckpointConfig}static async initialize(e){let{config:t,stream:r}=e,{checkSubgraphs:a=!0}=e;void 0!==r&&t.configurable?.[aS]!==void 0&&(r=function(...e){return new iE(t=>{for(let r of e)r.modes.has(t[1])&&r.push(t)},new Set(e.flatMap(e=>Array.from(e.modes))))}(r,t.configurable[aS]));let i=t.configurable?.checkpoint_id===void 0,s=aO in(t.configurable??{});s||t.configurable?.checkpoint_ns===void 0||t.configurable?.checkpoint_ns===""||(t=im(t,{checkpoint_ns:"",checkpoint_id:void 0}));let n=t;t.configurable?.[aC]!==void 0&&t.configurable?.[aC]?.[t.configurable?.checkpoint_ns]&&(n=im(t,{checkpoint_id:t.configurable[aC][t.configurable?.checkpoint_ns]}));let o=t.configurable?.checkpoint_ns?.split("|")??[],l=await e.checkpointer?.getTuple(n)??{config:t,checkpoint:ao(),metadata:{source:"input",step:-2,writes:null,parents:{}},pendingWrites:[]};n={...t,...l.config,configurable:{checkpoint_ns:"",...t.configurable,...l.config.configurable}};let c=l.parentConfig,u=al(l.checkpoint),h={...l.metadata},p=l.pendingWrites??[],d=ab(e.channelSpecs,u),f=(h.step??0)+1,m=f+(t.recursionLimit??25)+1,g={...u.channel_versions},b=e.store?new af(e.store):void 0;if(b&&b.start(),a&&s&&void 0!==e.checkpointer)if(er().has(t.configurable?.checkpoint_ns))throw new et('Detected the same subgraph called multiple times by the same node.\nThis is not allowed if checkpointing is enabled.\n\nYou can disable checkpointing for a subgraph by compiling it with ".compile({ checkpointer: false });"',{lc_error_code:"MULTIPLE_SUBGRAPHS"});else er().add(t.configurable?.checkpoint_ns);return new iS({input:e.input,config:t,checkpointer:e.checkpointer,checkpoint:u,checkpointMetadata:h,checkpointConfig:n,prevCheckpointConfig:c,checkpointNamespace:o,channels:d,managed:e.managed,isNested:s,skipDoneTasks:i,step:f,stop:m,checkpointPreviousVersions:g,checkpointPendingWrites:p,outputKeys:e.outputKeys??[],streamKeys:e.streamKeys??[],nodes:e.nodes,stream:r,store:b})}_checkpointerPutAfterPrevious(e){this._checkpointerChainedPromise=this._checkpointerChainedPromise.then(()=>this.checkpointer?.put(e.config,e.checkpoint,e.metadata,e.newVersions)),this.checkpointerPromises.push(this._checkpointerChainedPromise)}async updateManagedValues(e,t){let r=this.managed.get(e);r&&"update"in r&&"function"==typeof r.update&&await r.update(t)}putWrites(e,t){if(0===t.length)return;let r=t[0][0];if(!(t.find(([e])=>e===a$)||iT.includes(r))&&!this.taskWritesLeft)return this._outputWrites(e,t);r!==aj&&(this.taskWritesLeft-=1);let a=t.map(([t,r])=>[e,t,r]);this.checkpointPendingWrites.push(...a);let i=this.checkpointer?.putWrites({...this.checkpointConfig,configurable:{...this.checkpointConfig.configurable,checkpoint_ns:this.config.configurable?.checkpoint_ns??"",checkpoint_id:this.checkpoint.id}},t,e);void 0!==i&&this.checkpointerPromises.push(i),this._outputWrites(e,t)}_outputWrites(e,t,r=!1){let a=this.tasks[e];if(void 0!==a){if(void 0!==a.config&&(a.config.tags??[]).includes(aI))return;t.length>0&&t[0][0]!==ak&&t[0][0]!==aj&&this._emit(a0(aQ(is(this.outputKeys,[[a,t]],r),"updates"))),r||this._emit(a0(aQ(function*(e,t,r){let a=new Date().toISOString();for(let[{id:i,name:s,config:n},o]of t)n?.tags?.includes(aI)||(yield{type:"task_result",timestamp:a,step:e,payload:{id:i,name:s,result:o.filter(([e])=>Array.isArray(r)?r.includes(e):e===r),interrupts:o.filter(([e])=>e===aj)}})}(this.step,[[a,t]],this.streamKeys),"debug")))}}async tick(e){let t;try{this.store&&!this.store.isRunning&&this.store?.start();let{inputKeys:t=[],interruptAfter:r=[],interruptBefore:a=[],manager:i}=e;if("pending"!==this.status)throw Error(`Cannot tick when status is no longer "pending". Current status: "${this.status}"`);if([iO,iP].includes(this.input))if(!Object.values(this.tasks).every(e=>e.writes.length>0))return!1;else{let e=Object.values(this.tasks).flatMap(e=>e.writes),t=iw(this.checkpoint,this.channels,Object.values(this.tasks),this.checkpointerGetNextVersion);for(let[e,r]of Object.entries(t))await this.updateManagedValues(e,r);let a=await aZ(aQ(ii(this.outputKeys,e,this.channels),"values"));if(this._emit(a),this.checkpointPendingWrites=[],await this._putCheckpoint({source:"loop",writes:is(this.outputKeys,Object.values(this.tasks).map(e=>[e,e.writes])).next().value??null}),iy(this.checkpoint,r,Object.values(this.tasks))){if(this.status="interrupt_after",!this.isNested)return!1;throw new J}}else await this._first(t);if(this.step>this.stop)return this.status="out_of_steps",!1;let s=ik(this.checkpoint,this.nodes,this.channels,this.managed,this.config,!0,{step:this.step,checkpointer:this.checkpointer,isResuming:this.input===iP,manager:i,store:this.store});if(this.tasks=s,this.taskWritesLeft=Object.values(this.tasks).length-1,this.checkpointer&&this._emit(await aZ(aQ(function*(e,t,r,a,i,s,n,o){function l(e){let t={};return null!=e.callbacks&&(t.callbacks=e.callbacks),null!=e.configurable&&(t.configurable=e.configurable),null!=e.maxConcurrency&&(t.max_concurrency=e.maxConcurrency),null!=e.metadata&&(t.metadata=e.metadata),null!=e.recursionLimit&&(t.recursion_limit=e.recursionLimit),null!=e.runId&&(t.run_id=e.runId),null!=e.runName&&(t.run_name=e.runName),null!=e.tags&&(t.tags=e.tags),t}let c=t.configurable?.checkpoint_ns,u={};for(let e of s){if(!(e.subgraphs?.length?e.subgraphs:[e.proc]).find(il))continue;let r=`${e.name}:${e.id}`;c&&(r=`${c}|${r}`),u[e.id]={configurable:{thread_id:t.configurable?.thread_id,checkpoint_ns:r}}}let h=new Date().toISOString();yield{type:"checkpoint",timestamp:h,step:e,payload:{config:l(t),values:ia(r,a),metadata:i,next:s.map(e=>e.name),tasks:ih(s,n,u),parentConfig:o?l(o):void 0}}}(this.step-1,this.checkpointConfig,this.channels,this.streamKeys,this.checkpointMetadata,Object.values(this.tasks),this.checkpointPendingWrites,this.prevCheckpointConfig),"debug"))),0===Object.values(this.tasks).length)return this.status="done",!1;if(this.skipDoneTasks&&this.checkpointPendingWrites.length>0){for(let[e,t,r]of this.checkpointPendingWrites){if(t===ak||t===aj)continue;let a=Object.values(this.tasks).find(t=>t.id===e);a&&a.writes.push([t,r])}for(let e of Object.values(this.tasks))e.writes.length>0&&this._outputWrites(e.id,e.writes,!0)}if(Object.values(this.tasks).every(e=>e.writes.length>0))return this.tick({inputKeys:t,interruptAfter:r,interruptBefore:a,manager:i});if(iy(this.checkpoint,a,Object.values(this.tasks))){if(this.status="interrupt_before",!this.isNested)return!1;throw new J}let n=await aZ(aQ(function*(e,t){let r=new Date().toISOString();for(let{id:a,name:i,input:s,config:n,triggers:o,writes:l}of t){if(n?.tags?.includes(aI))continue;let t=l.filter(([e,t])=>e===a&&t===aj).map(([,e])=>e);yield{type:"task",timestamp:r,step:e,payload:{id:a,name:i,input:s,triggers:o,interrupts:t}}}}(this.step,Object.values(this.tasks)),"debug"));return this._emit(n),!0}catch(e){if(t=e,this._suppressInterrupt(t))this.output=ia(this.channels,this.outputKeys);else throw t;return!1}finally{void 0===t&&(this.output=ia(this.channels,this.outputKeys))}}_suppressInterrupt(e){return X(e)&&!this.isNested}async _first(e){let t=0!==Object.keys(this.checkpoint.channel_versions).length&&(this.config.configurable?.[aT]!==void 0||null===this.input);if(t){for(let e of Object.keys(this.channels))if(void 0!==this.checkpoint.channel_versions[e]){let t=this.checkpoint.channel_versions[e];this.checkpoint.versions_seen[aj]={...this.checkpoint.versions_seen[aj],[e]:t}}let e=await aZ(aQ(ii(this.outputKeys,!0,this.channels),"values"));this._emit(e)}else{let t=await aZ(function*(e,t){if(null!=t)if(Array.isArray(e)&&"object"==typeof t&&!Array.isArray(t))for(let r in t)e.includes(r)&&(yield[r,t[r]]);else if(Array.isArray(e))throw Error('Input chunk must be an object when "inputChannels" is an array');else yield[e,t]}(e,this.input));if(0===t.length)throw new Q(`Received no input writes for ${JSON.stringify(e,null,2)}`);let r=ik(this.checkpoint,this.nodes,this.channels,this.managed,this.config,!0,{step:this.step});iw(this.checkpoint,this.channels,Object.values(r).concat([{name:aw,writes:t,triggers:[]}]),this.checkpointerGetNextVersion),await this._putCheckpoint({source:"input",writes:Object.fromEntries(t)})}this.input=t?iP:iO,this.isNested||(this.config=im(this.config,{[aT]:t}))}_emit(e){for(let t of e)this.stream.modes.has(t[0])&&this.stream.push([this.checkpointNamespace,...t])}async _putCheckpoint(e){let t={...e,step:this.step,parents:this.config.configurable?.[aC]??{}};if(void 0!==this.checkpointer){this.prevCheckpointConfig=this.checkpointConfig?.configurable?.checkpoint_id?this.checkpointConfig:void 0,this.checkpointMetadata=t,this.checkpoint=ay(this.checkpoint,this.channels,this.step),this.checkpointConfig={...this.checkpointConfig,configurable:{...this.checkpointConfig.configurable,checkpoint_ns:this.config.configurable?.checkpoint_ns??""}};let e={...this.checkpoint.channel_versions},r=id(this.checkpointPreviousVersions,e);this.checkpointPreviousVersions=e,this._checkpointerPutAfterPrevious({config:{...this.checkpointConfig},checkpoint:al(this.checkpoint),metadata:{...this.checkpointMetadata},newVersions:r}),this.checkpointConfig={...this.checkpointConfig,configurable:{...this.checkpointConfig.configurable,checkpoint_id:this.checkpoint.id}}}this.step+=1}}let iC=[400,401,402,403,404,405,406,407,409],ij=e=>{if(e.message.startsWith("Cancel")||e.message.startsWith("AbortError")||"AbortError"===e.name||e?.code==="ECONNABORTED")return!1;let t=e?.response?.status??e?.status;return!(t&&iC.includes(+t))&&e?.error?.code!=="insufficient_quota"};async function*iA(e,t){let r,{stepTimeout:a,retryPolicy:i}=t??{},s=t?.signal,n=Object.fromEntries(e.map(e=>[e.id,iI(e,i)]));a&&s?"any"in AbortSignal&&(s=AbortSignal.any([s,AbortSignal.timeout(a)])):a&&(s=AbortSignal.timeout(a)),s?.throwIfAborted();let o=new Promise((e,t)=>{r=()=>t(Error("Abort")),s?.addEventListener("abort",r)}).finally(()=>s?.removeEventListener("abort",r));for(;Object.keys(n).length>0;){let e=await Promise.race([...Object.values(n),o]);yield e,delete n[e.task.id]}}async function iI(e,t){let r,a,i=e.retry_policy??t,s=void 0!==i?i.initialInterval??500:0,n=0;for(;;){for(;e.writes.length>0;)e.writes.pop();r=void 0;try{a=await e.proc.invoke(e.input,e.config);break}catch(o){if((r=o).pregelTaskId=e.id,X(r)||void 0===i||(n+=1)>=(i.maxAttempts??3)||!(i.retryOn??ij)(r))break;s=Math.min(i.maxInterval??128e3,s*(i.backoffFactor??2));let t=i.jitter?Math.floor(s+1e3*Math.random()):s;await new Promise(e=>setTimeout(e,t));let a=r.name??r.constructor.unminifiable_name??r.constructor.name;console.log(`Retrying task "${e.name}" after ${s.toFixed(2)} seconds (attempt ${n}) after ${a}: ${r}`)}finally{let t=e.config?.configurable?.checkpoint_ns;t&&er().delete(t)}}return{task:e,result:a,error:r}}class i${static subscribeTo(e,t){let r,{key:a,tags:i}=t??{};if(Array.isArray(e)&&void 0!==a)throw Error("Can't specify a key when subscribing to multiple channels");return new a6({channels:r="string"==typeof e?a?{[a]:e}:[e]:Object.fromEntries(e.map(e=>[e,e])),triggers:Array.isArray(e)?e:[e],tags:i})}static writeTo(e,t){let r=[];for(let t of e)r.push({channel:t,value:a5,skipNone:!1});for(let[e,a]of Object.entries(t??{}))e1.YN.isRunnable(a)||"function"==typeof a?r.push({channel:e,value:a5,skipNone:!0,mapper:(0,e1.Bp)(a)}):r.push({channel:e,value:a,skipNone:!1});return new a4(r)}}class iN extends e1.YN{static lc_name(){return"LangGraph"}constructor(e){super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langgraph","pregel"]}),Object.defineProperty(this,"lg_is_pregel",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"nodes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"channels",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"inputChannels",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"outputChannels",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"autoValidate",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"streamMode",{enumerable:!0,configurable:!0,writable:!0,value:["values"]}),Object.defineProperty(this,"streamChannels",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"interruptAfter",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"interruptBefore",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"stepTimeout",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"debug",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"checkpointer",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"retryPolicy",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"config",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"store",{enumerable:!0,configurable:!0,writable:!0,value:void 0});let{streamMode:t}=e;null==t||Array.isArray(t)||(t=[t]),this.nodes=e.nodes,this.channels=e.channels,this.autoValidate=e.autoValidate??this.autoValidate,this.streamMode=t??this.streamMode,this.inputChannels=e.inputChannels,this.outputChannels=e.outputChannels,this.streamChannels=e.streamChannels??this.streamChannels,this.interruptAfter=e.interruptAfter,this.interruptBefore=e.interruptBefore,this.stepTimeout=e.stepTimeout??this.stepTimeout,this.debug=e.debug??this.debug,this.checkpointer=e.checkpointer,this.retryPolicy=e.retryPolicy,this.config=e.config,this.store=e.store,this.autoValidate&&this.validate()}withConfig(e){let t=(0,ts.SV)(this.config,e);return new this.constructor({...this,config:t})}validate(){return!function({nodes:e,channels:t,inputChannels:r,outputChannels:a,streamChannels:i,interruptAfterNodes:s,interruptBeforeNodes:n}){if(!t)throw new ie("Channels not provided");let o=new Set,l=new Set;for(let[t,r]of Object.entries(e)){if(t===aj)throw new ie(`"Node name ${aj} is reserved"`);if(r.constructor===a6)r.triggers.forEach(e=>o.add(e));else throw new ie(`Invalid node type ${typeof r}, expected PregelNode`)}for(let e of o)if(!(e in t))throw new ie(`Subcribed channel '${String(e)}' not in channels`);if(Array.isArray(r)){if(r.every(e=>!o.has(e)))throw new ie(`None of the input channels ${r} are subscribed to by any node`)}else if(!o.has(r))throw new ie(`Input channel ${String(r)} is not subscribed to by any node`);for(let e of(Array.isArray(a)?a.forEach(e=>l.add(e)):l.add(a),i&&!Array.isArray(i)?l.add(i):Array.isArray(i)&&i.forEach(e=>l.add(e)),l))if(!(e in t))throw new ie(`Output channel '${String(e)}' not in channels`);if(s&&"*"!==s){for(let t of s)if(!(t in e))throw new ie(`Node ${String(t)} not in nodes`)}if(n&&"*"!==n){for(let t of n)if(!(t in e))throw new ie(`Node ${String(t)} not in nodes`)}}({nodes:this.nodes,channels:this.channels,outputChannels:this.outputChannels,inputChannels:this.inputChannels,streamChannels:this.streamChannels,interruptAfterNodes:this.interruptAfter,interruptBeforeNodes:this.interruptBefore}),this}get streamChannelsList(){return Array.isArray(this.streamChannels)?this.streamChannels:this.streamChannels?[this.streamChannels]:Object.keys(this.channels)}get streamChannelsAsIs(){return this.streamChannels?this.streamChannels:Object.keys(this.channels)}async getGraphAsync(e){return this.getGraph(e)}*getSubgraphs(e,t){for(let[r,a]of Object.entries(this.nodes))if(void 0===e||e.startsWith(r))for(let i of a.subgraphs?.length?a.subgraphs:[a.bound]){let a=il(i);if(void 0!==a){if(r===e)return void(yield[r,a]);if(void 0===e&&(yield[r,a]),t){let i=e;for(let[s,n]of(void 0!==e&&(i=e.slice(r.length+1)),a.getSubgraphs(i,t)))yield[`${r}|${s}`,n]}}}}async *getSubgraphsAsync(e,t){yield*this.getSubgraphs(e,t)}async _prepareStateSnapshot({config:e,saved:t,subgraphCheckpointer:r}){if(void 0===t)return{values:{},next:[],config:e,tasks:[]};let{managed:a}=await this.prepareSpecs(e,{skipManaged:!0}),i=ab(this.channels,t.checkpoint),s=Object.values(ik(t.checkpoint,this.nodes,i,a,t.config,!1,{step:(t.metadata?.step??-1)+1})),n=await aZ(this.getSubgraphsAsync()),o=t.config.configurable?.checkpoint_ns??"",l={};for(let e of s){let a=n.find(([t])=>t===e.name);if(!a)continue;let i=`${e.name}:${e.id}`;if(o&&(i=`${o}|${i}`),void 0===r){let r={configurable:{thread_id:t.config.configurable?.thread_id,checkpoint_ns:i}};l[e.id]=r}else{let s={configurable:{[aP]:r,thread_id:t.config.configurable?.thread_id,checkpoint_ns:i}};l[e.id]=await a[1].getState(s,{subgraphs:!0})}}return{values:ia(i,this.streamChannelsAsIs),next:s.map(e=>e.name),tasks:ih(s,t?.pendingWrites??[],l),metadata:t.metadata,config:ig(t.config,t.metadata),createdAt:t.checkpoint.ts,parentConfig:t.parentConfig}}async getState(e,t){let r=e.configurable?.[aP]??this.checkpointer;if(!r)throw new W("No checkpointer set");let a=e.configurable?.checkpoint_ns??"";if(""!==a&&e.configurable?.[aP]===void 0){let i=a.split("|").map(e=>e.split(":")[0]).join("|");for await(let[a,s]of this.getSubgraphsAsync(i,!0))if(a===i)return await s.getState(a1(e,{[aP]:r}),{subgraphs:t?.subgraphs});throw Error(`Subgraph with namespace "${i}" not found.`)}let i=(0,ts.SV)(this.config,e),s=await r.getTuple(e);return await this._prepareStateSnapshot({config:i,saved:s,subgraphCheckpointer:t?.subgraphs?r:void 0})}async *getStateHistory(e,t){let r=e.configurable?.[aP]??this.checkpointer;if(!r)throw Error("No checkpointer set");let a=e.configurable?.checkpoint_ns??"";if(""!==a&&e.configurable?.[aP]===void 0){let i=a.split("|").map(e=>e.split(":")[0]).join("|");for await(let[a,s]of this.getSubgraphsAsync(i,!0))if(a===i)return void(yield*s.getStateHistory(a1(e,{[aP]:r}),t));throw Error(`Subgraph with namespace "${i}" not found.`)}let i=(0,ts.SV)(this.config,e,{configurable:{checkpoint_ns:a}});for await(let e of r.list(i,t))yield this._prepareStateSnapshot({config:e.config,saved:e})}async updateState(e,t,r){let a=e.configurable?.[aP]??this.checkpointer;if(!a)throw new W("No checkpointer set");let i=e.configurable?.checkpoint_ns??"";if(""!==i&&e.configurable?.[aP]===void 0){let s=i.split("|").map(e=>e.split(":")[0]).join("|");for await(let[,i]of this.getSubgraphsAsync(s,!0))return await i.updateState(a1(e,{[aP]:a}),t,r);throw Error(`Subgraph "${s}" not found`)}let s=this.config?(0,ts.SV)(this.config,e):e,n=await a.getTuple(s),o=void 0!==n?al(n.checkpoint):ao(),l={...n?.checkpoint.channel_versions},c=n?.metadata?.step??-1,u=a1(s,{checkpoint_ns:s.configurable?.checkpoint_ns??""});if(n&&(u=a1(s,n.config.configurable)),null==t&&void 0===r)return ig(await a.put(u,ay(o,void 0,c),{source:"update",step:c,writes:{},parents:n?.metadata?.parents??{}},{}),n?n.metadata:void 0);let h=Object.values(o.versions_seen).map(e=>Object.values(e)).flat().find(e=>!!e);if(void 0===r&&void 0===h)"string"==typeof this.inputChannels&&void 0!==this.nodes[this.inputChannels]&&(r=this.inputChannels);else if(void 0===r){let e=Object.entries(o.versions_seen).map(([e,t])=>Object.values(t).map(t=>[t,e])).flat().sort(([e],[t])=>ac(e,t));e&&(1===e.length?r=e[0][1]:e[e.length-1][0]!==e[e.length-2][0]&&(r=e[e.length-1][1]))}if(void 0===r)throw new ee('Ambiguous update, specify "asNode"');if(void 0===this.nodes[r])throw new ee(`Node "${r.toString()}" does not exist`);let p=ab(this.channels,o),{managed:d}=await this.prepareSpecs(s,{skipManaged:!0}),f=this.nodes[r].getWriters();if(!f.length)throw new ee(`No writers found for node "${r.toString()}"`);let m={name:r,input:t,proc:f.length>1?e1.zZ.from(f,{omitSequenceTags:!0}):f[0],writes:[],triggers:[aj],id:eg(aj,o.id)};await m.proc.invoke(m.input,(0,ts.tn)({...s,store:s?.store??this.store},{runName:s.runName??`${this.getName()}UpdateState`,configurable:{[ax]:e=>m.writes.push(...e),[aO]:(e,t=!1)=>i_(c,o,p,d,m,e,t)}})),void 0!==n&&await a.putWrites(u,m.writes,m.id),iw(o,p,[m],a.getNextVersion.bind(this.checkpointer));let g=id(l,o.channel_versions);return ig(await a.put(u,ay(o,p,c+1),{source:"update",step:c+1,writes:{[r]:t},parents:n?.metadata?.parents??{}},g),n?n.metadata:void 0)}_defaults(e){let t,{debug:r,streamMode:a,inputKeys:i,outputKeys:s,interruptAfter:n,interruptBefore:o,...l}=e,c=void 0!==r?r:this.debug,u=s;void 0===u?u=this.streamChannelsAsIs:it(u,this.channels);let h=i;void 0===h?h=this.inputChannels:it(h,this.channels);let p=o??this.interruptBefore??[],d=n??this.interruptAfter??[];return t=void 0!==a?Array.isArray(a)?a:[a]:this.streamMode,e.configurable?.[aE]!==void 0&&(t=["values"]),[c,t,h,u,l,p,d,!1===this.checkpointer?void 0:void 0!==e&&e.configurable?.[aP]!==void 0?e.configurable[aP]:this.checkpointer,e.store??this.store]}async stream(e,t){return super.stream(e,t)}async prepareSpecs(e,t){let r={...e,store:this.store},a={},i={};for(let[e,r]of Object.entries(this.channels))am(r)?a[e]=r:t?.skipManaged?i[e]={cls:aU,params:{config:{}}}:i[e]=r;return{channelSpecs:a,managed:new aF(await Object.entries(i).reduce(async(e,[t,a])=>{let i,s=await e;return az(a)?("key"in a.params&&"__channel_key_placeholder__"===a.params.key&&(a.params.key=t),i=await a.cls.initialize(r,a.params)):i=await a.initialize(r),void 0!==i&&s.push([t,i]),s},Promise.resolve([])))}}async *_streamIterator(e,t){var r,a,i;let s,n=t?.subgraphs,o=aH(this.config,t);if(void 0===o.recursionLimit||o.recursionLimit<1)throw Error('Passed "recursionLimit" must be at least 1.');if(void 0!==this.checkpointer&&!1!==this.checkpointer&&void 0===o.configurable)throw Error('Checkpointer requires one or more of the following "configurable" keys: "thread_id", "checkpoint_ns", "checkpoint_id"');let l=await (0,ts.kJ)(o),c=await l?.handleChainStart(this.toJSON(),!e||Array.isArray(e)||e instanceof Date||"object"!=typeof e?{input:e}:e,o.runId,void 0,void 0,void 0,o?.runName??this.getName());delete o.runId;let[u,h,,p,d,f,m,g,b]=this._defaults(o),{channelSpecs:y,managed:_}=await this.prepareSpecs(d),v=new a7;function*w(){for(;void 0!==s&&v.length>0;){let e=v.shift();if(void 0===e)throw Error("Data structure error.");let[t,r,a]=e;h.includes(r)&&(n&&h.length>1?yield[t,r,a]:h.length>1?yield[r,a]:n?yield[t,a]:yield a)}}try{for(s=await iS.initialize({input:e,config:d,checkpointer:g,nodes:this.nodes,channelSpecs:y,managed:_,outputKeys:p,streamKeys:this.streamChannelsAsIs,store:b,stream:new iE(e=>v.push(e),new Set(h))}),t?.subgraphs&&(s.config.configurable={...s.config.configurable,[aS]:s.stream});await s.tick({inputKeys:this.inputChannels,interruptAfter:m,interruptBefore:f,manager:c});){for await(let{task:e,error:t}of(u&&(r=s.checkpointMetadata.step,a=s.channels,i=this.streamChannelsList,console.log([`${iu(ic.blue,`[${r}:checkpoint]`)}`,`\x1b[1m State at the end of step ${r}:\x1b[0m
`,JSON.stringify(ia(a,i),null,2)].join(""))),yield*w(),u&&function(e,t){let r=t.length;console.log([`${iu(ic.blue,`[${e}:tasks]`)}`,`\x1b[1m Starting step ${e} with ${r} task${1===r?"":"s"}:\x1b[0m
`,t.map(e=>`- ${iu(ic.green,String(e.name))} -> ${JSON.stringify(e.input,null,2)}`).join("\n")].join(""))}(s.step,Object.values(s.tasks)),iA(Object.values(s.tasks).filter(e=>0===e.writes.length),{stepTimeout:this.stepTimeout,signal:d.signal,retryPolicy:this.retryPolicy}))){if(void 0!==t)if(X(t)){if(s.isNested)throw t;t.interrupts.length&&s.putWrites(e.id,t.interrupts.map(e=>[aj,e]))}else s.putWrites(e.id,[[ak,{message:t.message,name:t.name}]]);else s.putWrites(e.id,e.writes);if(yield*w(),void 0!==t&&!X(t))throw t}u&&function(e,t,r){let a={};for(let[e,i]of t)r.includes(e)&&(a[e]||(a[e]=[]),a[e].push(i));console.log([`${iu(ic.blue,`[${e}:writes]`)}`,`\x1b[1m Finished step ${e} with writes to ${Object.keys(a).length} channel${1!==Object.keys(a).length?"s":""}:\x1b[0m
`,Object.entries(a).map(([e,t])=>`- ${iu(ic.yellow,e)} -> ${t.map(e=>JSON.stringify(e)).join(", ")}`).join("\n")].join(""))}(s.step,Object.values(s.tasks).map(e=>e.writes).flat(),this.streamChannelsList)}if(yield*w(),"out_of_steps"===s.status)throw new G(`Recursion limit of ${d.recursionLimit} reached without hitting a stop condition. You can increase the limit by setting the "recursionLimit" config key.`,{lc_error_code:"GRAPH_RECURSION_LIMIT"});await Promise.all(s?.checkpointerPromises??[]),await c?.handleChainEnd(s.output)}catch(e){throw await c?.handleChainError(e),e}finally{s&&await s.store?.stop(),await Promise.all([...s?.checkpointerPromises??[],...Array.from(_.values()).map(e=>e.promises())])}}async invoke(e,t){let r=t?.streamMode??"values",a={...t,outputKeys:t?.outputKeys??this.outputChannels,streamMode:r},i=[];for await(let t of(await this.stream(e,a)))i.push(t);return"values"===r?i[i.length-1]:i}}class iM extends ag{constructor(e=!0){super(),Object.defineProperty(this,"lc_graph_name",{enumerable:!0,configurable:!0,writable:!0,value:"EphemeralValue"}),Object.defineProperty(this,"guard",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.guard=e}fromCheckpoint(e){let t=new iM(this.guard);return e&&(t.value=e),t}update(e){if(0===e.length){let e=void 0!==this.value;return this.value=void 0,e}if(1!==e.length&&this.guard)throw new ee("EphemeralValue can only receive one value per step.");return this.value=e[e.length-1],!0}get(){if(void 0===this.value)throw new Z;return this.value}checkpoint(){if(void 0===this.value)throw new Z;return this.value}}let iR="__start__",iL="__end__";class iD{constructor(e){Object.defineProperty(this,"condition",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"ends",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.condition=e.path,this.ends=Array.isArray(e.pathMap)?e.pathMap.reduce((e,t)=>(e[t]=t,e),{}):e.pathMap}compile(e,t){return a4.registerWriter(new aX({trace:!1,func:async(r,a)=>{try{return await this._route(r,a,e,t)}catch(e){throw e.name===H.unminifiable_name&&console.warn("[WARN]: 'NodeInterrupt' thrown in conditional edge. This is likely a bug in your graph implementation.\nNodeInterrupt should only be thrown inside a node, not in edge conditions."),e}}}))}async _route(e,t,r,a){let i,s=await this.condition(a?a(t):e,t);if(Array.isArray(s)||(s=[s]),(i=this.ends?s.map(e=>aL(e)?e:this.ends[e]):s).some(e=>!e))throw Error("Branch condition returned unknown or null destination");if(i.filter(aL).some(e=>e.node===iL))throw new ee("Cannot send a packet to the END node");return r(i)}}class iF{constructor(){Object.defineProperty(this,"nodes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"edges",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"branches",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"entryPoint",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"compiled",{enumerable:!0,configurable:!0,writable:!0,value:!1}),this.nodes={},this.edges=new Set,this.branches={}}warnIfCompiled(e){this.compiled&&console.warn(e)}get allEdges(){return this.edges}addNode(e,t,r){for(let t of["|",":"])if(e.includes(t))throw Error(`"${t}" is a reserved character and is not allowed in node names.`);if(this.warnIfCompiled("Adding a node to a graph that has already been compiled. This will not be reflected in the compiled graph."),e in this.nodes)throw Error(`Node \`${e}\` already present.`);if(e===iL)throw Error(`Node \`${e}\` is reserved.`);let a=(0,e1.Bp)(t);return this.nodes[e]={runnable:a,metadata:r?.metadata,subgraphs:io(a)?[a]:r?.subgraphs},this}addEdge(e,t){if(this.warnIfCompiled("Adding an edge to a graph that has already been compiled. This will not be reflected in the compiled graph."),e===iL)throw Error("END cannot be a start node");if(t===iR)throw Error("START cannot be an end node");if(Array.from(this.edges).some(([t])=>t===e)&&!("channels"in this))throw Error(`Already found path for ${e}. For multiple edges, use StateGraph.`);return this.edges.add([e,t]),this}addConditionalEdges(e,t,r){let a="object"==typeof e?e:{source:e,path:t,pathMap:r};this.warnIfCompiled("Adding an edge to a graph that has already been compiled. This will not be reflected in the compiled graph.");let i=a.path.name||"condition";if(this.branches[a.source]&&this.branches[a.source][i])throw Error(`Condition \`${i}\` already present for node \`${e}\``);return this.branches[a.source]||(this.branches[a.source]={}),this.branches[a.source][i]=new iD(a),this}setEntryPoint(e){return this.warnIfCompiled("Setting the entry point of a graph that has already been compiled. This will not be reflected in the compiled graph."),this.addEdge(iR,e)}setFinishPoint(e){return this.warnIfCompiled("Setting a finish point of a graph that has already been compiled. This will not be reflected in the compiled graph."),this.addEdge(e,iL)}compile({checkpointer:e,interruptBefore:t,interruptAfter:r}={}){this.validate([...Array.isArray(t)?t:[],...Array.isArray(r)?r:[]]);let a=new iz({builder:this,checkpointer:e,interruptAfter:r,interruptBefore:t,autoValidate:!1,nodes:{},channels:{[iR]:new iM,[iL]:new iM},inputChannels:iR,outputChannels:iL,streamChannels:[],streamMode:"values"});for(let[e,t]of Object.entries(this.nodes))a.attachNode(e,t);for(let[e,t]of this.edges)a.attachEdge(e,t);for(let[e,t]of Object.entries(this.branches))for(let[r,i]of Object.entries(t))a.attachBranch(e,r,i);return a.validate()}validate(e){let t=new Set([...this.allEdges].map(([e,t])=>e));for(let[e]of Object.entries(this.branches))t.add(e);for(let e of t)if(e!==iR&&!(e in this.nodes))throw Error(`Found edge starting at unknown node \`${e}\``);let r=new Set([...this.allEdges].map(([e,t])=>t));for(let[e,t]of Object.entries(this.branches))for(let a of Object.values(t))if(a.ends)for(let e of Object.values(a.ends))r.add(e);else for(let t of(r.add(iL),Object.keys(this.nodes)))t!==e&&r.add(t);for(let e of Object.keys(this.nodes))if(!r.has(e))throw Error(`Node \`${e}\` is not reachable`);for(let e of r)if(e!==iL&&!(e in this.nodes))throw Error(`Found edge ending at unknown node \`${e}\``);if(e){for(let t of e)if(!(t in this.nodes))throw Error(`Interrupt node \`${t}\` is not present`)}this.compiled=!0}}class iz extends iN{constructor({builder:e,...t}){super(t),Object.defineProperty(this,"builder",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.builder=e}attachNode(e,t){this.channels[e]=new iM,this.nodes[e]=new a6({channels:[],triggers:[],metadata:t.metadata,subgraphs:t.subgraphs}).pipe(t.runnable).pipe(new a4([{channel:e,value:a5}],[aI])),this.streamChannels.push(e)}attachEdge(e,t){if(t===iL){if(e===iR)throw Error("Cannot have an edge from START to END");this.nodes[e].writers.push(new a4([{channel:iL,value:a5}],[aI]))}else this.nodes[t].triggers.push(e),this.nodes[t].channels.push(e)}attachBranch(e,t,r){for(let a of(e===iR&&this.nodes[iR]&&(this.nodes[iR]=i$.subscribeTo(iR,{tags:[aI]})),this.nodes[e].pipe(r.compile(r=>new a4(r.map(r=>aL(r)?r:{channel:r===iL?iL:`branch:${e}:${t}:${r}`,value:a5}),[aI]))),r.ends?Object.values(r.ends):Object.keys(this.nodes)))if(a!==iL){let r=`branch:${e}:${t}:${a}`;this.channels[r]=new iM,this.nodes[a].triggers.push(r),this.nodes[a].channels.push(r)}}async getGraphAsync(e){let t=e?.xray,r=new aB.T,a={[iR]:r.addNode({schema:V.z.any()},iR)},i={},s={};function n(e,t,s,o=!1){return t===iL&&void 0===i[iL]&&(i[iL]=r.addNode({schema:V.z.any()},iL)),r.addEdge(a[e],i[t],s!==t?s:void 0,o)}for(let[n,l]of(t&&(s=Object.fromEntries((await aZ(this.getSubgraphsAsync())).filter(e=>iU(e[1])))),Object.entries(this.builder.nodes))){let c=iV(n),u=l.runnable,h=l.metadata??{};if(this.interruptBefore?.includes(n)&&this.interruptAfter?.includes(n)?h.__interrupt="before,after":this.interruptBefore?.includes(n)?h.__interrupt="before":this.interruptAfter?.includes(n)&&(h.__interrupt="after"),t){let l="number"==typeof t?t-1:t,p=void 0!==s[n]?await s[n].getGraphAsync({...e,xray:l}):u.getGraph(e);if(p.trimFirstNode(),p.trimLastNode(),Object.keys(p.nodes).length>1){let[e,t]=r.extend(p,c);if(void 0===e)throw Error(`Could not extend subgraph "${n}" due to missing entrypoint.`);function o(e,t){if(void 0!==e&&!aG(e))return e;if(!t||!t.lc_runnable)return t.name??"UnknownSchema";try{let e=t.getName();return e=e.startsWith("Runnable")?e.slice(8):e}catch(e){return t.getName()}}void 0!==t&&(a[c]={name:o(t.id,t.data),...t}),i[c]={name:o(e.id,e.data),...e}}else{let e=r.addNode(u,c,h);a[c]=e,i[c]=e}}else{let e=r.addNode(u,c,h);a[c]=e,i[c]=e}}for(let[e,t]of[...this.builder.allEdges].sort(([e],[t])=>e<t?-1:+(t>e)))n(iV(e),iV(t));for(let[e,t]of Object.entries(this.builder.branches)){let r={...Object.fromEntries(Object.keys(this.builder.nodes).filter(t=>t!==e).map(e=>[iV(e),iV(e)])),[iL]:iL};for(let a of Object.values(t)){let t;for(let[t,i]of Object.entries(void 0!==a.ends?a.ends:r))n(iV(e),iV(i),t,!0)}}return r}getGraph(e){let t=e?.xray,r=new aB.T,a={[iR]:r.addNode({schema:V.z.any()},iR)},i={},s={};function n(e,t,s,o=!1){return t===iL&&void 0===i[iL]&&(i[iL]=r.addNode({schema:V.z.any()},iL)),r.addEdge(a[e],i[t],s!==t?s:void 0,o)}for(let[n,l]of(t&&(s=Object.fromEntries(a0(this.getSubgraphs()).filter(e=>iU(e[1])))),Object.entries(this.builder.nodes))){let c=iV(n),u=l.runnable,h=l.metadata??{};if(this.interruptBefore?.includes(n)&&this.interruptAfter?.includes(n)?h.__interrupt="before,after":this.interruptBefore?.includes(n)?h.__interrupt="before":this.interruptAfter?.includes(n)&&(h.__interrupt="after"),t){let l="number"==typeof t?t-1:t,p=void 0!==s[n]?s[n].getGraph({...e,xray:l}):u.getGraph(e);if(p.trimFirstNode(),p.trimLastNode(),Object.keys(p.nodes).length>1){let[e,t]=r.extend(p,c);if(void 0===e)throw Error(`Could not extend subgraph "${n}" due to missing entrypoint.`);function o(e,t){if(void 0!==e&&!aG(e))return e;if(!t||!t.lc_runnable)return t.name??"UnknownSchema";try{let e=t.getName();return e=e.startsWith("Runnable")?e.slice(8):e}catch(e){return t.getName()}}void 0!==t&&(a[c]={name:o(t.id,t.data),...t}),i[c]={name:o(e.id,e.data),...e}}else{let e=r.addNode(u,c,h);a[c]=e,i[c]=e}}else{let e=r.addNode(u,c,h);a[c]=e,i[c]=e}}for(let[e,t]of[...this.builder.allEdges].sort(([e],[t])=>e<t?-1:+(t>e)))n(iV(e),iV(t));for(let[e,t]of Object.entries(this.builder.branches)){let r={...Object.fromEntries(Object.keys(this.builder.nodes).filter(t=>t!==e).map(e=>[iV(e),iV(e)])),[iL]:iL};for(let a of Object.values(t)){let t;for(let[t,i]of Object.entries(void 0!==a.ends?a.ends:r))n(iV(e),iV(i),t,!0)}}return r}}function iU(e){return"function"==typeof e.attachNode&&"function"==typeof e.attachEdge}function iV(e){return"subgraph"===e?`"${e}"`:e}let iK=(e,t)=>e.size===t.size&&[...e].every(e=>t.has(e));class iq extends ag{constructor(e){super(),Object.defineProperty(this,"lc_graph_name",{enumerable:!0,configurable:!0,writable:!0,value:"NamedBarrierValue"}),Object.defineProperty(this,"names",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"seen",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.names=e,this.seen=new Set}fromCheckpoint(e){let t=new iq(this.names);return e&&(t.seen=new Set(e)),t}update(e){let t=!1;for(let r of e)if(this.names.has(r))this.seen.has(r)||(this.seen.add(r),t=!0);else throw new ee(`Value ${JSON.stringify(r)} not in names ${JSON.stringify(this.names)}`);return t}get(){if(!iK(this.names,this.seen))throw new Z}checkpoint(){return[...this.seen]}consume(){return!!(this.seen&&this.names&&iK(this.seen,this.names))&&(this.seen=new Set,!0)}}let iB="__root__";class iY extends iF{constructor(e,t){if(super(),Object.defineProperty(this,"channels",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"waitingEdges",{enumerable:!0,configurable:!0,writable:!0,value:new Set}),Object.defineProperty(this,"_schemaDefinition",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_inputDefinition",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_outputDefinition",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_schemaDefinitions",{enumerable:!0,configurable:!0,writable:!0,value:new Map}),Object.defineProperty(this,"_configSchema",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),function(e){return"object"==typeof e&&null!==e&&void 0===e.stateSchema&&void 0!==e.input&&void 0!==e.output}(e))this._schemaDefinition=e.input.spec,this._inputDefinition=e.input.spec,this._outputDefinition=e.output.spec;else if(function(e){return"object"==typeof e&&null!==e&&void 0!==e.stateSchema}(e))this._schemaDefinition=e.stateSchema.spec,this._inputDefinition=e.input?.spec??this._schemaDefinition,this._outputDefinition=e.output?.spec??this._schemaDefinition;else if(function(e){return"object"==typeof e&&null!==e&&!Array.isArray(e)&&Object.keys(e).length>0&&Object.values(e).every(e=>"function"==typeof e||am(e))}(e)||iW(e)){let t=iW(e)?e.spec:e;this._schemaDefinition=t}else if(function(e){return"object"==typeof e&&null!==e&&void 0!==e.channels}(e)){let t=function(e){let t={};for(let[r,a]of Object.entries(e))t[r]=aq(a);return t}(e.channels);this._schemaDefinition=t}else throw Error("Invalid StateGraph input.");this._inputDefinition=this._inputDefinition??this._schemaDefinition,this._outputDefinition=this._outputDefinition??this._schemaDefinition,this._addSchema(this._schemaDefinition),this._addSchema(this._inputDefinition),this._addSchema(this._outputDefinition),this._configSchema=t?.spec}get allEdges(){return new Set([...this.edges,...Array.from(this.waitingEdges).flatMap(([e,t])=>e.map(e=>[e,t]))])}_addSchema(e){if(!this._schemaDefinitions.has(e))for(let[t,r]of(this._schemaDefinitions.set(e,e),Object.entries(e))){let e;if(e="function"==typeof r?r():r,void 0!==this.channels[t]){if(this.channels[t]!==e&&!az(e)&&"LastValue"!==e.lc_graph_name)throw Error(`Channel "${t}" already exists with a different type.`)}else this.channels[t]=e}}addNode(e,t,r){let a;if(e in this.channels)throw Error(`${e} is already being used as a state attribute (a.k.a. a channel), cannot also be used as a node name.`);for(let t of["|",":"])if(e.includes(t))throw Error(`"${t}" is a reserved character and is not allowed in node names.`);if(this.warnIfCompiled("Adding a node to a graph that has already been compiled. This will not be reflected in the compiled graph."),e in this.nodes)throw Error(`Node \`${e}\` already present.`);if(e===iL||e===iR)throw Error(`Node \`${e}\` is reserved.`);r?.input!==void 0&&this._addSchema(r.input.spec);let i={runnable:a=e1.YN.isRunnable(t)?t:"function"==typeof t?new aX({func:t,name:e,trace:!1}):(0,e1.Bp)(t),retryPolicy:r?.retryPolicy,metadata:r?.metadata,input:r?.input?.spec??this._schemaDefinition,subgraphs:io(a)?[a]:r?.subgraphs};return this.nodes[e]=i,this}addEdge(e,t){if("string"==typeof e)return super.addEdge(e,t);for(let t of(this.compiled&&console.warn("Adding an edge to a graph that has already been compiled. This will not be reflected in the compiled graph."),e)){if(t===iL)throw Error("END cannot be a start node");if(!Object.keys(this.nodes).some(e=>e===t))throw Error(`Need to add a node named "${t}" first`)}if(t===iL)throw Error("END cannot be an end node");if(!Object.keys(this.nodes).some(e=>e===t))throw Error(`Need to add a node named "${t}" first`);return this.waitingEdges.add([e,t]),this}compile({checkpointer:e,store:t,interruptBefore:r,interruptAfter:a}={}){this.validate([...Array.isArray(r)?r:[],...Array.isArray(a)?a:[]]);let i=Object.keys(this._schemaDefinitions.get(this._outputDefinition)),s=1===i.length&&i[0]===iB?iB:i,n=Object.keys(this.channels),o=1===n.length&&n[0]===iB?iB:n,l=new iG({builder:this,checkpointer:e,interruptAfter:a,interruptBefore:r,autoValidate:!1,nodes:{},channels:{...this.channels,[iR]:new iM},inputChannels:iR,outputChannels:s,streamChannels:o,streamMode:"updates",store:t});for(let[e,t]of(l.attachNode(iR),Object.entries(this.nodes)))l.attachNode(e,t);for(let[e,t]of this.edges)l.attachEdge(e,t);for(let[e,t]of this.waitingEdges)l.attachEdge(e,t);for(let[e,t]of Object.entries(this.branches))for(let[r,a]of Object.entries(t))l.attachBranch(e,r,a);return l.validate()}}class iG extends iz{attachNode(e,t){function r(e,t){if(!t)return a2;if(!("object"!=typeof t||Array.isArray(t)))return e in t?t[e]:a2;{let r=Array.isArray(t)?"array":typeof t;throw new ee(`Expected node "${e.toString()}" to return an object, received ${r}`,{lc_error_code:"INVALID_GRAPH_NODE_RETURN_VALUE"})}}let a=Object.keys(this.builder.channels).map(e=>e===iB?{channel:e,value:a5,skipNone:!0}:{channel:e,value:a5,mapper:new aX({func:r.bind(null,e),trace:!1,recurse:!1})});if(e===iR)this.nodes[e]=new a6({tags:[aI],triggers:[iR],channels:[iR],writers:[new a4(a,[aI])]});else{let r=t?.input??this.builder._schemaDefinition,i=Object.fromEntries(Object.keys(this.builder._schemaDefinitions.get(r)).map(e=>[e,e])),s=1===Object.keys(i).length&&iB in i;this.channels[e]=new iM(!1),this.nodes[e]=new a6({triggers:[],channels:s?Object.keys(i):i,writers:[new a4(a.concat({channel:e,value:e}),[aI])],mapper:s?void 0:e=>Object.fromEntries(Object.entries(e).filter(([e])=>e in i)),bound:t?.runnable,metadata:t?.metadata,retryPolicy:t?.retryPolicy,subgraphs:t?.subgraphs})}}attachEdge(e,t){if(t!==iL)if(Array.isArray(e)){let r=`join:${e.join("+")}:${t}`;for(let a of(this.channels[r]=new iq(new Set(e)),this.nodes[t].triggers.push(r),e))this.nodes[a].writers.push(new a4([{channel:r,value:a}],[aI]))}else if(e===iR){let e=`${iR}:${t}`;this.channels[e]=new iM,this.nodes[t].triggers.push(e),this.nodes[iR].writers.push(new a4([{channel:e,value:iR}],[aI]))}else this.nodes[t].triggers.push(e)}attachBranch(e,t,r){for(let a of(this.nodes[e].writers.push(r.compile(r=>{let a=r.filter(e=>e!==iL);if(a.length)return new a4(a.map(r=>aL(r)?r:{channel:`branch:${e}:${t}:${r}`,value:e}),[aI])},e=>a3.doRead(e,this.streamChannels??this.outputChannels,!0))),r.ends?Object.values(r.ends):Object.keys(this.builder.nodes))){if(a===iL)continue;let r=`branch:${e}:${t}:${a}`;this.channels[r]=new iM(!1),this.nodes[a].triggers.push(r)}}}function iW(e){return"object"==typeof e&&null!==e&&"lc_graph_name"in e&&"AnnotationRoot"===e.lc_graph_name}let iJ={randomUUID:en().randomUUID},iH=new Uint8Array(256),iX=iH.length,iQ=[];for(let e=0;e<256;++e)iQ.push((e+256).toString(16).slice(1));let iZ=function(e,t,r){if(iJ.randomUUID&&!t&&!e)return iJ.randomUUID();let a=(e=e||{}).random||(e.rng||function(){return iX>iH.length-16&&(en().randomFillSync(iH),iX=0),iH.slice(iX,iX+=16)})();if(a[6]=15&a[6]|64,a[8]=63&a[8]|128,t){r=r||0;for(let e=0;e<16;++e)t[r+e]=a[e];return t}return function(e,t=0){return(iQ[e[t+0]]+iQ[e[t+1]]+iQ[e[t+2]]+iQ[e[t+3]]+"-"+iQ[e[t+4]]+iQ[e[t+5]]+"-"+iQ[e[t+6]]+iQ[e[t+7]]+"-"+iQ[e[t+8]]+iQ[e[t+9]]+"-"+iQ[e[t+10]]+iQ[e[t+11]]+iQ[e[t+12]]+iQ[e[t+13]]+iQ[e[t+14]]+iQ[e[t+15]]).toLowerCase()}(a)};function i0(e,t){let r=Array.isArray(e)?e:[e],a=Array.isArray(t)?t:[t],i=r.map(eR.coerceMessageLikeToMessage),s=a.map(eR.coerceMessageLikeToMessage);for(let e of i)(null===e.id||void 0===e.id)&&(e.id=iZ(),e.lc_kwargs.id=e.id);for(let e of s)(null===e.id||void 0===e.id)&&(e.id=iZ(),e.lc_kwargs.id=e.id);let n=new Map(i.map((e,t)=>[e.id,t])),o=[...i],l=new Set;for(let e of s){let t=n.get(e.id);if(void 0!==t)"remove"===e._getType()?l.add(e.id):o[t]=e;else{if("remove"===e._getType())throw Error(`Attempting to delete a message with an ID that doesn't exist ('${e.id}')`);o.push(e)}}return o.filter(e=>!l.has(e.id))}aK.Root({messages:aK({reducer:i0,default:()=>[]})}),q.N.initializeGlobalInstance(new B.AsyncLocalStorage),e1.fJ;class i1 extends aX{constructor(e,t){let{name:r,tags:a,handleToolErrors:i}=t??{};super({name:r,tags:a,func:(e,t)=>this.run(e,t)}),Object.defineProperty(this,"tools",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"handleToolErrors",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"trace",{enumerable:!0,configurable:!0,writable:!0,value:!1}),this.tools=e,this.handleToolErrors=i??this.handleToolErrors}async run(e,t){let r=Array.isArray(e)?e[e.length-1]:e.messages[e.messages.length-1];if(r?._getType()!=="ai")throw Error("ToolNode only accepts AIMessages as input.");let a=await Promise.all(r.tool_calls?.map(async e=>{let r=this.tools.find(t=>t.name===e.name);try{if(void 0===r)throw Error(`Tool "${e.name}" not found.`);let a=await r.invoke({...e,type:"tool_call"},t);if((0,eR.isBaseMessage)(a)&&"tool"===a._getType())return a;return new eR.ToolMessage({name:r.name,content:"string"==typeof a?a:JSON.stringify(a),tool_call_id:e.id})}catch(t){if(!this.handleToolErrors)throw t;return new eR.ToolMessage({content:`Error: ${t.message}
 Please fix your mistakes.`,name:e.name,tool_call_id:e.id??""})}})??[]);return Array.isArray(e)?a:{messages:a}}}class i2 extends tc{constructor(e){super({}),this.apiKey=e.apiKey,this.temperature=e.temperature??.7,this.maxTokens=e.maxTokens??2e3,this.streaming=e.streaming??!1}_llmType(){return"rokey-llm"}convertMessagesToRouKeyFormat(e){return e.map(e=>e instanceof eR.HumanMessage?{role:"user",content:e.content}:e instanceof eR.AIMessage?{role:"assistant",content:e.content}:e instanceof eR.SystemMessage?{role:"system",content:e.content}:{role:"user",content:e.content})}async _generate(e,t,r){try{let r={messages:this.convertMessagesToRouKeyFormat(e),temperature:this.temperature,max_tokens:this.maxTokens,stream:this.streaming,...t},a=(0,z.Y)(this.apiKey.encrypted_api_key),i=await sI(this.apiKey.provider,this.apiKey.predefined_model_id,a,r);if(!i.success||!i.responseData)throw Error(`RouKey LLM call failed: ${i.error||"Unknown error"}`);let s=i.responseData.choices?.[0]?.message?.content||"",n=i.responseData.choices?.[0]?.finish_reason||"stop";return{generations:[{message:new eR.AIMessage(s),text:s,generationInfo:{finishReason:n,usage:i.responseData.usage,provider:this.apiKey.provider,model:this.apiKey.predefined_model_id}}],llmOutput:{tokenUsage:i.responseData.usage,provider:this.apiKey.provider,model:this.apiKey.predefined_model_id}}}catch(e){throw Error(`RouKey LLM failed: ${e instanceof Error?e.message:"Unknown error"}`)}}async *_streamResponseChunks(e,t,r){let a=await this._generate(e,t,r);a.generations.length>0&&(yield{chunk:a.generations[0].message,generationInfo:a.generations[0].generationInfo})}get identifyingParams(){return{provider:this.apiKey.provider,model:this.apiKey.predefined_model_id,temperature:this.temperature,maxTokens:this.maxTokens,apiKeyId:this.apiKey.id}}withConfig(e){return new i2({apiKey:e.apiKey??this.apiKey,temperature:e.temperature??this.temperature,maxTokens:e.maxTokens??this.maxTokens,streaming:e.streaming??this.streaming})}getModelInfo(){return`${this.apiKey.provider}/${this.apiKey.predefined_model_id} (${this.apiKey.label})`}}class i5{constructor(e){this.agents={},this.workflow=null,this.config=e}async initializeAgents(){for(let e of this.config.roles){let t=this.config.userApiKeys[e];if(!t)continue;let r=new i2({apiKey:t,temperature:this.getTemperatureForRole(e),maxTokens:this.getMaxTokensForRole(e)}),a=this.getSystemPromptForRole(e),i=this.getToolsForRole(e),s=function(e){let t,{llm:r,tools:a,messageModifier:i,checkpointSaver:s,interruptBefore:n,interruptAfter:o}=e;if(t=Array.isArray(a)?a:a.tools,!("bindTools"in r)||"function"!=typeof r.bindTools)throw Error(`llm ${r} must define bindTools method.`);let l=function(e,t){if(!t)return e;let r=e1.jY.from(e=>({messages:e}));if("string"==typeof t){let a=new eR.SystemMessage(t),i=rr.RZ.fromMessages([a,["placeholder","{messages}"]]);return r.pipe(i).pipe(e)}if("function"==typeof t)return e1.jY.from(t).withConfig({runName:"message_modifier"}).pipe(e);if(e1.YN.isRunnable(t))return t.pipe(e);if("system"===t._getType()){let a=rr.RZ.fromMessages([t,["placeholder","{messages}"]]);return r.pipe(a).pipe(e)}throw Error(`Unsupported message modifier type: ${typeof t}`)}(r.bindTools(t),i),c=async(e,t)=>{let{messages:r}=e;return{messages:[await l.invoke(r,t)]}};return new iY({channels:{messages:{value:i0,default:()=>[]}}}).addNode("agent",c).addNode("tools",new i1(t)).addEdge(iR,"agent").addConditionalEdges("agent",e=>{let{messages:t}=e,r=t[t.length-1];return(0,eR.isAIMessage)(r)&&(!r.tool_calls||0===r.tool_calls.length)?iL:"continue"},{continue:"tools",[iL]:iL}).addEdge("tools","agent").compile({checkpointer:s,interruptBefore:n,interruptAfter:o})}({llm:r,tools:i,stateModifier:new eR.SystemMessage(a)});this.agents[e]={agent:s,llm:r,role:e,systemPrompt:a,tools:i.length}}}getSystemPromptForRole(e){let t=U.p2.find(t=>t.id===e),r=t?.description||`You are an expert in ${e}.`;return({brainstorming_ideation:`${r}

You are a creative brainstorming expert. Your role in this multi-agent collaboration is to:
- Generate innovative and diverse ideas
- Think outside the box and explore unconventional approaches
- Build upon ideas from other agents
- Ask thought-provoking questions to stimulate creativity
- Provide multiple perspectives and alternatives

Always be enthusiastic, creative, and collaborative. When other agents share their work, build upon it creatively.`,writing:`${r}

You are a professional writing expert. Your role in this multi-agent collaboration is to:
- Transform ideas into well-structured, engaging content
- Adapt writing style to the target audience and purpose
- Ensure clarity, coherence, and compelling narrative flow
- Collaborate with other agents to refine and improve content
- Provide constructive feedback on written materials

Focus on creating high-quality, polished content that achieves the intended goals.`,coding_backend:`${r}

You are a backend development expert. Your role in this multi-agent collaboration is to:
- Design robust, scalable backend architectures
- Write clean, efficient, and maintainable code
- Consider security, performance, and best practices
- Collaborate with frontend developers and other team members
- Provide technical guidance and code reviews

Always prioritize code quality, security, and scalability in your solutions.`,coding_frontend:`${r}

You are a frontend development expert. Your role in this multi-agent collaboration is to:
- Create intuitive, responsive user interfaces
- Implement modern frontend technologies and best practices
- Ensure excellent user experience and accessibility
- Collaborate with backend developers and designers
- Optimize for performance and cross-browser compatibility

Focus on creating beautiful, functional, and user-friendly interfaces.`,general_chat:`You are a helpful AI assistant participating in a multi-agent collaboration. Your role is to:
- Provide general assistance and coordination
- Help bridge communication between specialized agents
- Offer balanced perspectives and common-sense insights
- Facilitate smooth collaboration between team members
- Ensure all participants stay focused on the main objectives

Be supportive, clear, and help maintain productive collaboration.`})[e]||`${r}

You are participating in a multi-agent collaboration. Work together with other agents to achieve the best possible outcome. Be collaborative, constructive, and focused on the shared goals.`}getToolsForRole(e){return[]}getTemperatureForRole(e){return({brainstorming_ideation:.9,writing:.7,coding_backend:.3,coding_frontend:.3,translation_localization:.3,summarization_briefing:.5,general_chat:.7})[e]||.7}getMaxTokensForRole(e){return({brainstorming_ideation:3e3,writing:4e3,coding_backend:3e3,coding_frontend:3e3,translation_localization:2e3,summarization_briefing:2e3,general_chat:2e3})[e]||2e3}determineWorkflowType(){let e=this.config.roles.length;return this.config.workflowType?this.config.workflowType:2===e?"sequential":e<=3?"supervisor":"hierarchical"}async buildWorkflow(e){switch(e){case"sequential":this.workflow=this.buildSequentialWorkflow();break;case"parallel":this.workflow=this.buildParallelWorkflow();break;case"supervisor":this.workflow=this.buildSupervisorWorkflow();break;case"hierarchical":this.workflow=this.buildHierarchicalWorkflow();break;default:throw Error(`Unknown workflow type: ${e}`)}}buildSequentialWorkflow(){let e=new iY({channels:{messages:{reducer:(e,t)=>e.concat(t)},next:{default:()=>void 0},currentRole:{default:()=>void 0},taskProgress:{default:()=>({})},results:{default:()=>({})},metadata:{default:()=>({})},conversationId:{default:()=>this.config.conversationId},userId:{default:()=>this.config.userId}}});this.config.roles.forEach((t,r)=>{e.addNode(t,this.createAgentNode(t))}),e.addEdge(iR,this.config.roles[0]);for(let t=0;t<this.config.roles.length-1;t++)e.addEdge(this.config.roles[t],this.config.roles[t+1]);return e.addEdge(this.config.roles[this.config.roles.length-1],iL),e.compile()}buildParallelWorkflow(){let e=new iY({channels:{messages:{reducer:(e,t)=>e.concat(t)},next:{default:()=>void 0},currentRole:{default:()=>void 0},taskProgress:{default:()=>({})},results:{default:()=>({})},metadata:{default:()=>({})},conversationId:{default:()=>this.config.conversationId},userId:{default:()=>this.config.userId}}});return this.config.roles.forEach(t=>{e.addNode(t,this.createAgentNode(t))}),e.addNode("merger",this.createMergerNode()),this.config.roles.forEach(t=>{e.addEdge(iR,t),e.addEdge(t,"merger")}),e.addEdge("merger",iL),e.compile()}buildSupervisorWorkflow(){let e=new iY({channels:{messages:{reducer:(e,t)=>e.concat(t)},next:{default:()=>void 0},currentRole:{default:()=>void 0},taskProgress:{default:()=>({})},results:{default:()=>({})},metadata:{default:()=>({})},conversationId:{default:()=>this.config.conversationId},userId:{default:()=>this.config.userId}}}),t=this.determineSupervisorRole();e.addNode("supervisor",this.createSupervisorNode(t));let r=this.config.roles.filter(e=>e!==t);return r.forEach(t=>{e.addNode(t,this.createAgentNode(t)),e.addEdge(t,"supervisor")}),e.addConditionalEdges("supervisor",this.createSupervisorRouter(r)),e.addEdge(iR,"supervisor"),e.compile()}buildHierarchicalWorkflow(){return this.buildSupervisorWorkflow()}async execute(){let e=Date.now();try{await this.initializeAgents();let t=this.determineWorkflowType();await this.buildWorkflow(t);let r=await this.executeWorkflow(),a=Date.now()-e;return{success:!0,finalResponse:r.finalResponse,roleContributions:r.roleContributions,metadata:{totalTokens:r.totalTokens,executionTime:a,workflowType:t,rolesUsed:this.config.roles},conversationHistory:r.conversationHistory}}catch(t){return{success:!1,finalResponse:`Orchestration failed: ${t instanceof Error?t.message:"Unknown error"}`,roleContributions:{},metadata:{totalTokens:0,executionTime:Date.now()-e,workflowType:"failed",rolesUsed:this.config.roles},conversationHistory:[]}}}createAgentNode(e){return async(t,r)=>{let a=this.agents[e];if(!a)throw Error(`Agent not found for role: ${e}`);try{let i=[...t.messages];if(i.length>0){let r=i[i.length-1];if(r instanceof eR.HumanMessage){let a=`${r.content}

[Multi-Agent Context]
You are working as part of a team with the following roles: ${this.config.roles.join(", ")}
Your specific role is: ${e}
Previous work from other agents: ${Object.keys(t.results||{}).length>0?Object.entries(t.results||{}).map(([e,t])=>`${e}: ${t}`).join("\n"):"None yet"}

Please contribute your expertise while building upon any previous work.`;i[i.length-1]=new eR.HumanMessage(a)}}let s=await a.agent.invoke({messages:i},r),n=s.messages[s.messages.length-1].content;return{messages:[new eR.AIMessage({content:n,name:e})],currentRole:e,results:{...t.results,[e]:n},taskProgress:{...t.taskProgress,[e]:"completed"}}}catch(r){return{messages:[new eR.AIMessage({content:`Error in ${e}: ${r instanceof Error?r.message:"Unknown error"}`,name:e})],currentRole:e,results:{...t.results,[e]:`Error: ${r instanceof Error?r.message:"Unknown error"}`},taskProgress:{...t.taskProgress,[e]:"failed"}}}}}createMergerNode(){return async(e,t)=>{let r=e.results||{},a=Object.entries(r).map(([e,t])=>`**${e.toUpperCase()}:**
${t}`).join("\n\n---\n\n"),i=`# Multi-Agent Collaboration Results

${a}

---

**Summary:** This response was collaboratively created by ${this.config.roles.length} specialized agents, each contributing their expertise to provide you with a comprehensive and well-rounded answer.`;return{messages:[new eR.AIMessage({content:i,name:"merger"})],currentRole:"merger",results:{...r,final:i}}}}determineSupervisorRole(){return this.config.roles.includes("general_chat")?"general_chat":this.config.roles[0]}createSupervisorNode(e){return async(t,r)=>{let a,i=this.agents[e];if(!i)throw Error(`Supervisor agent not found for role: ${e}`);let s=this.config.roles.filter(t=>t!==e),n=Object.keys(t.results||{}).filter(t=>t!==e),o=s.filter(e=>!n.includes(e));a=0===o.length?`All team members have completed their work. Please create a comprehensive final response that synthesizes all contributions:

${Object.entries(t.results||{}).filter(([t])=>t!==e).map(([e,t])=>`**${e}:** ${t}`).join("\n\n")}

Create a polished, cohesive final response that incorporates the best insights from each team member.`:`You are coordinating a multi-agent team. Here's the current status:

Original request: ${this.config.originalPrompt}

Available team members: ${s.join(", ")}
Completed work: ${n.join(", ")||"None yet"}
Remaining work: ${o.join(", ")}

${n.length>0?`Previous contributions:
${Object.entries(t.results||{}).filter(([t])=>t!==e).map(([e,t])=>`**${e}:** ${t}`).join("\n\n")}`:""}

Decide which team member should work next. Respond with just the role name: ${o.join(" or ")}.`;try{let s=await i.agent.invoke({messages:[...t.messages,new eR.HumanMessage(a)]},r),n=s.messages[s.messages.length-1].content;return{messages:[new eR.AIMessage({content:n,name:e})],currentRole:e,next:0===o.length?iL:this.parseNextRole(n,o),results:{...t.results,[e]:n}}}catch(t){return{messages:[new eR.AIMessage({content:`Supervisor error: ${t instanceof Error?t.message:"Unknown error"}`,name:e})],currentRole:e,next:iL}}}}createSupervisorRouter(e){return t=>{let r=t.next;return r!==iL&&r&&e.includes(r)?r:iL}}parseNextRole(e,t){let r=e.toLowerCase();for(let e of t)if(r.includes(e.toLowerCase()))return e;return t[0]||iL}async executeWorkflow(){if(!this.workflow)throw Error("Workflow not built");let e={messages:[new eR.HumanMessage(this.config.originalPrompt)],conversationId:this.config.conversationId,userId:this.config.userId,results:{},taskProgress:{},metadata:{}},t=[];try{let r;for await(let r of(await this.workflow.stream(e,{recursionLimit:this.config.maxIterations||10})))r&&"object"==typeof r&&(Object.assign(e,r),r.messages&&t.push(...r.messages),Object.keys(r)[0]);let a=e.results||{};if(a.final)r=a.final;else if(a.merger)r=a.merger;else{let e=Object.entries(a).map(([e,t])=>`**${e.toUpperCase()}:**
${t}`).join("\n\n---\n\n");r=`# Multi-Agent Collaboration Results

${e}`}return{finalResponse:r,roleContributions:a,totalTokens:0,conversationHistory:t}}catch(e){throw e}}}class i9{constructor(e,t){this.config=e,this.streamingCallback=t}async execute(){try{this.validateConfiguration();let e=this.determineWorkflowType(),t={roles:this.config.roles,userApiKeys:this.config.userApiKeys,originalPrompt:this.config.originalPrompt,conversationId:this.config.conversationId,userId:this.config.userId,workflowType:e,maxIterations:this.config.preferences?.maxIterations||10,enableMemory:this.config.preferences?.enableMemory||!1},r=new i5(t);if(this.config.preferences?.enableStreaming&&this.streamingCallback)return await this.executeWithStreaming(r);return await r.execute()}catch(e){return{success:!1,finalResponse:`Multi-role orchestration failed: ${e instanceof Error?e.message:"Unknown error"}`,roleContributions:{},metadata:{totalTokens:0,executionTime:0,workflowType:"failed",rolesUsed:this.config.roles},conversationHistory:[]}}}validateConfiguration(){if(!this.config.roles||0===this.config.roles.length)throw Error("No roles specified for orchestration");if(!this.config.userApiKeys||0===Object.keys(this.config.userApiKeys).length)throw Error("No API keys provided for orchestration");if(!this.config.originalPrompt||0===this.config.originalPrompt.trim().length)throw Error("No prompt provided for orchestration");if(this.config.roles.filter(e=>!this.config.userApiKeys[e]).length>0&&(this.config.roles=this.config.roles.filter(e=>this.config.userApiKeys[e]),0===this.config.roles.length))throw Error("No valid API keys found for any of the specified roles")}determineWorkflowType(){if(this.config.preferences?.workflowType&&"auto"!==this.config.preferences.workflowType)return this.config.preferences.workflowType;let e=this.config.roles.length,t=this.config.preferences?.complexity||this.analyzeComplexity();return 1===e||2===e?"sequential":e<=3&&"simple"===t?"parallel":e<=4?"supervisor":"hierarchical"}analyzeComplexity(){let e=this.config.originalPrompt.toLowerCase(),t=["analyze","research","comprehensive","detailed","in-depth","compare","evaluate","assess","strategy","plan","architecture","design","implement","develop"].filter(t=>e.includes(t)).length,r=["write","create","generate","make","simple","quick","brief","short","basic","easy"].filter(t=>e.includes(t)).length;return t>r&&t>=2?"complex":r>t?"simple":"medium"}async executeWithStreaming(e){this.streamingCallback?.onWorkflowProgress&&this.streamingCallback.onWorkflowProgress({completed:[],remaining:this.config.roles});let t=await e.execute();return this.streamingCallback?.onFinalResult&&this.streamingCallback.onFinalResult(t),t}getWorkflowRecommendations(){let e,t,r=this.config.roles.length,a=this.analyzeComplexity(),i=[];return r<=2?(e="sequential",i=["parallel"],t=`With ${r} role(s), sequential execution ensures proper collaboration and context sharing.`):r<=3&&"simple"===a?(e="parallel",i=["sequential","supervisor"],t=`For ${r} roles with ${a} complexity, parallel execution maximizes efficiency.`):r<=4?(e="supervisor",i=["sequential","hierarchical"],t=`With ${r} roles, a supervisor pattern provides optimal coordination and quality control.`):(e="hierarchical",i=["supervisor"],t=`For ${r} roles, hierarchical coordination prevents chaos and ensures structured collaboration.`),{recommended:e,alternatives:i,reasoning:t}}static async executeMultiRole(e,t,r,a){let i=new i9({roles:e,userApiKeys:t,originalPrompt:r,conversationId:a?.conversationId,userId:a?.userId,preferences:{workflowType:a?.workflowType||"auto",enableStreaming:a?.enableStreaming||!1}});return await i.execute()}}class i4{constructor(){if(this.currentKeyIndex=0,this.keyUsage=new Map,this.baseUrl="https://api.jina.ai/v1/classify",this.model="jina-clip-v2",this.multiRoleThreshold=.15,this.singleRoleThreshold=.05,this.apiKeys=[process.env.JINA_API_KEY,process.env.JINA_API_KEY_2,process.env.JINA_API_KEY_3,process.env.JINA_API_KEY_4,process.env.JINA_API_KEY_5,process.env.JINA_API_KEY_6,process.env.JINA_API_KEY_7,process.env.JINA_API_KEY_9,process.env.JINA_API_KEY_10].filter(Boolean),0===this.apiKeys.length)throw Error("No Jina API keys found in environment variables");this.apiKeys.forEach(e=>{this.keyUsage.set(e,{requests:0,tokens:0,lastUsed:new Date,errors:0})})}getNextKey(){let e=this.apiKeys[this.currentKeyIndex];return this.currentKeyIndex=(this.currentKeyIndex+1)%this.apiKeys.length,e}updateKeyUsage(e,t,r=!1){let a=this.keyUsage.get(e);a&&(a.requests++,a.tokens+=t,a.lastUsed=new Date,r&&(a.errors++,a.lastError=new Date))}createRoleLabels(e){return e.map(e=>{let t=e.role_name||e.name||e.id,r=e.description||"";return r?`${t}: ${r}`:t})}createRoleMapping(e){let t={};return e.forEach(e=>{let r=e.id,a=e.role_name||e.name||e.id,i=e.description||"",s=i?`${a}: ${i}`:a;t[r]=s}),t}async classifyPrompt(e,t,r=[]){if(!e||0===t.length)return null;let a=this.getNextKey(),i=this.createRoleMapping(t),s=this.createRoleLabels(t);try{let r=await fetch(this.baseUrl,{method:"POST",headers:{Authorization:`Bearer ${a}`,"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({model:this.model,input:[{text:e}],labels:s})});if(!r.ok)throw Error(`Jina Classification API error: ${r.status} ${r.statusText}`);let n=await r.json();if(this.updateKeyUsage(a,n.usage?.total_tokens||0),!n.data||0===n.data.length)return null;let o=(n.data[0].predictions||[]).map(e=>{let r=Object.entries(i).find(([t,r])=>r===e.label),a=r?r[0]:null,s=t.find(e=>e.id===a);return{roleId:a||e.label,confidence:e.score,role:s}}).filter(e=>e.roleId&&e.role);if(o.sort((e,t)=>t.confidence-e.confidence),/^(hi|hello|hey|sup|yo|greetings?)\.?$/i.test(e.trim())){let e=o.find(e=>"general_chat"===e.roleId);if(e&&e.confidence>=this.singleRoleThreshold)return{roleId:"general_chat",confidence:Math.max(e.confidence,.9)}}let l=o.filter(e=>e.confidence>=this.multiRoleThreshold);if(l.length>1)return{roleId:l[0].roleId,confidence:l[0].confidence,isMultiRole:!0,roles:l.map((e,t)=>({roleId:e.roleId,confidence:e.confidence,executionOrder:t+1})),reasoning:`Multi-role task detected requiring: ${l.map(e=>e.roleId).join(", ")}`};if(o.length>0&&o[0].confidence>=this.singleRoleThreshold)return{roleId:o[0].roleId,confidence:o[0].confidence};return null}catch(e){throw this.updateKeyUsage(a,0,!0),e}}getUsageStats(){return new Map(this.keyUsage)}getCurrentKeyIndex(){return this.currentKeyIndex}}let i3=new i4;var i8=r(55511),i6=r.n(i8),i7=r(2842),se=r(55591),st=r.n(se),sr=r(81630),sa=r.n(sr),si=r(79551);let ss=new Map,sn=new Map,so=new Map;function sl(e,t){sn.has(e)||sn.set(e,{});let r=sn.get(e);r[t]||(r[t]={count:0,lastUsed:0}),r[t].count++,r[t].lastUsed=Date.now()}let sc=new Map;async function su(e,t,r,a,i=[]){let s=sm(e);if(sc.has(s))try{return await sc.get(s)}catch(e){sc.delete(s)}let n=sh(e,t,r,a,i);sc.set(s,n);try{return await n}catch(e){throw e}finally{sc.delete(s)}}async function sh(e,t,r,a,i=[]){try{let r=await i3.classifyPrompt(e,t,i);if(!r)throw Error("Jina classification returned null result");return r.isMultiRole,r}catch(e){if(t.length>0)return{roleId:t[0].id,confidence:.1};throw Error(`Classification failed and no fallback roles available: ${e}`)}}let sp=new Map,sd=new Map,sf=new Map;function sm(e){return i6().createHash("md5").update(e.toLowerCase().trim()).digest("hex")}function sg(e,t,r){let a=e.map(e=>`${e.role}:${"string"==typeof e.content?e.content:JSON.stringify(e.content)}`).join("|"),i=`${t}|${a}|${r||0}`;return i6().createHash("md5").update(i).digest("hex")}function sb(e,t,r,a,i,s){if(r&&a&&i&&t.custom_api_config_id){let e=`${t.custom_api_config_id}:${t.messages?.[t.messages.length-1]?.content?.substring(0,100)||"default"}`;setImmediate(()=>{sf.set(e,{provider:r,model:a,apiKey:i,timestamp:Date.now()})})}let n={};if(s?.roleUsed&&(n["X-RoKey-Role-Used"]=s.roleUsed),s?.routingStrategy&&(n["X-RoKey-Routing-Strategy"]=s.routingStrategy),s?.complexityLevel&&(n["X-RoKey-Complexity-Level"]=s.complexityLevel.toString()),r&&(n["X-RoKey-API-Key-Provider"]=r),s?.processingTime&&(n["X-RoKey-Processing-Time"]=`${s.processingTime}ms`),t.stream&&e.response){let t={...Object.fromEntries(e.response.headers.entries()),...n};return new Response(e.response.body,{status:e.response.status,headers:t})}if(t.stream||void 0===e.responseData)throw Error("Invalid provider result: no response data available");{let t={...e.responseHeaders||{},...n};return L.NextResponse.json(e.responseData,{status:e.status||200,headers:t})}}async function sy(e,t,r){let a=`${e}_${t}`,i=so.get(a);if(i&&Date.now()-i.timestamp<9e5)return{customRoles:i.customRoles,roleAssignments:i.roleAssignments,apiKeys:i.apiKeys};try{let[i,s,n]=await Promise.allSettled([r.from("user_custom_roles").select("role_id, name, description").eq("user_id",e),r.from("api_key_role_assignments").select("role_name, api_key_id").eq("custom_api_config_id",t),r.from("api_keys").select("*").eq("custom_api_config_id",t).eq("status","active")]),o="fulfilled"===i.status&&i.value.data||[],l="fulfilled"===s.status&&s.value.data||[],c="fulfilled"===n.status&&n.value.data||[];return so.set(a,{customRoles:o,roleAssignments:l,apiKeys:c,timestamp:Date.now()}),{customRoles:o,roleAssignments:l,apiKeys:c}}catch(e){return null}}async function s_(e){try{let t=i7.trainingDataCache.get(e);if(t)return{trainingData:t.data,trainingJobId:t.jobId};let r=await (0,D.createSupabaseServerClientOnRequest)(),{data:a,error:i}=await r.from("training_jobs").select("id, training_data, created_at").eq("custom_api_config_id",e).eq("status","completed").order("created_at",{ascending:!1}).limit(1).single();if(i&&"PGRST116"!==i.code||!a?.training_data)return null;return i7.trainingDataCache.set(e,a.training_data,a.id),{trainingData:a.training_data,trainingJobId:a.id}}catch(e){return null}}async function sv(e,t,a,i){try{let{jinaEmbeddings:s}=await r.e(8108).then(r.bind(r,88108)),n=await s.embedQuery(e),{data:o,error:l}=await i.rpc("search_document_chunks",{query_embedding:n,config_id:t,user_id_param:a,match_threshold:.5,match_count:8});if(l)return{context:"",sources:[]};if(!o||0===o.length){let{data:e,error:r}=await i.rpc("search_document_chunks",{query_embedding:n,config_id:t,user_id_param:a,match_threshold:.3,match_count:5});if(r||!e||0===e.length)return{context:"",sources:[]};o=e}o.forEach((e,t)=>{});let c=[...new Set(o.map(e=>e.document_id))],{data:u}=await i.from("documents").select("id, filename").in("id",c),h=o.map((e,t)=>`[Document ${t+1} - Similarity: ${e.similarity.toFixed(3)}]
${e.content.trim()}`),p=o.map(e=>{let t=u?.find(t=>t.id===e.document_id);return{filename:t?.filename||"Unknown Document",document_id:e.document_id,similarity:Math.round(100*e.similarity)/100}});return{context:h.join("\n\n"),sources:p}}catch(e){return{context:"",sources:[]}}}async function sw(e,t){if(!t||!t.processed_prompts)return e;let{processed_prompts:r}=t,a="";if(r.system_instructions?.trim()&&(a+=`${r.system_instructions.trim()}

`),r.general_instructions?.trim()&&(a+=`${r.general_instructions.trim()}

`),r.behavior_guidelines?.trim()&&(a+=`## Behavior Guidelines:
${r.behavior_guidelines.trim()}

`),r.examples&&r.examples.length>0&&(a+=`## Training Examples:
`,r.examples.forEach((e,t)=>{a+=`Example ${t+1}:
User: ${e.input}
Assistant: ${e.output}

`})),a.trim()){a+=`---
IMPORTANT INSTRUCTIONS:
1. Follow the training examples and behavior guidelines above
2. Maintain the personality and behavior patterns shown in the examples
3. Apply the system instructions and general instructions consistently

Now respond to the user following these patterns and guidelines.`;let t=[...e],r=t.findIndex(e=>"system"===e.role);if(r>=0){let e=t[r].content,i="string"==typeof e?e:Array.isArray(e)&&e.find(e=>"text"===e.type)?.text||"";t[r].content=a+"\n\n"+i}else t.unshift({role:"system",content:a});return t}return e}setInterval(function(){let e=Date.now();for(let[t,r]of(i7.trainingDataCache.cleanup(),ss.entries()))e-r.timestamp>36e5&&ss.delete(t)},6e5);let sk=V.z.object({custom_api_config_id:V.z.string().uuid({message:"custom_api_config_id must be a valid UUID."}),role:V.z.string().optional(),messages:V.z.array(V.z.object({role:V.z.enum(["user","assistant","system"]),content:V.z.any()})).min(1,{message:"Messages array cannot be empty and must contain at least one message."}),model:V.z.string().optional(),stream:V.z.boolean().optional().default(!1),temperature:V.z.number().optional(),max_tokens:V.z.number().int().positive().optional(),specific_api_key_id:V.z.string().uuid().optional()}).catchall(V.z.any());async function sx(e,t){if(!e||!t)return null;let r={model:"gemini-2.0-flash-lite",messages:[{role:"system",content:"You are a prompt complexity classification expert. Your task is to analyze the user's prompt and classify its complexity on a scale of 1 to 5, where 1 is Very Simple, 2 is Simple, 3 is Moderate, 4 is Complex, and 5 is Very Complex. Output ONLY the integer number corresponding to the complexity level. Do not provide any explanation or any other text. Just the number."},{role:"user",content:`User's original prompt: "${e}"

Classify the complexity of this prompt (1-5):`}],temperature:.1,max_tokens:10,top_p:1};try{let e=await fetch("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify(r)});if(!e.ok)return await e.json().catch(()=>({message:e.statusText})),null;let a=await e.json(),i=a.choices?.[0]?.message?.content?.trim();if(i){let e=parseInt(i,10);if(!isNaN(e)&&e>=1&&e<=5)return e}}catch(e){}return null}async function sO(e,t,r,a,i){if(!e.user_id)return{targetApiKeyData:null,roleUsedState:"missing_user_id"};let s="";if(r.messages&&r.messages.length>0){let e=r.messages[r.messages.length-1];if("user"===e.role&&"string"==typeof e.content)s=e.content;else if("user"===e.role&&Array.isArray(e.content)){let t=e.content.find(e=>"text"===e.type);t&&"string"==typeof t.text&&(s=t.text)}}if(!s&&r.prompt&&(s=r.prompt),!s)return{targetApiKeyData:null,roleUsedState:"no_prompt_for_classification"};let n=function(e){let t=function(e){let t=e.find(e=>"user"===e.role);if(t&&"string"==typeof t.content){let e=i6().createHash("md5").update(t.content.substring(0,200)).digest("hex").substring(0,12);return`conv_${e}`}return`conv_${Date.now()}`}(e);for(let[e,r]of sp.entries())if(e.startsWith(t.substring(0,15))&&Date.now()-r.lastActivity<18e5)return e;return t}(r.messages),o=sp.get(n);if(o){let i=Date.now()-o.lastActivity,l=function(e,t){let r=e.toLowerCase().trim();for(let[e,a]of Object.entries({coding_frontend:["write code","write python","write javascript","write java","write c++","code this","program this","create a script","build an app","make a website","write html","write css","write react","frontend code","client code","create component","build interface","ui code","web development"],coding_backend:["write api","create server","database code","backend code","server code","write sql","create endpoint","api development","microservice","write node","express code","django code","flask code"],data_analysis:["analyze this data","create a chart","make a graph","data analysis","statistical analysis","create visualization","data science","machine learning","pandas code","numpy analysis","plot this","visualize data"],writing:["write an article","write a blog","create content","write an essay","write documentation","create copy","marketing content","blog post","article about","essay on","content for"],translation:["translate this","translate to","convert to language","in spanish","in french","in german","in chinese","translate into"],summarization:["summarize this","create summary","tldr","brief overview","key points","main ideas","executive summary"]}))if(e!==t){for(let t of a)if(r.includes(t))return{isTransition:!0,newTaskRole:e,reasoning:`explicit_task_transition: "${t}" -> ${e}`}}let a=["now","instead","switch to","change to","help me","can you","please","i want you to","i need you to"].some(e=>r.includes(e)),i=["create","build","make","develop","design","implement","generate","produce","construct","craft","compose"].some(e=>r.includes(e));if(a&&i)return{isTransition:!0,newTaskRole:null,reasoning:"transition_keyword_with_action_verb"};for(let e of["now write","now create","now build","now make","now help","instead write","instead create","can you write","can you create","help me write","help me create","help me build"])if(r.includes(e))return{isTransition:!0,newTaskRole:null,reasoning:`strong_transition_phrase: "${e}"`};return{isTransition:!1,newTaskRole:null,reasoning:"no_transition_detected"}}(s,o.lastClassifiedRole);if(l.isTransition){if(l.newTaskRole){sl(e.user_id,l.newTaskRole),sp.set(n,{lastClassifiedRole:l.newTaskRole,messageCount:r.messages.length,lastActivity:Date.now(),confidence:.9,conversationId:n});let i=await sy(e.user_id,t,a);if(i){let e=i.roleAssignments.find(e=>e.role_name===l.newTaskRole);if(e&&e.api_key_id){let t=i.apiKeys.find(t=>t.id===e.api_key_id);if(t)return{targetApiKeyData:t,roleUsedState:`context_transition_${o.lastClassifiedRole}_to_${l.newTaskRole}`}}}}}else{let n=function(e,t,r,a){let i=e.toLowerCase().trim(),s=["continue","continue please","keep going","go on","more","more please","finish","complete","what next","then what","and then"],n=r.slice().reverse().find(e=>"assistant"===e.role),o=n&&"string"==typeof n.content&&(n.content.includes("continues")||n.content.includes("The response will continue")||n.content.length>1500);if(s.includes(i)&&a<6e5&&o)return{isContinuation:!0,confidence:.98,reasoning:"universal_continuation"};if(s.includes(i)&&a<12e4)return{isContinuation:!0,confidence:.85,reasoning:"universal_short_continuation"};let l={StoryTeller:{strong:["continue","what happens next","keep going","more story","then what"],medium:["be creative","more","and then","what about","tell me more"],weak:["go on","next","more please","continue please"]},coding_frontend:{strong:["fix this","debug this","improve this code","add feature"],medium:["make it better","optimize","refactor","enhance"],weak:["change","update","modify"]},coding_backend:{strong:["fix this","debug this","improve this code","add feature"],medium:["make it better","optimize","refactor","enhance"],weak:["change","update","modify"]},writing:{strong:["revise this","edit this","improve this","rewrite this"],medium:["make it better","enhance","polish"],weak:["change","update","fix"]},data_analysis:{strong:["analyze more","deeper analysis","what else","more insights"],medium:["explain further","elaborate","more details"],weak:["continue","more"]}}[t];if(!l)return{isContinuation:!1,confidence:0,reasoning:"no_patterns_for_role"};for(let e of l.strong)if(i.includes(e))return{isContinuation:!0,confidence:.95,reasoning:`strong_pattern_match: "${e}"`};for(let e of l.medium)if(i.includes(e))return{isContinuation:!0,confidence:.8,reasoning:`medium_pattern_match: "${e}"`};if(a<12e4){for(let e of l.weak)if(i.includes(e))return{isContinuation:!0,confidence:.6,reasoning:`weak_pattern_match: "${e}" (recent)`}}return i.length<20&&a<3e5&&["yes","no","ok","sure","please","thanks","more","again"].some(e=>i.includes(e))?{isContinuation:!0,confidence:.7,reasoning:"short_continuation_prompt"}:a<6e4?{isContinuation:!0,confidence:.65,reasoning:"very_recent_message"}:a<3e5?{isContinuation:!0,confidence:.4,reasoning:"recent_message"}:{isContinuation:!1,confidence:0,reasoning:"no_continuation_detected"}}(s,o.lastClassifiedRole,r.messages,i);if(n.isContinuation&&n.confidence>.6){o.lastActivity=Date.now(),o.messageCount++,o.confidence=n.confidence;let r=o.lastClassifiedRole;sl(e.user_id,r);let i=await sy(e.user_id,t,a);if(i){let e=i.roleAssignments.find(e=>e.role_name===r);if(e&&e.api_key_id){let t=i.apiKeys.find(t=>t.id===e.api_key_id);if(t)return{targetApiKeyData:t,roleUsedState:`contextual_continuation_${r}_confidence_${Math.round(100*n.confidence)}`}}}}}}let l=sm(s),c=`${t}_${l}`;e.user_id;let u=ss.get(c);if(u&&Date.now()-u.timestamp<36e5){let r=await sy(e.user_id,t,a);if(r)if("general_chat"===u.roleId){let e=r.apiKeys.find(e=>!0===e.is_default_general_chat_model);if(e)return{targetApiKeyData:e,roleUsedState:K.intelligentRoleRouting(u.roleId)}}else{let e=r.roleAssignments.find(e=>e.role_name===u.roleId);if(e&&e.api_key_id){let t=r.apiKeys.find(t=>t.id===e.api_key_id);if(t)return{targetApiKeyData:t,roleUsedState:K.intelligentRoleRouting(u.roleId||"general_chat")}}}}let h=await sy(e.user_id,t,a);if(!h)return{targetApiKeyData:null,roleUsedState:"ecosystem_load_failed"};let p=h.roleAssignments,d=p.map(e=>e.role_name),f=h.customRoles.map(e=>({id:e.role_id,name:e.name,description:e.description||""})),m=U.p2.map(e=>({id:e.id,name:e.name,description:e.description||""})),g=m.filter(e=>d.includes(e.id)),b=f.filter(e=>d.includes(e.id)),y=h.apiKeys.some(e=>!0===e.is_default_general_chat_model),_=d.includes("general_chat");if(y&&!_){let e=m.find(e=>"general_chat"===e.id);e&&g.push(e)}let v=[...g,...b];if(0===v.length)return{targetApiKeyData:null,roleUsedState:"no_assigned_roles_available"};let w=null;try{(w=await su(s,v,"",t,r.messages||[]))||(w={roleId:"general_chat",confidence:.1})}catch(e){w={roleId:"general_chat",confidence:.1}}if(w.isMultiRole&&w.roles&&w.roles.length>1)try{let t=w.roles.map(e=>e.roleId),a={};for(let e of t){let t=null;if("general_chat"===e)t=h.apiKeys.find(e=>!0===e.is_default_general_chat_model)||null;else{let r=p.find(t=>t.role_name===e);r&&r.api_key_id&&(t=h.apiKeys.find(e=>e.id===r.api_key_id)||null)}t&&(a[e]=t)}let i=Object.keys(a);if(i.length>=2){let t=await i9.executeMultiRole(i,a,s,{conversationId:r.conversation_id,userId:e.user_id,workflowType:"auto",enableStreaming:!1});if(t.success){let e=new ReadableStream({start(e){let r={id:`chatcmpl-${i6().randomUUID()}`,object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-agents-orchestration",choices:[{index:0,delta:{role:"assistant",content:t.finalResponse},finish_reason:"stop"}],usage:{prompt_tokens:0,completion_tokens:t.metadata.totalTokens,total_tokens:t.metadata.totalTokens}};e.enqueue(new TextEncoder().encode(`data: ${JSON.stringify(r)}

`)),e.enqueue(new TextEncoder().encode("data: [DONE]\n\n")),e.close()}});return{targetApiKeyData:null,roleUsedState:`langgraph_orchestration_${i.join("_")}`,hybridResponse:new Response(e,{headers:{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive"}})}}}}catch(e){}let k=w.roleId;if(k&&ss.set(c,{roleId:k,timestamp:Date.now()}),k){if(sl(e.user_id,k),"general_chat"===k){let e=h.apiKeys.find(e=>!0===e.is_default_general_chat_model);if(e)return{targetApiKeyData:e,roleUsedState:K.intelligentRoleRouting(k)}}let t=p.find(e=>e.role_name===k);if(t&&t.api_key_id){let e=h.apiKeys.find(e=>e.id===t.api_key_id);if(e)return{targetApiKeyData:e,roleUsedState:K.intelligentRoleRouting(k)}}}return{targetApiKeyData:null,roleUsedState:"classification_no_key_found"}}async function sP(e,t,r){let a=e?.ordered_api_key_ids;if(!Array.isArray(a)||0===a.length)return{targetApiKeyData:null,roleUsedState:"strict_fallback_no_keys_defined"};let i=a.map(async(e,a)=>{try{let{data:i,error:s}=await r.from("api_keys").select("*").eq("id",e).eq("custom_api_config_id",t).single();return{index:a,keyId:e,apiKey:i,error:s,success:!s&&i&&"active"===i.status}}catch(t){return{index:a,keyId:e,apiKey:null,error:t,success:!1}}}),s=await Promise.allSettled(i);for(let e=0;e<s.length;e++){let t=s[e];if("fulfilled"===t.status&&t.value.success){let{apiKey:r,keyId:a}=t.value;return{targetApiKeyData:r,roleUsedState:K.fallbackRouting(e)}}if("fulfilled"===t.status){let{keyId:e,apiKey:r,error:a}=t.value;a&&a.code}}return{targetApiKeyData:null,roleUsedState:"strict_fallback_no_active_key_in_list"}}async function sT(e,t,r){let a="";if(t.messages&&t.messages.length>0){let e=t.messages[t.messages.length-1];if("user"===e.role&&"string"==typeof e.content)a=e.content;else if("user"===e.role&&Array.isArray(e.content)){let t=e.content.find(e=>"text"===e.type);t&&"string"==typeof t.text&&(a=t.text)}}if(!a&&t.prompt&&(a=t.prompt),!a)return{targetApiKeyData:null,roleUsedState:"complexity_rr_no_prompt"};let i=await sx(a,"");if(null===i||i<1||i>5)return{targetApiKeyData:null,roleUsedState:"complexity_rr_invalid_classification"};let{data:s,error:n}=await r.from("config_api_key_complexity_assignments").select(`
      api_key_id,
      complexity_level,
      api_keys!inner (
        id,
        status,
        provider,
        predefined_model_id,
        is_default_general_chat_model,
        encrypted_api_key,
        label
      )
    `).eq("custom_api_config_id",e.id).eq("complexity_level",i);if(n)return{targetApiKeyData:null,roleUsedState:"complexity_rr_db_error",classifiedComplexityLevel:i,classifiedComplexityLLM:"gemini-2.0-flash-lite"};let o=s?.filter(e=>"active"===e.api_keys.status)?.map(e=>e.api_keys)||[];if(o.length>0){let t=e.routing_strategy_params||{},a=`_complexity_${i}_rr_idx`,s="number"==typeof t[a]?t[a]:0,n=[...o].sort((e,t)=>e.id.localeCompare(t.id)),l=n[s%n.length],c=(s+1)%n.length;return t[a]=c,setImmediate(async()=>{let{error:a}=await r.from("custom_api_configs").update({routing_strategy_params:t}).eq("id",e.id)}),{targetApiKeyData:l,roleUsedState:`complexity_rr_level_${i}_key_found`,classifiedComplexityLevel:i,classifiedComplexityLLM:"gemini-2.0-flash-lite"}}let l=[];for(let e=1;e<=4;e++)i-e>=1&&l.push(i-e),i+e<=5&&l.push(i+e);for(let t of l){let{data:a,error:s}=await r.from("config_api_key_complexity_assignments").select(`
        api_key_id,
        complexity_level,
        api_keys!inner (
          id,
          status,
          provider,
          predefined_model_id,
          is_default_general_chat_model,
          encrypted_api_key,
          label
        )
      `).eq("custom_api_config_id",e.id).eq("complexity_level",t);if(s)continue;let n=a?.filter(e=>"active"===e.api_keys.status)?.map(e=>e.api_keys)||[];if(n.length>0){let a=e.routing_strategy_params||{},s=`_complexity_${t}_rr_idx`,o="number"==typeof a[s]?a[s]:0,l=[...n].sort((e,t)=>e.id.localeCompare(t.id)),c=l[o%l.length],u=(o+1)%l.length;return a[s]=u,setImmediate(async()=>{let{error:t}=await r.from("custom_api_configs").update({routing_strategy_params:a}).eq("id",e.id)}),{targetApiKeyData:c,roleUsedState:`complexity_rr_level_${i}_proximal_${t}_key_found`,classifiedComplexityLevel:i,classifiedComplexityLLM:"gemini-2.0-flash-lite"}}}return{targetApiKeyData:null,roleUsedState:"complexity_rr_no_keys_found",classifiedComplexityLevel:i,classifiedComplexityLLM:"gemini-2.0-flash-lite"}}async function sE(e,t,r){let[a,i]=await Promise.allSettled([r?e.from("api_key_role_assignments").select("api_key_id").eq("custom_api_config_id",t).eq("role_name",r).single():Promise.resolve({data:null,error:null}),e.from("api_keys").select("*").eq("custom_api_config_id",t).eq("is_default_general_chat_model",!0).single()]);if(r&&"fulfilled"===a.status&&a.value.data){let t=a.value.data,{data:r,error:i}=await e.from("api_keys").select("*").eq("id",t.api_key_id).single();if(i);else if(r&&"active"===r.status)return r}else r&&a.status;if("fulfilled"===i.status&&i.value.data){let e=i.value.data;if("active"===e.status)return e}else i.status;return null}let sS={LLM_REQUEST:8e3,SOCKET:1500,GOOGLE_CLASSIFICATION:7e3},sC=new(st()).Agent({keepAlive:!0,keepAliveMsecs:12e4,maxSockets:200,maxFreeSockets:50,timeout:sS.SOCKET,scheduling:"lifo",maxTotalSockets:500}),sj=new(sa()).Agent({keepAlive:!0,keepAliveMsecs:12e4,maxSockets:200,maxFreeSockets:50,timeout:sS.SOCKET,scheduling:"lifo",maxTotalSockets:500});async function sA(e,t,a=3,i){let s,n=i||(e.includes("generativelanguage.googleapis.com")?sS.GOOGLE_CLASSIFICATION:sS.LLM_REQUEST);for(let i=1;i<=a;i++)try{return await function(e,t,a=sS.LLM_REQUEST){return new Promise((i,s)=>{let n=new si.URL(e),o="https:"===n.protocol,l=o?st():sa(),c={hostname:n.hostname,port:n.port||(o?443:80),path:n.pathname+n.search,method:t.method||"GET",headers:{...t.headers,Connection:"keep-alive","Keep-Alive":`timeout=${Math.floor(a/1e3)}, max=100`},timeout:a,agent:o?sC:sj},u=l.request(c,e=>{let t=e,a=e.headers["content-encoding"];if("gzip"===a){let a=r(74075);t=e.pipe(a.createGunzip())}else if("deflate"===a){let a=r(74075);t=e.pipe(a.createInflate())}else if("br"===a){let a=r(74075);t=e.pipe(a.createBrotliDecompress())}if(e.headers["content-type"]?.includes("text/event-stream")||e.headers["content-type"]?.includes("text/plain")||e.headers["content-type"]?.includes("application/x-ndjson"))i(new Response(new ReadableStream({start(e){t.on("data",t=>{let r=t.toString("utf8");e.enqueue(new TextEncoder().encode(r))}),t.on("end",()=>{e.close()}),t.on("error",t=>{e.error(t)})}}),{status:e.statusCode,statusText:e.statusMessage||"",headers:new Headers(e.headers)}));else{let r=[];t.on("data",e=>{r.push(Buffer.isBuffer(e)?e:Buffer.from(e))}),t.on("end",()=>{i(new Response(Buffer.concat(r).toString("utf8"),{status:e.statusCode,statusText:e.statusMessage||"",headers:new Headers(e.headers)}))})}});u.on("error",e=>{s(Error(`Network request failed: ${e.message}`))}),u.on("timeout",()=>{u.destroy(),s(Error(`Request timeout after ${a}ms`))}),t.body&&u.write(t.body),u.end()})}(e,t,n)}catch(t){if(s=t,i===a)throw t;let e=t.message.includes("timeout")?50:100*i;await new Promise(t=>setTimeout(t,e))}throw s}async function sI(e,t,a,i){let s,n,o,l,c,u=null,h=new Date;if(!i.stream&&i.messages&&t){let e=sg(i.messages,t,i.temperature),r=sd.get(e);if(r&&Date.now()-r.timestamp<12e4)return{success:!0,response:void 0,responseData:r.response,responseHeaders:new Headers({"x-rokey-cache":"hit"}),status:200,error:null,llmRequestTimestamp:new Date,llmResponseTimestamp:new Date}}let p={method:"POST",headers:{"Content-Type":"application/json",Connection:"keep-alive","Keep-Alive":"timeout=30, max=100","User-Agent":"RoKey/1.0 (Performance-Optimized)",Accept:"application/json","Cache-Control":"no-cache"}};try{let d=function(e,t){if(!e)return"";let r=t.toLowerCase(),a=`${r}/`;return e.toLowerCase().startsWith(a)?e.substring(a.length):e}(t,e||""),f=e?.toLowerCase()==="openrouter"?t:d;if(!f)throw{message:`Effective model ID is missing for provider ${e} (DB Model: ${t})`,status:500,internal:!0};if(u=new Date,e?.toLowerCase()==="openai"){let{custom_api_config_id:e,role:t,...r}=i,u={...r,model:f,messages:i.messages,stream:i.stream};Object.keys(u).forEach(e=>void 0===u[e]&&delete u[e]);let d={...p};d.headers={...p.headers,Authorization:`Bearer ${a}`,Origin:"https://rokey.app",Connection:"keep-alive","Keep-Alive":"timeout=30, max=100","Cache-Control":"no-cache",Priority:"u=1, i"},d.body=JSON.stringify(u);let m=await sA("https://api.openai.com/v1/chat/completions",d);if(h=new Date,s=m.status,c=m.headers,!m.ok){let e=await m.json().catch(()=>({error:{message:m.statusText}}));throw n={message:`OpenAI Error: ${e?.error?.message||m.statusText}`,status:m.status,provider_error:e}}if(i.stream){if(!m.body)throw n={message:"OpenAI stream body null",status:500};o=m,l={note:"streamed"}}else l=await m.json()}else if(e?.toLowerCase()==="openrouter"){let{custom_api_config_id:e,role:t,...u}=i,d={...u,model:f,messages:i.messages,stream:i.stream,usage:{include:!0}};Object.keys(d).forEach(e=>void 0===d[e]&&delete d[e]);let m={...p};m.headers={...p.headers,Authorization:`Bearer ${a}`,"HTTP-Referer":"https://rokey.app","X-Title":"RoKey",Origin:"https://rokey.app",Connection:"keep-alive","Keep-Alive":"timeout=30, max=100","Cache-Control":"no-cache",Priority:"u=1, i"},m.body=JSON.stringify(d);let g=await sA("https://openrouter.ai/api/v1/chat/completions",m);if(h=new Date,s=g.status,c=g.headers,!g.ok){let e=await g.json().catch(()=>({error:{message:g.statusText}}));throw n={message:`OpenRouter Error: ${e?.error?.message||g.statusText}`,status:g.status,provider_error:e}}if(i.stream){if(!g.body)throw n={message:"OpenRouter stream body null",status:500};let{createFirstTokenTrackingStream:e}=await r.e(9704).then(r.bind(r,99704)),t=e(g.body,"OpenRouter",f);o=new Response(t,{status:g.status,statusText:g.statusText,headers:g.headers}),l={note:"streamed"}}else l=await g.json()}else if(e?.toLowerCase()==="google"){let e=f.replace(/^models\//,""),t=i.messages.map(e=>{if("string"==typeof e.content)return{role:e.role,content:e.content};if(Array.isArray(e.content)){let t=e.content.map(e=>"text"===e.type&&"string"==typeof e.text?{type:"text",text:e.text}:"image_url"===e.type&&e.image_url?.url?{type:"image_url",image_url:{url:e.image_url.url}}:null).filter(Boolean);return{role:e.role,content:t}}return{role:e.role,content:"[RoKey: Invalid content structure for Google]"}});if(0===t.length)throw n={message:"No processable message content found for Google provider after filtering.",status:400};let u={model:e,messages:t,stream:i.stream};void 0!==i.temperature&&(u.temperature=i.temperature),void 0!==i.max_tokens&&(u.max_tokens=i.max_tokens),void 0!==i.top_p&&(u.top_p=i.top_p);let d={...p};d.headers={...p.headers,Authorization:`Bearer ${a}`,Connection:"keep-alive","Keep-Alive":"timeout=30, max=100","User-Agent":"RoKey/1.0 (Performance-Optimized)",Origin:"https://rokey.app","Cache-Control":"no-cache",Priority:"u=1, i"},d.body=JSON.stringify(u);let m=await sA("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",d);if(h=new Date,s=m.status,c=m.headers,!m.ok){let e=await m.json().catch(()=>({error:{message:m.statusText}})),t=e?.error?.message||m.statusText;throw Array.isArray(e)&&e[0]?.error?.message&&(t=e[0].error.message),n={message:`Google Error: ${t}`,status:m.status,provider_error:e}}if(i.stream){if(!m.body)throw n={message:"Google stream body null",status:500};l={note:"streamed"};let{createFirstTokenTrackingStream:t}=await r.e(9704).then(r.bind(r,99704)),a=t(m.body,"Google",e);o=new Response(a,{status:m.status,statusText:m.statusText,headers:m.headers})}else l=await m.json()}else if(e?.toLowerCase()==="anthropic"){let e,t=i.max_tokens||2048,u=i.messages.filter(t=>"system"!==t.role||("string"==typeof t.content&&(e=t.content),!1)).map(e=>({role:e.role,content:e.content}));if(0===u.length||"user"!==u[0].role)throw n={message:"Invalid messages format for Anthropic: Must contain at least one user message and start with user after system filter.",status:400};let d={model:f,messages:u,max_tokens:t,stream:i.stream};e&&(d.system=e),void 0!==i.temperature&&(d.temperature=i.temperature);let m={...p};m.headers={...p.headers,"x-api-key":a,"anthropic-version":"2023-06-01",Origin:"https://rokey.app"},m.body=JSON.stringify(d);let g=await sA("https://api.anthropic.com/v1/messages",m);if(h=new Date,s=g.status,c=g.headers,!g.ok){let e=await g.json().catch(()=>({error:{message:g.statusText}}));throw n={message:`Anthropic Error: ${e?.error?.message||g.statusText} (Type: ${e?.error?.type})`,status:g.status,provider_error:e}}if(i.stream){if(!g.body)throw n={message:"Anthropic stream body null",status:500};let{createFirstTokenTrackingStream:e}=await r.e(9704).then(r.bind(r,99704)),t=e(g.body,"Anthropic",f);o=new Response(t,{status:g.status,headers:{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive"}}),l={note:"streamed"}}else{let e=await g.json(),t=e.content?.find(e=>"text"===e.type)?.text||"";l={id:e.id||`anthropic-exPR-${Date.now()}`,object:"chat.completion",created:Math.floor(Date.now()/1e3),model:e.model||f,choices:[{index:0,message:{role:"assistant",content:t},finish_reason:e.stop_reason?.toLowerCase()||"stop"}],usage:{prompt_tokens:e.usage?.input_tokens,completion_tokens:e.usage?.output_tokens,total_tokens:(e.usage?.input_tokens||0)+(e.usage?.output_tokens||0)}}}}else if(e?.toLowerCase()==="deepseek"){let{custom_api_config_id:e,role:t,...u}=i,d={...u,model:f,messages:i.messages,stream:i.stream};Object.keys(d).forEach(e=>void 0===d[e]&&delete d[e]);let m={...p};m.headers={...p.headers,Authorization:`Bearer ${a}`,Origin:"https://rokey.app"},m.body=JSON.stringify(d);let g=await sA("https://api.deepseek.com/chat/completions",m);if(h=new Date,s=g.status,c=g.headers,!g.ok){let e=await g.json().catch(()=>({error:{message:g.statusText}}));throw n={message:`DeepSeek Error: ${e?.error?.message||g.statusText} (Type: ${e?.error?.type})`,status:g.status,provider_error:e}}if(i.stream){if(!g.body)throw n={message:"DeepSeek stream body null",status:500};let{createFirstTokenTrackingStream:e}=await r.e(9704).then(r.bind(r,99704)),t=e(g.body,"DeepSeek",f);o=new Response(t,{status:g.status,statusText:g.statusText,headers:g.headers}),l={note:"streamed"}}else l=await g.json()}else if(e?.toLowerCase()==="xai"){let{custom_api_config_id:e,role:t,...u}=i,d={...u,model:f,messages:i.messages,stream:i.stream||!1};"number"==typeof i.temperature&&(d.temperature=i.temperature),"number"==typeof i.max_tokens&&(d.max_tokens=i.max_tokens),"number"==typeof i.top_p&&(d.top_p=i.top_p),"number"==typeof i.frequency_penalty&&(d.frequency_penalty=i.frequency_penalty),"number"==typeof i.presence_penalty&&(d.presence_penalty=i.presence_penalty);let m={...p};m.headers={...p.headers,Authorization:`Bearer ${a}`,Origin:"https://rokey.app"},m.body=JSON.stringify(d);let g=await sA("https://api.x.ai/v1/chat/completions",m);if(h=new Date,s=g.status,c=g.headers,!g.ok){let e=await g.json().catch(()=>({error:{message:g.statusText}}));throw n={message:`XAI/Grok Error: ${e?.error?.message||g.statusText} (Type: ${e?.error?.type})`,status:g.status,provider_error:e}}if(i.stream){if(!g.body)throw n={message:"XAI/Grok stream body null",status:500};let{createFirstTokenTrackingStream:e}=await r.e(9704).then(r.bind(r,99704)),t=e(g.body,"XAI",f);o=new Response(t,{status:g.status,headers:{...Object.fromEntries(g.headers),"Content-Type":"text/event-stream"}}),l={note:"streamed"}}else l=await g.json()}else throw n={message:`Provider '${e}' is configured but not supported by RoKey proxy (executeProviderRequest).`,status:501,internal:!0};if(!i.stream&&l&&i.messages&&t){let r=sg(i.messages,t,i.temperature);if(sd.set(r,{response:l,timestamp:Date.now(),provider:e||"unknown",model:t}),sd.size>1e3){let e=Array.from(sd.entries());e.sort((e,t)=>e[1].timestamp-t[1].timestamp);let t=Math.floor(.2*e.length);for(let r=0;r<t;r++)sd.delete(e[r][0])}}return{success:!0,response:o,responseData:l,responseHeaders:c,status:s,error:null,llmRequestTimestamp:u,llmResponseTimestamp:h}}catch(a){let e=n||a,t="ProviderCommsError",r="";return"AbortError"===a.name?(t="TimeoutError",r="Request timed out after 30 seconds"):a.message?.includes("fetch failed")?(t="NetworkError",r="Network connection failed - check internet connectivity"):"ENOTFOUND"===a.code?(t="DNSError",r="DNS resolution failed - check network settings"):"ECONNREFUSED"===a.code&&(t="ConnectionRefused",r="Connection refused by server"),{success:!1,status:e.status||500,error:e.provider_error||{message:`${e.message}${r?` (${r})`:""}`,type:e.internal?"RoKeyInternal":t,diagnostic:r},llmRequestTimestamp:u||new Date,llmResponseTimestamp:h||new Date,response:void 0,responseData:void 0,responseHeaders:c}}}async function s$(e){let t,r,a=new Date,i=await (0,D.createSupabaseServerClientOnRequest)();performance.now();let s=e.headers.get("Authorization"),n=process.env.ROKEY_API_ACCESS_TOKEN;if(!n)return L.NextResponse.json({error:"Server configuration error: API access token not configured."},{status:500});if(!s||!s.startsWith("Bearer "))return L.NextResponse.json({error:"Unauthorized: Missing or invalid Authorization header format."},{status:401});if(s.substring(7)!==n)return L.NextResponse.json({error:"Unauthorized: Invalid API token."},{status:401});let o=null,l="unknown",c=null,u=null,h=null,p=null,d=null,f=null,m=null,g=null,b=null,y=null,_=null,v=null,w=null,k=null,x=null,O=!1,P=null;try{let s=await e.json();s&&"_internal_user_id"in s&&(v=s._internal_user_id,delete s._internal_user_id,delete s._internal_user_email);let n=sk.safeParse(s);if(!n.success)throw p={message:"Invalid request body",issues:n.error.flatten().fieldErrors,status:400};c=(t=n.data).custom_api_config_id;let w=t.messages?.[t.messages.length-1];if(w?.role==="user"&&"string"==typeof w.content){let e=w.content.toLowerCase().trim();["continue","continue please","keep going","go on","more","more please","finish","complete","what next","then what","and then"].includes(e)}let k=Date.now();for(let[e,t]of sp.entries())k-t.lastActivity>18e5&&sp.delete(e);if(t.specific_api_key_id)try{let{data:e,error:r}=await i.from("api_keys").select("*").eq("id",t.specific_api_key_id).eq("custom_api_config_id",c).eq("status","active").single();if(r||!e)throw p={message:`Specific API key ${t.specific_api_key_id} not found or not active in this configuration.`,status:404};let a=(0,z.Y)(e.encrypted_api_key),s=await sI(e.provider,e.predefined_model_id,a,t);if(o=e.id,u=e.predefined_model_id,h=e.provider,m=s.llmRequestTimestamp,g=s.llmResponseTimestamp,d=s.status??null,_=s.responseHeaders??null,l="specific_key_retry",s.success){if(f=s.responseData||{note:"streamed via specific key routing"},t.stream&&s.response)return s.response;if(!t.stream&&void 0!==s.responseData)return L.NextResponse.json(f,{status:d||200,headers:s.responseHeaders})}else throw p={message:`Specific API key ${e.id} failed: ${s.error?.message||"Unknown error"}`,status:s.status||500,provider_error:s.error},f=s.error,p}catch(e){p||(p={message:`Error using specific API key: ${e.message}`,status:500})}let x=`${c}:${t.messages?.[t.messages.length-1]?.content?.substring(0,100)||"default"}`,O=sf.get(x);if(O&&Date.now()-O.timestamp<18e5){let e=await sI(O.provider,O.model,O.apiKey,t);if(e.success)return sb(e,t);sf.delete(x)}let[T,E]=await Promise.allSettled([s_(c),i.from("custom_api_configs").select("id, name, user_id, routing_strategy, routing_strategy_params").eq("id",c).single()]);if("rejected"===E.status||!E.value.data){let e="rejected"===E.status?E.reason:E.value.error;throw p={message:"Custom API Configuration not found or error fetching it.",status:404,provider_error:e}}let S=E.value.data;P=S.user_id;let C=S.routing_strategy,j=S.routing_strategy_params,A=C&&"none"!==C&&"auto"!==C,I=performance.now(),[$,N]=await Promise.allSettled([(async()=>{let e=t.messages,r=null,a="",s=[];if("fulfilled"===T.status&&T.value&&(r=T.value.trainingData,e=await sw(e,r)),P&&c){let t=e.filter(e=>"user"===e.role);if(t.length>0){let r=t[t.length-1],n="string"==typeof r.content?r.content:r.content?.[0]?.text||"";if(n.trim()){if(/^(hi|hello|hey|sup|yo|greetings?|thanks?|thank you|bye|goodbye|ok|okay|yes|no)\.?$/i.test(n.trim()))a="",s=[];else{let e=await sv(n,c,P,i);a=e.context,s=e.sources}if(a){let t=e.findIndex(e=>"system"===e.role),r=`

=== IMPORTANT KNOWLEDGE BASE CONTEXT ===
${a}
=== END KNOWLEDGE BASE ===

IMPORTANT: The above knowledge base contains specific information that should be prioritized when answering questions. Use this information to provide detailed, accurate responses. If the user asks about topics covered in the knowledge base, draw extensively from this content rather than giving generic responses.`;t>=0?e[t].content+=r:e.unshift({role:"system",content:`You are a helpful AI assistant.${r}`})}}}}return{enhancedMessages:e,trainingData:r,documentContext:a,documentSources:s}})(),(async()=>"intelligent_role"===C?await sO(S,c,t,i,e):"strict_fallback"===C?await sP(j,c,i):"complexity_round_robin"===C?await sT(S,t,i):{targetApiKeyData:null,roleUsedState:"no_strategy"})()]),M=performance.now(),R=null,D=[];if("fulfilled"===$.status&&(t.messages=$.value.enhancedMessages,D=$.value.documentSources||[],t.messages.find(e=>"system"===e.role),D.length),"fulfilled"===N.status){if("hybridResponse"in N.value&&N.value.hybridResponse)return l=N.value.roleUsedState,h="hybrid_orchestration",u="crewai_autogen_hybrid",m=new Date,g=new Date,d=200,f={note:"Revolutionary hybrid orchestration response"},N.value.hybridResponse;R=N.value.targetApiKeyData,l=N.value.roleUsedState,"classifiedComplexityLevel"in N.value&&void 0!==N.value.classifiedComplexityLevel&&(b=N.value.classifiedComplexityLevel),"classifiedComplexityLLM"in N.value&&void 0!==N.value.classifiedComplexityLLM&&(y=N.value.classifiedComplexityLLM)}else l="routing_failed";if(!R)if(A)if(R=await sE(i,c,t.role))if(t.role){let{data:e}=await i.from("api_key_role_assignments").select("api_key_id").eq("custom_api_config_id",c).eq("api_key_id",R.id).eq("role_name",t.role).maybeSingle();l=e?K.roleRouting(t.role):K.defaultKeySuccess()}else l=K.defaultKeySuccess();else l=`${l}_then_fb_failed_completely`;else{let{data:e,error:r}=await i.from("api_keys").select("*").eq("custom_api_config_id",c).eq("status","active");if(r)p={message:"Database error fetching keys for default routing.",status:500,provider_error:r},l="default_db_error_fetching_keys";else if(e&&0!==e.length){let r=S.routing_strategy_params||{};"number"==typeof r._default_rr_idx&&r._default_rr_idx;let a=[...e].sort((e,t)=>e.id.localeCompare(t.id)),s=a.map(async(e,r)=>{let a=r+1;try{let r=(0,z.Y)(e.encrypted_api_key),i=await sI(e.provider,e.predefined_model_id,r,t);if(i.success)return{success:!0,key:e,result:i,attemptNumber:a};return{success:!1,key:e,error:i.error,status:i.status,attemptNumber:a}}catch(t){return{success:!1,key:e,error:t,status:t.status||500,attemptNumber:a}}});try{let e=await Promise.allSettled(s),n=null,c=null,b=e.length;for(let t of e)if("fulfilled"===t.status&&t.value.success){n=t.value;break}else"fulfilled"===t.status&&(c=t.value);if(n){let e=n.key,s=n.result,c=n.attemptNumber;if(r._default_rr_idx=(a.findIndex(t=>t.id===e.id)+1)%a.length,S.routing_strategy_params=r,setImmediate(async()=>{let{error:e}=await i.from("custom_api_configs").update({routing_strategy_params:r}).eq("id",S.id)}),o=e.id,u=e.predefined_model_id,h=e.provider,m=s?.llmRequestTimestamp||null,g=s?.llmResponseTimestamp||null,d=s?.status??null,_=s?.responseHeaders??null,l=K.defaultKeySuccess(c),p=null,t.stream&&s?.response)return f=s.responseData||{note:"streamed via parallel default routing"},s.response;if(!t.stream&&s?.responseData!==void 0)return f=s.responseData,L.NextResponse.json(f,{status:d||200,headers:s.responseHeaders});f={error:(p={message:`Internal error: Key ${e.id} success but no response data/stream.`,status:500}).message},d=500}else c?(f=c.error,d=c.status??null,p={message:`All ${b} key(s) for parallel default routing failed. Last error from key ${c.key.id}: ${c.error?.message||"Unknown"}`,status:c.status||500,provider_error:c.error},l=K.allKeysFailed(b)):(p={message:`All ${b} key(s) for parallel default routing failed with unknown errors.`,status:500},l=`default_all_parallel_attempts_failed_${b}`)}catch(e){p={message:`Parallel default routing failed: ${e.message}`,status:500},l="default_parallel_execution_error"}!p&&!o&&a.length>0&&(p={message:`All ${a.length} key(s) for default routing were attempted but failed. Status: ${d||"N/A"}. An internal error may have occurred.`,status:d||500,provider_error:null},a.length>0&&(l=K.allKeysFailed(a.length))),p&&!o&&(l=`default_all_attempts_failed_final_err_summary_${p?.message?.substring(0,70)}`)}else p={message:`No active keys configured for RoKey Config ID ${c} to use with default routing.`,status:404},l="default_no_active_keys_for_config"}if(R&&!p)if(o=R.id,u=R.predefined_model_id,h=R.provider){if(!r)try{r=(0,z.Y)(R.encrypted_api_key)}catch(e){p={message:`API Key decryption failed for selected key ${R.id}.`,status:500}}}else p={message:`Selected API key '${o}' does not have a provider configured. Please check the API key settings.`,status:500};else R||p||(p={message:`RoKey could not resolve an API key for this request. Last routing state: ${l||"unknown"}.`,status:404});if(R&&r&&!p&&h&&A){let s=await sI(h,u,r,t);if(m=s.llmRequestTimestamp,g=s.llmResponseTimestamp,d=s.status??null,_=s.responseHeaders??null,s.success)return f=s.responseData||{note:"streamed via explicit strategy"},sb(s,t,h,u||void 0,r,{roleUsed:l,routingStrategy:C,complexityLevel:b||void 0,processingTime:M-I});{let r={keyId:o,provider:h,status:s.status,error:s.error,strategy:C};try{let r={custom_api_config_id:c,api_key_id:o,user_id:P||v,predefined_model_id:u,role_requested:t?.role||null,role_used:`${l}_FAILED`,ip_address:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||null,request_timestamp:a.toISOString(),response_timestamp:new Date().toISOString(),status_code:s.status||500,request_payload_summary:t?{messages_count:t.messages?.length,model_requested_passthrough:t.model,stream:t.stream,temp:t.temperature,max_tok:t.max_tokens}:{note:"Request body was not available."},response_payload_summary:{error_type:s.error?.type||s.error?.error?.type||"provider_error",error_message_summary:s.error?.message||s.error?.error?.message||s.error?.details?.message||"Unknown error",provider_status:s.error?.status||s.status,full_error_details:s.error,fallback_initiated:!0},error_message:`ORIGINAL FAILURE: ${s.error?.message||s.error?.error?.message||s.error?.details?.message||JSON.stringify(s.error)||"Unknown error"}. Fallback will be attempted.`,error_source:h,error_details_zod:null,llm_provider_name:h,llm_model_name:u,llm_provider_status_code:s.status,llm_provider_latency_ms:s.llmResponseTimestamp&&s.llmRequestTimestamp?s.llmResponseTimestamp.getTime()-s.llmRequestTimestamp.getTime():null,processing_duration_ms:new Date().getTime()-a.getTime(),classified_role_llm:null,classified_complexity_level:b,classified_complexity_llm:y,cost:null,input_tokens:null,output_tokens:null,is_multimodal:!!t?.messages&&t.messages.some(e=>Array.isArray(e.content)&&e.content.some(e=>"image_url"===e.type))},{error:n}=await i.from("request_logs").insert(r)}catch(e){}let n=!1,w=await sE(i,c,void 0);if(w&&w.id!==o){let e;try{e=(0,z.Y)(w.encrypted_api_key)}catch(t){e=""}if(e){let r=await sI(w.provider,w.predefined_model_id,e,t);if(r.success){if(l=`${l}_FALLBACK_SUCCESS_default`,n=!0,m=r.llmRequestTimestamp,g=r.llmResponseTimestamp,d=r.status??null,_=r.responseHeaders??null,o=w.id,u=w.predefined_model_id,h=w.provider,t.stream&&r.response)return f=r.responseData||{note:"streamed via intelligent fallback to default"},r.response;if(!t.stream&&void 0!==r.responseData)return f=r.responseData,L.NextResponse.json(f,{status:d||200,headers:r.responseHeaders})}}}if(!n){let{data:e,error:r}=await i.from("api_keys").select("*").eq("custom_api_config_id",c).eq("status","active");if(r);else if(e&&e.length>0){let r=new Set([o]);w&&r.add(w.id);let a=e.filter(e=>!r.has(e.id));if(a.length>0){let e=a.map(async e=>{try{let r=(0,z.Y)(e.encrypted_api_key),a=await sI(e.provider,e.predefined_model_id,r,t);if(a.success)return{success:!0,key:e,result:a};return{success:!1,key:e,error:a.error}}catch(t){return{success:!1,key:e,error:t}}});try{for(let r of(await Promise.allSettled(e)))if("fulfilled"===r.status&&r.value.success){let e=r.value,a=e.key,i=e.result;if(l=`${l}_PARALLEL_FALLBACK_SUCCESS_${a.id}`,n=!0,m=i?.llmRequestTimestamp||null,g=i?.llmResponseTimestamp||null,d=i?.status??null,_=i?.responseHeaders??null,o=a.id,u=a.predefined_model_id,h=a.provider,t.stream&&i?.response)return f=i.responseData||{note:`streamed via parallel fallback to ${a.id}`},i.response;if(!t.stream&&i?.responseData!==void 0)return f=i.responseData,L.NextResponse.json(f,{status:d||200,headers:i.responseHeaders});break}}catch(e){}}}}n||(l=`${l}_all_fallbacks_failed`,p={message:`Intelligent routing (${r.strategy}) failed for key ${r.keyId} (${r.provider}). All fallback attempts also failed. Original error: ${r.error?.message||"Unknown"}`,status:r.status||500,provider_error:r.error,fallback_attempted:!0},f=r.error,d=r.status??null)}}else R&&!r&&!p&&h&&A?p||(p={message:`API key ${o} selected but decryption failed (safeguard).`,status:500}):R&&r&&!p&&!h&&A&&!p&&(p={message:`API key ${o} selected but has no provider configured (safeguard).`,status:500});if(p){let e=p.status||500,t=p.message||"An unexpected internal server error occurred.",r=p.issues,a=p.provider_error;return!d&&p.status&&p.provider_error&&(d=p.status),!f&&t&&(f={error:{message:t,...a&&{details:a}}}),L.NextResponse.json({error:t,...r&&{issues:r},...a&&{provider_error_details:a}},{status:e})}if(!p&&!o)return p={message:"Critical internal error: No API key processed and no explicit error state.",status:500},L.NextResponse.json({error:p.message},{status:p.status});if(f&&!t?.stream)return L.NextResponse.json(f,{status:d||200});return L.NextResponse.json({error:"An unexpected critical server error occurred."},{status:500})}finally{let r=new Date,s=r.getTime()-a.getTime(),n=null;m&&g&&(n=g.getTime()-m.getTime());let T=s-(n||0);performance.now(),t?.messages&&(O=t.messages.some(e=>Array.isArray(e.content)&&e.content.some(e=>"image_url"===e.type))),f?.usage?(k=f.usage.prompt_tokens||f.usage.input_tokens||null,x=f.usage.completion_tokens||f.usage.output_tokens||null):h?.toLowerCase()==="google"&&f?.promptFeedback?.tokenCount!==void 0&&(k=f.promptFeedback.tokenCount,x=f.candidates?.[0]?.tokenCount||null),function(e,t){if(!e)return!1;let r=e.toLowerCase();if("deepseek"===r)return!0;if(("google"===r||"gemini"===r)&&t)for(let e of["x-ratelimit-limit","x-ratelimit-requests-limit","x-goog-quota-limit","quota-limit"]){let r=t.get(e);if(r){let e=parseInt(r);if(!isNaN(e))return e<=60}}return!1}(h,_||void 0)?w=0:f?.usage?.cost&&h?.toLowerCase()==="openrouter"?w=1e-6*f.usage.cost:null!==k&&null!==x&&u&&(setImmediate(async()=>{try{let{data:e,error:t}=await i.from("models").select("input_token_price, output_token_price").eq("id",u).single();if(!t&&e?.input_token_price&&e?.output_token_price&&null!==k&&null!==x){let t=k*e.input_token_price,r=x*e.output_token_price;E&&await i.from("request_logs").update({cost:t+r}).eq("custom_api_config_id",E).eq("request_timestamp",a.toISOString())}}catch(e){}}),w=null);let E=c||t?.custom_api_config_id;E?setImmediate(async()=>{try{let i=P||v,s={custom_api_config_id:E,api_key_id:o,user_id:i,predefined_model_id:u,role_requested:t?.role||null,role_used:l,ip_address:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||null,request_timestamp:a.toISOString(),response_timestamp:r.toISOString(),status_code:p?p.status||500:d||200,request_payload_summary:t?{messages_count:t.messages?.length,model_requested_passthrough:t.model,stream:t.stream,temp:t.temperature,max_tok:t.max_tokens}:{note:"Request body was not available or Zod validation failed."},response_payload_summary:{usage:f?.usage,finish_reason:f?.choices?.[0]?.finish_reason,error_type:f?.error?.type,error_message_summary:f?.error?.message,full_error_details:f?.error,is_fallback_success:l?.includes("FALLBACK_SUCCESS")||!1,original_failure_summary:l?.includes("FAILED")?"See previous log entry for original failure details":null},error_message:l?.includes("FALLBACK_SUCCESS")?"FALLBACK SUCCESS: Original model failed, successfully used fallback model. Check previous log entry for failure details.":p?.message?p.message:f?.error?.message?f.error.message:null,error_source:p?p.provider_error&&h?h:"RoKey":f?.error?h:null,error_details_zod:p?.issues?JSON.stringify(p.issues):null,llm_provider_name:h,llm_model_name:u,llm_provider_status_code:d,llm_provider_latency_ms:n,processing_duration_ms:T,classified_role_llm:null,classified_complexity_level:b,classified_complexity_llm:y,cost:w,input_tokens:k,output_tokens:x,is_multimodal:O},c=(0,F.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}}),{error:m}=await c.from("request_logs").insert(s)}catch(e){}}):p&&setImmediate(async()=>{try{let i={custom_api_config_id:null,api_key_id:null,user_id:v,predefined_model_id:null,role_requested:t?.role||null,role_used:null,ip_address:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||null,request_timestamp:a.toISOString(),response_timestamp:r.toISOString(),status_code:p.status||500,request_payload_summary:{note:"Early error, request body may be malformed.",custom_api_config_id_attempted:c},response_payload_summary:{error_message_summary:p.message?.substring(0,100)},error_message:p.message,error_source:"RoKey",error_details_zod:p.issues?JSON.stringify(p.issues):null,llm_provider_name:null,llm_model_name:null,llm_provider_status_code:null,llm_provider_latency_ms:null,processing_duration_ms:s,classified_role_llm:null,classified_complexity_level:null,classified_complexity_llm:null,cost:null,input_tokens:null,output_tokens:null,is_multimodal:!1},n=(0,F.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}}),{error:o}=await n.from("request_logs").insert(i)}catch(e){}})}}async function sN(e){return L.NextResponse.json({},{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}let sM=new N.AppRouteRouteModule({definition:{kind:M.RouteKind.APP_ROUTE,page:"/api/v1/chat/completions/route",pathname:"/api/v1/chat/completions",filename:"route",bundlePath:"app/api/v1/chat/completions/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\v1\\chat\\completions\\route.ts",nextConfigOutput:"",userland:$}),{workAsyncStorage:sR,workUnitAsyncStorage:sL,serverHooks:sD}=sM;function sF(){return(0,R.patchFetch)({workAsyncStorage:sR,workUnitAsyncStorage:sL})}},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},99366:(e,t,r)=>{"use strict";r.d(t,{ez:()=>E,RG:()=>S,Ns:()=>A,Vt:()=>P,Qs:()=>T,D4:()=>k,g2:()=>O,QC:()=>j,Xm:()=>C});var a=Object.prototype.toString,i=Array.isArray||function(e){return"[object Array]"===a.call(e)};function s(e){return"function"==typeof e}function n(e){return e.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")}function o(e,t){return null!=e&&"object"==typeof e&&t in e}var l=RegExp.prototype.test,c=/\S/,u={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;","`":"&#x60;","=":"&#x3D;"},h=/\s*/,p=/\s+/,d=/\s*=/,f=/\s*\}/,m=/#|\^|\/|>|\{|&|=|!/;function g(e){this.string=e,this.tail=e,this.pos=0}function b(e,t){this.view=e,this.cache={".":this.view},this.parent=t}function y(){this.templateCache={_cache:{},set:function(e,t){this._cache[e]=t},get:function(e){return this._cache[e]},clear:function(){this._cache={}}}}g.prototype.eos=function(){return""===this.tail},g.prototype.scan=function(e){var t=this.tail.match(e);if(!t||0!==t.index)return"";var r=t[0];return this.tail=this.tail.substring(r.length),this.pos+=r.length,r},g.prototype.scanUntil=function(e){var t,r=this.tail.search(e);switch(r){case -1:t=this.tail,this.tail="";break;case 0:t="";break;default:t=this.tail.substring(0,r),this.tail=this.tail.substring(r)}return this.pos+=t.length,t},b.prototype.push=function(e){return new b(e,this)},b.prototype.lookup=function(e){var t=this.cache;if(t.hasOwnProperty(e))i=t[e];else{for(var r,a,i,n,l,c,u=this,h=!1;u;){if(e.indexOf(".")>0)for(n=u.view,l=e.split("."),c=0;null!=n&&c<l.length;)c===l.length-1&&(h=o(n,l[c])||(r=n,a=l[c],null!=r&&"object"!=typeof r&&r.hasOwnProperty&&r.hasOwnProperty(a))),n=n[l[c++]];else n=u.view[e],h=o(u.view,e);if(h){i=n;break}u=u.parent}t[e]=i}return s(i)&&(i=i.call(this.view)),i},y.prototype.clearCache=function(){void 0!==this.templateCache&&this.templateCache.clear()},y.prototype.parse=function(e,t){var r=this.templateCache,a=e+":"+(t||_.tags).join(":"),s=void 0!==r,o=s?r.get(a):void 0;return void 0==o&&(o=function(e,t){if(!e)return[];var r,a,s,o,u,b,y,v,w,k=!1,x=[],O=[],P=[],T=!1,E=!1,S="",C=0;function j(){if(T&&!E)for(;P.length;)delete O[P.pop()];else P=[];T=!1,E=!1}function A(e){if("string"==typeof e&&(e=e.split(p,2)),!i(e)||2!==e.length)throw Error("Invalid tags: "+e);r=RegExp(n(e[0])+"\\s*"),a=RegExp("\\s*"+n(e[1])),s=RegExp("\\s*"+n("}"+e[1]))}A(t||_.tags);for(var I=new g(e);!I.eos();){if(o=I.pos,b=I.scanUntil(r))for(var $=0,N=b.length;$<N;++$)!function(e){return!l.call(c,e)}(y=b.charAt($))?(E=!0,k=!0,S+=" "):(P.push(O.length),S+=y),O.push(["text",y,o,o+1]),o+=1,"\n"===y&&(j(),S="",C=0,k=!1);if(!I.scan(r))break;if(T=!0,u=I.scan(m)||"name",I.scan(h),"="===u?(b=I.scanUntil(d),I.scan(d),I.scanUntil(a)):"{"===u?(b=I.scanUntil(s),I.scan(f),I.scanUntil(a),u="&"):b=I.scanUntil(a),!I.scan(a))throw Error("Unclosed tag at "+I.pos);if(v=">"==u?[u,b,o,I.pos,S,C,k]:[u,b,o,I.pos],C++,O.push(v),"#"===u||"^"===u)x.push(v);else if("/"===u){if(!(w=x.pop()))throw Error('Unopened section "'+b+'" at '+o);if(w[1]!==b)throw Error('Unclosed section "'+w[1]+'" at '+o)}else"name"===u||"{"===u||"&"===u?E=!0:"="===u&&A(b)}if(j(),w=x.pop())throw Error('Unclosed section "'+w[1]+'" at '+I.pos);return function(e){for(var t,r=[],a=r,i=[],s=0,n=e.length;s<n;++s)switch((t=e[s])[0]){case"#":case"^":a.push(t),i.push(t),a=t[4]=[];break;case"/":i.pop()[5]=t[2],a=i.length>0?i[i.length-1][4]:r;break;default:a.push(t)}return r}(function(e){for(var t,r,a=[],i=0,s=e.length;i<s;++i)(t=e[i])&&("text"===t[0]&&r&&"text"===r[0]?(r[1]+=t[1],r[3]=t[3]):(a.push(t),r=t));return a}(O))}(e,t),s&&r.set(a,o)),o},y.prototype.render=function(e,t,r,a){var i=this.getConfigTags(a),s=this.parse(e,i),n=t instanceof b?t:new b(t,void 0);return this.renderTokens(s,n,r,e,a)},y.prototype.renderTokens=function(e,t,r,a,i){for(var s,n,o,l="",c=0,u=e.length;c<u;++c)o=void 0,"#"===(n=(s=e[c])[0])?o=this.renderSection(s,t,r,a,i):"^"===n?o=this.renderInverted(s,t,r,a,i):">"===n?o=this.renderPartial(s,t,r,i):"&"===n?o=this.unescapedValue(s,t):"name"===n?o=this.escapedValue(s,t,i):"text"===n&&(o=this.rawValue(s)),void 0!==o&&(l+=o);return l},y.prototype.renderSection=function(e,t,r,a,n){var o=this,l="",c=t.lookup(e[1]);if(c){if(i(c))for(var u=0,h=c.length;u<h;++u)l+=this.renderTokens(e[4],t.push(c[u]),r,a,n);else if("object"==typeof c||"string"==typeof c||"number"==typeof c)l+=this.renderTokens(e[4],t.push(c),r,a,n);else if(s(c)){if("string"!=typeof a)throw Error("Cannot use higher-order sections without the original template");null!=(c=c.call(t.view,a.slice(e[3],e[5]),function(e){return o.render(e,t,r,n)}))&&(l+=c)}else l+=this.renderTokens(e[4],t,r,a,n);return l}},y.prototype.renderInverted=function(e,t,r,a,s){var n=t.lookup(e[1]);if(!n||i(n)&&0===n.length)return this.renderTokens(e[4],t,r,a,s)},y.prototype.indentPartial=function(e,t,r){for(var a=t.replace(/[^ \t]/g,""),i=e.split("\n"),s=0;s<i.length;s++)i[s].length&&(s>0||!r)&&(i[s]=a+i[s]);return i.join("\n")},y.prototype.renderPartial=function(e,t,r,a){if(r){var i=this.getConfigTags(a),n=s(r)?r(e[1]):r[e[1]];if(null!=n){var o=e[6],l=e[5],c=e[4],u=n;0==l&&c&&(u=this.indentPartial(n,c,o));var h=this.parse(u,i);return this.renderTokens(h,t,r,u,a)}}},y.prototype.unescapedValue=function(e,t){var r=t.lookup(e[1]);if(null!=r)return r},y.prototype.escapedValue=function(e,t,r){var a=this.getConfigEscape(r)||_.escape,i=t.lookup(e[1]);if(null!=i)return"number"==typeof i&&a===_.escape?String(i):a(i)},y.prototype.rawValue=function(e){return e[1]},y.prototype.getConfigTags=function(e){return i(e)?e:e&&"object"==typeof e?e.tags:void 0},y.prototype.getConfigEscape=function(e){return e&&"object"==typeof e&&!i(e)?e.escape:void 0};var _={name:"mustache.js",version:"4.2.0",tags:["{{","}}"],clearCache:void 0,escape:void 0,parse:void 0,render:void 0,Scanner:void 0,Context:void 0,Writer:void 0,set templateCache(cache){v.templateCache=cache},get templateCache(){return v.templateCache}},v=new y;function w(){_.escape=e=>e}_.clearCache=function(){return v.clearCache()},_.parse=function(e,t){return v.parse(e,t)},_.render=function(e,t,r,a){if("string"!=typeof e)throw TypeError('Invalid template! Template should be a "string" but "'+(i(e)?"array":typeof e)+'" was given as the first argument for mustache#render(template, view, partials)');return v.render(e,t,r,a)},_.escape=function(e){return String(e).replace(/[&<>"'`=\/]/g,function(e){return u[e]})},_.Scanner=g,_.Context=b,_.Writer=y;let k=e=>{let t=e.split(""),r=[],a=(e,r)=>{for(let a=r;a<t.length;a+=1)if(e.includes(t[a]))return a;return -1},i=0;for(;i<t.length;)if("{"===t[i]&&i+1<t.length&&"{"===t[i+1])r.push({type:"literal",text:"{"}),i+=2;else if("}"===t[i]&&i+1<t.length&&"}"===t[i+1])r.push({type:"literal",text:"}"}),i+=2;else if("{"===t[i]){let e=a("}",i);if(e<0)throw Error("Unclosed '{' in template.");r.push({type:"variable",name:t.slice(i+1,e).join("")}),i=e+1}else if("}"===t[i])throw Error("Single '}' in template.");else{let e=a("{}",i),s=(e<0?t.slice(i):t.slice(i,e)).join("");r.push({type:"literal",text:s}),i=e<0?t.length:e}return r},x=e=>e.map(e=>"name"===e[0]?{type:"variable",name:e[1].includes(".")?e[1].split(".")[0]:e[1]}:["#","&","^",">"].includes(e[0])?{type:"variable",name:e[1]}:{type:"literal",text:e[1]}),O=e=>(w(),x(_.parse(e))),P=(e,t)=>k(e).reduce((e,r)=>{if("variable"===r.type){if(r.name in t)return e+t[r.name];throw Error(`(f-string) Missing value for input ${r.name}`)}return e+r.text},""),T=(e,t)=>(w(),_.render(e,t)),E={"f-string":P,mustache:T},S={"f-string":k,mustache:O},C=(e,t,r)=>E[t](e,r),j=(e,t)=>S[t](e),A=(e,t,r)=>{if(!(t in E)){let e=Object.keys(E);throw Error(`Invalid template format. Got \`${t}\`;
                         should be one of ${e}`)}try{let a=r.reduce((e,t)=>(e[t]="foo",e),{});Array.isArray(e)?e.forEach(e=>{if("text"===e.type)C(e.text,t,a);else if("image_url"===e.type)if("string"==typeof e.image_url)C(e.image_url,t,a);else{let r=e.image_url.url;C(r,t,a)}else throw Error(`Invalid message template received. ${JSON.stringify(e,null,2)}`)}):C(e,t,a)}catch(e){throw Error(`Invalid prompt schema: ${e.message}`)}}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[7719,580,9398,3410,5697,5601],()=>r(76549));module.exports=a})();
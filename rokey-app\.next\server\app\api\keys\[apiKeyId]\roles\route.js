(()=>{var e={};e.id=1529,e.ids=[1489,1529],e.modules={507:(e,t,i)=>{"use strict";i.d(t,{Dc:()=>n,p2:()=>a});let a=[{id:"general_chat",name:"General Chat",description:"Handles general conversation, Q&A, and tasks not covered by other specific roles. Often the default fallback.",backstory:"You are a knowledgeable and helpful AI assistant with broad expertise across multiple domains. You have years of experience helping people with diverse questions and tasks, from simple queries to complex problem-solving. You are patient, thorough, and always strive to provide accurate and useful information while maintaining a friendly and professional demeanor."},{id:"logic_reasoning",name:"Logic & Reasoning",description:"For tasks requiring logical deduction, problem-solving, mathematical reasoning, and complex analytical thinking.",backstory:"You are a brilliant analytical thinker with a PhD in Logic and Philosophy, combined with extensive experience in mathematical reasoning and problem-solving. You have spent years working as a consultant for complex analytical challenges, helping organizations break down intricate problems into manageable components. Your approach is methodical, precise, and you excel at identifying patterns and logical connections that others might miss."},{id:"writing",name:"Writing & Content Creation",description:"For all writing tasks, including articles, blog posts, marketing copy, creative content, essays, and more.",backstory:"You are an accomplished professional writer and content strategist with over 15 years of experience in journalism, marketing, and creative writing. You have worked with major publications, Fortune 500 companies, and bestselling authors. Your expertise spans multiple writing styles, from technical documentation to compelling storytelling. You understand audience psychology and know how to craft messages that resonate, engage, and drive action."},{id:"coding_frontend",name:"Coding - Frontend",description:"For generating and assisting with HTML, CSS, JavaScript, and frontend frameworks (React, Vue, Angular, etc.).",backstory:"You are a senior frontend engineer with 10+ years of experience building beautiful, responsive, and user-friendly web applications. You have worked at leading tech companies and startups, mastering modern frameworks like React, Vue, and Angular. You are passionate about user experience, accessibility, and performance optimization. You stay current with the latest web technologies and best practices, and you have a keen eye for design and usability."},{id:"coding_backend",name:"Coding - Backend",description:"For generating and assisting with server-side logic, APIs, databases, and backend frameworks (Node.js, Python, Java, etc.).",backstory:"You are a seasoned backend engineer and system architect with 12+ years of experience building scalable, robust server-side applications. You have expertise in multiple programming languages including Python, Node.js, Java, and Go. You have designed and implemented high-performance APIs, microservices, and distributed systems for companies ranging from startups to enterprise-level organizations. You are well-versed in database design, cloud architecture, and DevOps practices."},{id:"research_synthesis",name:"Research & Synthesis",description:"For information retrieval from various sources, data analysis, and synthesizing findings into reports or summaries.",backstory:"You are a research analyst and information scientist with a Master's degree in Information Science and 8+ years of experience in academic and corporate research. You have worked with think tanks, consulting firms, and research institutions, specializing in gathering, analyzing, and synthesizing complex information from diverse sources. You are skilled at identifying credible sources, extracting key insights, and presenting findings in clear, actionable formats."},{id:"summarization_briefing",name:"Summarization & Briefing",description:"For condensing long texts, documents, or conversations into concise summaries or executive briefings.",backstory:"You are an executive communications specialist with 10+ years of experience working with C-level executives and government officials. You have mastered the art of distilling complex information into clear, concise, and actionable briefings. Your background includes work in consulting, journalism, and corporate communications. You understand how busy decision-makers consume information and can quickly identify the most critical points that require attention."},{id:"translation_localization",name:"Translation & Localization",description:"For translating text between languages and adapting content culturally for different locales.",backstory:"You are a professional translator and localization expert with 12+ years of experience working with international organizations and global brands. You are fluent in multiple languages and have deep cultural knowledge of various regions. You have worked on everything from legal documents to marketing campaigns, ensuring that content not only translates accurately but also resonates culturally with target audiences. You understand the nuances of language, cultural context, and regional preferences."},{id:"data_extraction_structuring",name:"Data Extraction & Structuring",description:"For identifying and extracting specific pieces of information from unstructured/semi-structured text and organizing it.",backstory:"You are a data analyst and information architect with expertise in natural language processing and data mining. You have 8+ years of experience working with large datasets, unstructured documents, and complex information systems. You have helped organizations extract valuable insights from messy data sources, create structured databases from unorganized information, and develop automated data processing pipelines. You are meticulous, detail-oriented, and skilled at pattern recognition."},{id:"brainstorming_ideation",name:"Brainstorming & Ideation",description:"For generating new ideas, exploring concepts, and creative problem-solving sessions.",backstory:"You are a creative innovation consultant and design thinking expert with 10+ years of experience helping organizations breakthrough creative blocks and generate breakthrough ideas. You have worked with startups, Fortune 500 companies, and creative agencies, facilitating ideation sessions and innovation workshops. You are skilled at lateral thinking, connecting disparate concepts, and creating environments where creativity flourishes. Your approach combines structured methodologies with free-flowing creative exploration."},{id:"education_tutoring",name:"Education & Tutoring",description:"For explaining concepts, answering educational questions, and providing tutoring assistance across various subjects.",backstory:"You are an experienced educator and academic tutor with a Master's in Education and 15+ years of teaching experience across multiple subjects and age groups. You have worked in schools, universities, and private tutoring, helping thousands of students understand complex concepts and achieve their learning goals. You are patient, encouraging, and skilled at adapting your teaching style to different learning preferences. You believe in making learning engaging, accessible, and meaningful."},{id:"image_generation",name:"Image Generation",description:"For creating images from textual descriptions. Assign to keys linked to image generation models.",backstory:"You are a digital artist and creative director with 8+ years of experience in visual design, digital art, and creative technology. You have worked with advertising agencies, game studios, and tech companies, creating compelling visual content for various media. You understand composition, color theory, visual storytelling, and the technical aspects of digital image creation. You are skilled at translating abstract concepts and ideas into powerful visual representations."},{id:"audio_transcription",name:"Audio Transcription",description:"For converting speech from audio files into written text. Assign to keys linked to transcription models.",backstory:"You are a professional transcriptionist and audio processing specialist with 10+ years of experience in media, legal, and academic transcription. You have worked with podcasters, journalists, legal firms, and researchers, converting audio content into accurate written text. You understand various accents, technical terminology, and industry-specific language. You are detail-oriented, accurate, and skilled at capturing not just words but also the context and nuance of spoken communication."}],n=e=>a.find(t=>t.id===e)},2507:(e,t,i)=>{"use strict";i.d(t,{Q:()=>s,createSupabaseServerClientOnRequest:()=>r});var a=i(34386),n=i(44999);async function r(){let e=await (0,n.UL)();return(0,a.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,i,a){try{e.set({name:t,value:i,...a})}catch(e){}},remove(t,i){try{e.set({name:t,value:"",...i})}catch(e){}}}})}function s(e){return(0,a.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,i){},remove(e,t){}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},49722:(e,t,i)=>{"use strict";i.r(t),i.d(t,{patchFetch:()=>f,routeModule:()=>p,serverHooks:()=>h,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>m});var a={};i.r(a),i.d(a,{GET:()=>u,POST:()=>l});var n=i(96559),r=i(48088),s=i(37719),o=i(32190),c=i(2507),d=i(507);async function u(e,{params:t}){let i=(0,c.Q)(e),{apiKeyId:a}=await t;if(!a)return o.NextResponse.json({error:"API Key ID is required"},{status:400});try{let{data:e,error:t}=await i.from("api_key_role_assignments").select("role_name, created_at").eq("api_key_id",a);if(t)return o.NextResponse.json({error:"Failed to fetch role assignments",details:t.message},{status:500});let n=e.map(e=>{let t=(0,d.Dc)(e.role_name);return{...e,role_details:t||{id:e.role_name,name:e.role_name,description:"Custom role (details managed globally)"}}});return o.NextResponse.json(n||[],{status:200})}catch(e){return o.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}async function l(e,{params:t}){let i=(0,c.Q)(e),{apiKeyId:a}=await t,{data:{session:n},error:r}=await i.auth.getSession();if(r||!n?.user)return o.NextResponse.json({error:"Unauthorized: You must be logged in to assign roles."},{status:401});let s=n.user.id;if(!a)return o.NextResponse.json({error:"API Key ID is required"},{status:400});try{let{role_name:t}=await e.json();if(!t||"string"!=typeof t)return o.NextResponse.json({error:"Role name (role_id) is required and must be a string"},{status:400});let{data:n,error:r}=await i.from("api_keys").select(`
        custom_api_config_id,
        custom_api_configs ( user_id )
      `).eq("id",a).single();if(r||!n)return o.NextResponse.json({error:"API Key not found or failed to fetch its details"},{status:404});let c=n.custom_api_configs?.user_id;if(!c&&"00000000-0000-0000-0000-000000000000"!==s)return o.NextResponse.json({error:"Could not determine the config owner for the API Key."},{status:500});if(c&&s!==c)return o.NextResponse.json({error:"Forbidden. You do not own the configuration this API key belongs to."},{status:403});let u=d.p2.some(e=>e.id===t),l=!1;if(!u){let{data:e,error:a}=await i.from("user_custom_roles").select("id").eq("user_id",s).eq("role_id",t).maybeSingle();if(a)return o.NextResponse.json({error:"Error validating role.",details:a.message},{status:500});e&&(l=!0)}if(!u&&!l)return o.NextResponse.json({error:`Invalid role_name: ${t}. Not a predefined role or a custom role you own.`},{status:400});let{custom_api_config_id:p}=n,{data:g,error:m}=await i.from("api_key_role_assignments").insert({api_key_id:a,custom_api_config_id:p,role_name:t}).select().single();if(m){if("23505"===m.code){if(m.message.includes("unique_api_key_role"))return o.NextResponse.json({error:"This API key already has this role assigned.",details:m.message},{status:409});if(m.message.includes("unique_role_per_custom_config"))return o.NextResponse.json({error:"This role is already assigned to another API key in this Custom Model (config). Check unique_role_per_custom_config constraint.",details:m.message},{status:409});return o.NextResponse.json({error:"Failed to assign role: This role may already be assigned in a way that violates a uniqueness constraint.",details:m.message,code:m.code},{status:409})}return o.NextResponse.json({error:"Failed to assign role to API key",details:m.message},{status:500})}return o.NextResponse.json(g,{status:201})}catch(e){if("SyntaxError"===e.name)return o.NextResponse.json({error:"Invalid request body: Malformed JSON."},{status:400});return o.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/keys/[apiKeyId]/roles/route",pathname:"/api/keys/[apiKeyId]/roles",filename:"route",bundlePath:"app/api/keys/[apiKeyId]/roles/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\keys\\[apiKeyId]\\roles\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:g,workUnitAsyncStorage:m,serverHooks:h}=p;function f(){return(0,s.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:m})}},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),a=t.X(0,[7719,580,9398,3410],()=>i(49722));module.exports=a})();
(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4150],{38152:(e,t,a)=>{"use strict";a.d(t,{Pi:()=>r.A,fK:()=>n.A,uc:()=>s.A});var r=a(55628),s=a(31151),n=a(74500)},44469:(e,t,a)=>{Promise.resolve().then(a.bind(a,76357))},75922:(e,t,a)=>{"use strict";a.d(t,{MG:()=>r});let r=[{id:"openai",name:"OpenAI",apiBaseUrl:"https://api.openai.com/v1/chat/completions",models:[]},{id:"google",name:"Google",apiBaseUrl:"https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",models:[]},{id:"anthropic",name:"Anthropic",apiBaseUrl:"https://api.anthropic.com/v1/chat/completions",models:[]},{id:"deepseek",name:"DeepSeek",apiBaseUrl:"https://api.deepseek.com/chat/completions",models:[]},{id:"xai",name:"xAI (Grok)",apiBaseUrl:"https://api.x.ai/v1/chat/completions",models:[]},{id:"openrouter",name:"OpenRouter",apiBaseUrl:"https://openrouter.ai/api/v1/chat/completions",models:[]}]},76357:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>S});var r=a(95155),s=a(12115),n=a(35695),i=a(75922);let o=[{id:"general_chat",name:"General Chat",description:"Handles general conversation, Q&A, and tasks not covered by other specific roles. Often the default fallback.",backstory:"You are a knowledgeable and helpful AI assistant with broad expertise across multiple domains. You have years of experience helping people with diverse questions and tasks, from simple queries to complex problem-solving. You are patient, thorough, and always strive to provide accurate and useful information while maintaining a friendly and professional demeanor."},{id:"logic_reasoning",name:"Logic & Reasoning",description:"For tasks requiring logical deduction, problem-solving, mathematical reasoning, and complex analytical thinking.",backstory:"You are a brilliant analytical thinker with a PhD in Logic and Philosophy, combined with extensive experience in mathematical reasoning and problem-solving. You have spent years working as a consultant for complex analytical challenges, helping organizations break down intricate problems into manageable components. Your approach is methodical, precise, and you excel at identifying patterns and logical connections that others might miss."},{id:"writing",name:"Writing & Content Creation",description:"For all writing tasks, including articles, blog posts, marketing copy, creative content, essays, and more.",backstory:"You are an accomplished professional writer and content strategist with over 15 years of experience in journalism, marketing, and creative writing. You have worked with major publications, Fortune 500 companies, and bestselling authors. Your expertise spans multiple writing styles, from technical documentation to compelling storytelling. You understand audience psychology and know how to craft messages that resonate, engage, and drive action."},{id:"coding_frontend",name:"Coding - Frontend",description:"For generating and assisting with HTML, CSS, JavaScript, and frontend frameworks (React, Vue, Angular, etc.).",backstory:"You are a senior frontend engineer with 10+ years of experience building beautiful, responsive, and user-friendly web applications. You have worked at leading tech companies and startups, mastering modern frameworks like React, Vue, and Angular. You are passionate about user experience, accessibility, and performance optimization. You stay current with the latest web technologies and best practices, and you have a keen eye for design and usability."},{id:"coding_backend",name:"Coding - Backend",description:"For generating and assisting with server-side logic, APIs, databases, and backend frameworks (Node.js, Python, Java, etc.).",backstory:"You are a seasoned backend engineer and system architect with 12+ years of experience building scalable, robust server-side applications. You have expertise in multiple programming languages including Python, Node.js, Java, and Go. You have designed and implemented high-performance APIs, microservices, and distributed systems for companies ranging from startups to enterprise-level organizations. You are well-versed in database design, cloud architecture, and DevOps practices."},{id:"research_synthesis",name:"Research & Synthesis",description:"For information retrieval from various sources, data analysis, and synthesizing findings into reports or summaries.",backstory:"You are a research analyst and information scientist with a Master's degree in Information Science and 8+ years of experience in academic and corporate research. You have worked with think tanks, consulting firms, and research institutions, specializing in gathering, analyzing, and synthesizing complex information from diverse sources. You are skilled at identifying credible sources, extracting key insights, and presenting findings in clear, actionable formats."},{id:"summarization_briefing",name:"Summarization & Briefing",description:"For condensing long texts, documents, or conversations into concise summaries or executive briefings.",backstory:"You are an executive communications specialist with 10+ years of experience working with C-level executives and government officials. You have mastered the art of distilling complex information into clear, concise, and actionable briefings. Your background includes work in consulting, journalism, and corporate communications. You understand how busy decision-makers consume information and can quickly identify the most critical points that require attention."},{id:"translation_localization",name:"Translation & Localization",description:"For translating text between languages and adapting content culturally for different locales.",backstory:"You are a professional translator and localization expert with 12+ years of experience working with international organizations and global brands. You are fluent in multiple languages and have deep cultural knowledge of various regions. You have worked on everything from legal documents to marketing campaigns, ensuring that content not only translates accurately but also resonates culturally with target audiences. You understand the nuances of language, cultural context, and regional preferences."},{id:"data_extraction_structuring",name:"Data Extraction & Structuring",description:"For identifying and extracting specific pieces of information from unstructured/semi-structured text and organizing it.",backstory:"You are a data analyst and information architect with expertise in natural language processing and data mining. You have 8+ years of experience working with large datasets, unstructured documents, and complex information systems. You have helped organizations extract valuable insights from messy data sources, create structured databases from unorganized information, and develop automated data processing pipelines. You are meticulous, detail-oriented, and skilled at pattern recognition."},{id:"brainstorming_ideation",name:"Brainstorming & Ideation",description:"For generating new ideas, exploring concepts, and creative problem-solving sessions.",backstory:"You are a creative innovation consultant and design thinking expert with 10+ years of experience helping organizations breakthrough creative blocks and generate breakthrough ideas. You have worked with startups, Fortune 500 companies, and creative agencies, facilitating ideation sessions and innovation workshops. You are skilled at lateral thinking, connecting disparate concepts, and creating environments where creativity flourishes. Your approach combines structured methodologies with free-flowing creative exploration."},{id:"education_tutoring",name:"Education & Tutoring",description:"For explaining concepts, answering educational questions, and providing tutoring assistance across various subjects.",backstory:"You are an experienced educator and academic tutor with a Master's in Education and 15+ years of teaching experience across multiple subjects and age groups. You have worked in schools, universities, and private tutoring, helping thousands of students understand complex concepts and achieve their learning goals. You are patient, encouraging, and skilled at adapting your teaching style to different learning preferences. You believe in making learning engaging, accessible, and meaningful."},{id:"image_generation",name:"Image Generation",description:"For creating images from textual descriptions. Assign to keys linked to image generation models.",backstory:"You are a digital artist and creative director with 8+ years of experience in visual design, digital art, and creative technology. You have worked with advertising agencies, game studios, and tech companies, creating compelling visual content for various media. You understand composition, color theory, visual storytelling, and the technical aspects of digital image creation. You are skilled at translating abstract concepts and ideas into powerful visual representations."},{id:"audio_transcription",name:"Audio Transcription",description:"For converting speech from audio files into written text. Assign to keys linked to transcription models.",backstory:"You are a professional transcriptionist and audio processing specialist with 10+ years of experience in media, legal, and academic transcription. You have worked with podcasters, journalists, legal firms, and researchers, converting audio content into accurate written text. You understand various accents, technical terminology, and industry-specific language. You are detail-oriented, accurate, and skilled at capturing not just words but also the context and nuance of spoken communication."}],l=e=>o.find(t=>t.id===e);var d=a(32461),c=a(6865),u=a(89959),m=a(37186),g=a(67695),h=a(94038),p=a(61316),x=a(85037),f=a(57765),b=a(8246),y=a(31151),v=a(52589),j=a(55424),w=a(80377),N=a(87162),k=a(28003),_=a(79958),C=a(53951),A=a(99323);let I=i.MG.map(e=>({value:e.id,label:e.name}));function S(){var e,t;let a=(0,n.useParams)().configId,S=(0,N.Z)(),T=(0,A.bu)(),D=(null==T?void 0:T.navigateOptimistically)||(e=>{window.location.href=e}),{getCachedData:P,isCached:E}=(0,k._)(),{createHoverPrefetch:Y}=(0,C.c)(),[F,M]=(0,s.useState)(null),[R,O]=(0,s.useState)(!0),[L,K]=(0,s.useState)(!1),[z,B]=(0,s.useState)((null==(e=I[0])?void 0:e.value)||"openai"),[V,q]=(0,s.useState)(""),[G,U]=(0,s.useState)(""),[J,H]=(0,s.useState)(""),[W,Z]=(0,s.useState)(1),[Q,$]=(0,s.useState)(!1),[X,ee]=(0,s.useState)(null),[et,ea]=(0,s.useState)(null),[er,es]=(0,s.useState)(null),[en,ei]=(0,s.useState)(!1),[eo,el]=(0,s.useState)(null),[ed,ec]=(0,s.useState)([]),[eu,em]=(0,s.useState)(!0),[eg,eh]=(0,s.useState)(null),[ep,ex]=(0,s.useState)(null),[ef,eb]=(0,s.useState)(null),[ey,ev]=(0,s.useState)(null),[ej,ew]=(0,s.useState)(1),[eN,ek]=(0,s.useState)(""),[e_,eC]=(0,s.useState)(!1),[eA,eI]=(0,s.useState)([]),[eS,eT]=(0,s.useState)(!1),[eD,eP]=(0,s.useState)(null),[eE,eY]=(0,s.useState)(!1),[eF,eM]=(0,s.useState)(""),[eR,eO]=(0,s.useState)(""),[eL,eK]=(0,s.useState)(""),[ez,eB]=(0,s.useState)(!1),[eV,eq]=(0,s.useState)(null),[eG,eU]=(0,s.useState)(null),eJ=(0,s.useCallback)(async()=>{if(!a)return;let e=P(a);if(e&&e.configDetails){M(e.configDetails),O(!1);return}E(a)||K(!0),O(!0),ee(null);try{let e=await fetch("/api/custom-configs");if(!e.ok){let t=await e.json();throw Error(t.error||"Failed to fetch configurations list")}let t=(await e.json()).find(e=>e.id===a);if(!t)throw Error("Configuration not found in the list.");M(t)}catch(e){ee("Error loading model configuration: ".concat(e.message)),M(null)}finally{O(!1),K(!1)}},[a,P,E]);(0,s.useEffect)(()=>{eJ()},[eJ]);let eH=(0,s.useCallback)(async()=>{let e=P(a);if(e&&e.models){es(e.models),ei(!1);return}ei(!0),el(null),es(null);try{let e=await fetch("/api/providers/list-models",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({})}),t=await e.json();if(!e.ok)throw Error(t.error||"Failed to fetch models from database.");t.models?es(t.models):es([])}catch(e){el("Error fetching models: ".concat(e.message)),es([])}finally{ei(!1)}},[a,P]);(0,s.useEffect)(()=>{a&&eH()},[a,eH]);let eW=(0,s.useCallback)(async()=>{let e=P(a);if(e&&e.userCustomRoles){eI(e.userCustomRoles),eT(!1);return}eT(!0),eP(null);try{let e=await fetch("/api/user/custom-roles");if(e.ok){let t=await e.json();eI(t)}else{let t;try{t=await e.json()}catch(a){t={error:await e.text().catch(()=>"HTTP error ".concat(e.status))}}let a=t.error||(t.issues?JSON.stringify(t.issues):"Failed to fetch custom roles (status: ".concat(e.status,")"));if(401===e.status)eP(a);else throw Error(a);eI([])}}catch(e){eP(e.message),eI([])}finally{eT(!1)}},[]),eZ=(0,s.useCallback)(async()=>{if(!a||!eA)return;let e=P(a);if(e&&e.apiKeys&&void 0!==e.defaultChatKeyId){let t=e.apiKeys.map(async t=>{let a=await fetch("/api/keys/".concat(t.id,"/roles")),r=[];return a.ok&&(r=(await a.json()).map(e=>{let t=l(e.role_name);if(t)return t;let a=eA.find(t=>t.role_id===e.role_name);return a?{id:a.role_id,name:a.name,description:a.description||void 0}:null}).filter(Boolean)),{...t,assigned_roles:r,is_default_general_chat_model:e.defaultChatKeyId===t.id}});ec(await Promise.all(t)),ex(e.defaultChatKeyId),em(!1);return}em(!0),ee(e=>e&&e.startsWith("Error loading model configuration:")?e:null),ea(null);try{let e=await fetch("/api/keys?custom_config_id=".concat(a));if(!e.ok){let t=await e.json();throw Error(t.error||"Failed to fetch API keys")}let t=await e.json(),r=await fetch("/api/custom-configs/".concat(a,"/default-chat-key"));r.ok;let s=200===r.status?await r.json():null;ex((null==s?void 0:s.id)||null);let n=t.map(async e=>{let t=await fetch("/api/keys/".concat(e.id,"/roles")),a=[];return t.ok&&(a=(await t.json()).map(e=>{let t=l(e.role_name);if(t)return t;let a=eA.find(t=>t.role_id===e.role_name);return a?{id:a.role_id,name:a.name,description:a.description||void 0}:null}).filter(Boolean)),{...e,assigned_roles:a,is_default_general_chat_model:(null==s?void 0:s.id)===e.id}}),i=await Promise.all(n);ec(i)}catch(e){ee(t=>t?"".concat(t,"; ").concat(e.message):e.message)}finally{em(!1)}},[a,eA]);(0,s.useEffect)(()=>{F&&eW()},[F,eW]),(0,s.useEffect)(()=>{F&&eA&&eZ()},[F,eA,eZ]);let eQ=(0,s.useMemo)(()=>{if(er){let e=i.MG.find(e=>e.id===z);if(!e)return[];if("openrouter"===e.id)return er.map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""));if("deepseek"===e.id){let e=[];return er.find(e=>"deepseek-chat"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-chat",label:"Deepseek V3",provider_id:"deepseek"}),er.find(e=>"deepseek-reasoner"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-reasoner",label:"DeepSeek R1-0528",provider_id:"deepseek"}),e.sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return er.filter(t=>t.provider_id===e.id).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return[]},[er,z]),e$=(0,s.useMemo)(()=>{if(er&&ey){let e=i.MG.find(e=>e.id===ey.provider);if(!e)return[];if("openrouter"===e.id)return er.map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""));if("deepseek"===e.id){let e=[];return er.find(e=>"deepseek-chat"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-chat",label:"Deepseek V3",provider_id:"deepseek"}),er.find(e=>"deepseek-reasoner"===e.id&&"deepseek"===e.provider_id)&&e.push({value:"deepseek-reasoner",label:"DeepSeek R1-0528",provider_id:"deepseek"}),e.sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return er.filter(t=>t.provider_id===e.id).map(e=>({value:e.id,label:e.display_name||e.name,provider_id:e.provider_id})).sort((e,t)=>(e.label||"").localeCompare(t.label||""))}return[]},[er,ey]);(0,s.useEffect)(()=>{eQ.length>0?q(eQ[0].value):q("")},[eQ,z]),(0,s.useEffect)(()=>{z&&eH()},[z,eH]);let eX=async e=>{if(e.preventDefault(),!a)return void ee("Configuration ID is missing.");if(ed.some(e=>e.predefined_model_id===V))return void ee("This model is already configured in this setup. Each model can only be used once per configuration, but you can use the same API key with different models.");$(!0),ee(null),ea(null);let t=[...ed];try{var r;let e=await fetch("/api/keys",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({custom_api_config_id:a,provider:z,predefined_model_id:V,api_key_raw:G,label:J,temperature:W})}),t=await e.json();if(!e.ok)throw Error(t.details||t.error||"Failed to save API key");let s={id:t.id,custom_api_config_id:a,provider:z,predefined_model_id:V,label:J,temperature:W,status:"active",created_at:new Date().toISOString(),last_used_at:null,is_default_general_chat_model:!1,assigned_roles:[]};ec(e=>[...e,s]),ea('API key "'.concat(J,'" saved successfully!')),B((null==(r=I[0])?void 0:r.value)||"openai"),U(""),H(""),Z(1),eQ.length>0&&q(eQ[0].value)}catch(e){ec(t),ee("Save Key Error: ".concat(e.message))}finally{$(!1)}},e0=e=>{ev(e),ew(e.temperature||1),ek(e.predefined_model_id)},e2=async()=>{if(!ey)return;if(ed.some(e=>e.id!==ey.id&&e.predefined_model_id===eN))return void ee("This model is already configured in this setup. Each model can only be used once per configuration.");eC(!0),ee(null),ea(null);let e=[...ed];ec(e=>e.map(e=>e.id===ey.id?{...e,temperature:ej,predefined_model_id:eN}:e));try{let t=await fetch("/api/keys?id=".concat(ey.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({temperature:ej,predefined_model_id:eN})}),a=await t.json();if(!t.ok)throw ec(e),Error(a.details||a.error||"Failed to update API key");ea('API key "'.concat(ey.label,'" updated successfully!')),ev(null)}catch(e){ee("Update Key Error: ".concat(e.message))}finally{eC(!1)}},e1=(e,t)=>{S.showConfirmation({title:"Delete API Key",message:'Are you sure you want to delete the API key "'.concat(t,'"? This will permanently remove the key and unassign all its roles. This action cannot be undone.'),confirmText:"Delete API Key",cancelText:"Cancel",type:"danger"},async()=>{eh(e),ee(null),ea(null);let a=[...ed],r=ed.find(t=>t.id===e);ec(t=>t.filter(t=>t.id!==e)),(null==r?void 0:r.is_default_general_chat_model)&&ex(null);try{let r=await fetch("/api/keys/".concat(e),{method:"DELETE"}),s=await r.json();if(!r.ok){if(ec(a),ex(ep),404===r.status){ec(t=>t.filter(t=>t.id!==e)),ea('API key "'.concat(t,'" was already deleted.'));return}throw Error(s.details||s.error||"Failed to delete API key")}ea('API key "'.concat(t,'" deleted successfully!'))}catch(e){throw ee("Delete Key Error: ".concat(e.message)),e}finally{eh(null)}})},e5=async e=>{if(!a)return;ee(null),ea(null);let t=[...ed];ec(t=>t.map(t=>({...t,is_default_general_chat_model:t.id===e}))),ex(e);try{let r=await fetch("/api/custom-configs/".concat(a,"/default-key-handler/").concat(e),{method:"PUT"}),s=await r.json();if(!r.ok)throw ec(t.map(e=>({...e}))),ex(ep),Error(s.details||s.error||"Failed to set default chat key");ea(s.message||"Default general chat key updated!")}catch(e){ee("Set Default Error: ".concat(e.message))}},e6=async(e,t,a)=>{ee(null),ea(null);let r="/api/keys/".concat(e.id,"/roles"),s=[...o.map(e=>({...e,isCustom:!1})),...eA.map(e=>({id:e.role_id,name:e.name,description:e.description||void 0,isCustom:!0,databaseId:e.id}))].find(e=>e.id===t)||{id:t,name:t,description:""},n=ed.map(e=>({...e,assigned_roles:[...e.assigned_roles.map(e=>({...e}))]})),i=null;ef&&ef.id===e.id&&(i={...ef,assigned_roles:[...ef.assigned_roles.map(e=>({...e}))]}),ec(r=>r.map(r=>{if(r.id===e.id){let e=a?r.assigned_roles.filter(e=>e.id!==t):[...r.assigned_roles,s];return{...r,assigned_roles:e}}return r})),ef&&ef.id===e.id&&eb(e=>{if(!e)return null;let r=a?e.assigned_roles.filter(e=>e.id!==t):[...e.assigned_roles,s];return{...e,assigned_roles:r}});try{let o;o=a?await fetch("".concat(r,"/").concat(t),{method:"DELETE"}):await fetch(r,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({role_name:t})});let l=await o.json();if(!o.ok){if(ec(n),i)eb(i);else if(ef&&ef.id===e.id){let t=n.find(t=>t.id===e.id);t&&eb(t)}let t=409===o.status&&l.error?l.error:l.details||l.error||(a?"Failed to unassign role":"Failed to assign role");throw Error(t)}ea(l.message||"Role '".concat(s.name,"' ").concat(a?"unassigned":"assigned"," successfully."))}catch(e){ee("Role Update Error: ".concat(e.message))}},e4=async()=>{if(!eF.trim()||eF.trim().length>30||!/^[a-zA-Z0-9_]+$/.test(eF.trim()))return void eq("Role ID is required (max 30 chars, letters, numbers, underscores only).");if(o.some(e=>e.id.toLowerCase()===eF.trim().toLowerCase())||eA.some(e=>e.role_id.toLowerCase()===eF.trim().toLowerCase()))return void eq("This Role ID is already in use (either predefined or as one of your custom roles).");if(!eR.trim())return void eq("Role Name is required.");if(!eL.trim())return void eq("Agent Backstory is required for multi-role orchestration.");if(eL.trim().length<50)return void eq("Agent Backstory must be at least 50 characters to provide meaningful context.");eq(null),eB(!0);try{let e=await fetch("/api/user/custom-roles",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({role_id:eF.trim(),name:eR.trim(),backstory:eL.trim()})});if(!e.ok){let t;try{t=await e.json()}catch(r){let a=await e.text().catch(()=>"HTTP status ".concat(e.status));t={error:"Server error, could not parse response.",details:a}}let a=t.error||"Failed to create custom role.";if(t.details)a+=" (Details: ".concat(t.details,")");else if(t.issues){let e=Object.entries(t.issues).map(e=>{let[t,a]=e;return"".concat(t,": ").concat(a.join(", "))}).join("; ");a+=" (Issues: ".concat(e,")")}throw Error(a)}let t=await e.json();eM(""),eO(""),eK(""),eW(),ea("Custom role '".concat(t.name,"' created successfully! It is now available globally."))}catch(e){eq(e.message)}finally{eB(!1)}},e3=(e,t)=>{e&&S.showConfirmation({title:"Delete Custom Role",message:'Are you sure you want to delete the custom role "'.concat(t,"\"? This will unassign it from all API keys where it's currently used. This action cannot be undone."),confirmText:"Delete Role",cancelText:"Cancel",type:"danger"},async()=>{eU(e),eP(null),eq(null),ea(null);try{let r=await fetch("/api/user/custom-roles/".concat(e),{method:"DELETE"}),s=await r.json();if(!r.ok)throw Error(s.error||"Failed to delete custom role");eI(t=>t.filter(t=>t.id!==e)),ea(s.message||'Global custom role "'.concat(t,'" deleted successfully.')),a&&eZ()}catch(e){throw eP("Error deleting role: ".concat(e.message)),e}finally{eU(null)}})};return L&&!E(a)?(0,r.jsx)(_.A,{}):R&&!F?(0,r.jsx)(_._,{}):(0,r.jsxs)("div",{className:"min-h-screen",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("button",{onClick:()=>D("/my-models"),className:"text-orange-600 hover:text-orange-700 inline-flex items-center mb-6 transition-colors duration-200 group",children:[(0,r.jsx)(d.A,{className:"h-5 w-5 mr-2 group-hover:-translate-x-1 transition-transform"}),"Back to My API Models"]}),(0,r.jsx)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 p-8 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6",children:[(0,r.jsx)("div",{className:"flex-1",children:F?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg",children:(0,r.jsx)(m.A,{className:"h-6 w-6 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:F.name}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Model Configuration"})]})]}),(0,r.jsxs)("div",{className:"flex items-center text-sm text-gray-600 bg-gray-50 px-4 py-2 rounded-xl w-fit",children:[(0,r.jsx)("span",{className:"inline-block w-2 h-2 bg-orange-500 rounded-full mr-2"}),"ID: ",F.id]})]}):X&&!R?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-red-100 rounded-2xl flex items-center justify-center mr-4",children:(0,r.jsx)(v.A,{className:"h-6 w-6 text-red-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-red-600",children:"Configuration Error"}),(0,r.jsx)("p",{className:"text-red-500 mt-1",children:X.replace("Error loading model configuration: ","")})]})]}):(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gray-100 rounded-2xl flex items-center justify-center mr-4",children:(0,r.jsx)(u.A,{className:"h-6 w-6 text-gray-400 animate-pulse"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Loading Configuration..."}),(0,r.jsx)("p",{className:"text-gray-500 mt-1",children:"Please wait while we fetch your model details"})]})]})}),F&&(0,r.jsxs)("button",{onClick:()=>D("/routing-setup/".concat(a,"?from=model-config")),className:"inline-flex items-center px-6 py-3 text-sm font-semibold rounded-2xl shadow-sm text-white bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 group",...Y(a),children:[(0,r.jsx)(m.A,{className:"h-5 w-5 mr-2 group-hover:rotate-90 transition-transform duration-200"}),"Advanced Routing Setup"]})]})}),et&&(0,r.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-2xl p-4 mb-6 animate-slide-in",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(c.A,{className:"h-5 w-5 text-green-600"}),(0,r.jsx)("p",{className:"text-green-800 font-medium",children:et})]})}),X&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-2xl p-4 mb-6 animate-slide-in",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(v.A,{className:"h-5 w-5 text-red-600"}),(0,r.jsx)("p",{className:"text-red-800 font-medium",children:X})]})})]}),F&&(0,r.jsxs)("div",{className:"grid grid-cols-1 xl:grid-cols-5 gap-8",children:[(0,r.jsx)("div",{className:"xl:col-span-2",children:(0,r.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 p-6 sticky top-8",children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mr-3 shadow-md",children:(0,r.jsx)(f.A,{className:"h-5 w-5 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"Add API Key"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Configure new key"})]})]}),(0,r.jsxs)("form",{onSubmit:eX,className:"space-y-5",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"provider",className:"block text-sm font-medium text-gray-700 mb-2",children:"Provider"}),(0,r.jsx)("select",{id:"provider",value:z,onChange:e=>{B(e.target.value)},className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm",children:I.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"apiKeyRaw",className:"block text-sm font-medium text-gray-700 mb-2",children:"API Key"}),(0,r.jsx)("input",{id:"apiKeyRaw",type:"password",value:G,onChange:e=>U(e.target.value),className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm",placeholder:"Enter your API key"}),en&&null===er&&(0,r.jsxs)("p",{className:"mt-2 text-xs text-orange-600 flex items-center bg-orange-50 p-2 rounded-lg",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-1 animate-pulse"}),"Fetching models..."]}),eo&&(0,r.jsx)("p",{className:"mt-2 text-xs text-red-600 bg-red-50 p-2 rounded-lg",children:eo})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"predefinedModelId",className:"block text-sm font-medium text-gray-700 mb-2",children:"Model Variant"}),(0,r.jsx)("select",{id:"predefinedModelId",value:V,onChange:e=>q(e.target.value),disabled:!eQ.length,className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm disabled:bg-gray-50 disabled:text-gray-500",children:eQ.length>0?eQ.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value)):(0,r.jsx)("option",{value:"",disabled:!0,children:null===er&&en?"Loading models...":"Select a provider first"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"label",className:"block text-sm font-medium text-gray-700 mb-2",children:"Label"}),(0,r.jsx)("input",{type:"text",id:"label",value:J,onChange:e=>H(e.target.value),required:!0,className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm",placeholder:"e.g., My OpenAI GPT-4o Key #1"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"temperature",className:"block text-sm font-medium text-gray-700 mb-2",children:["Temperature",(0,r.jsx)("span",{className:"text-xs text-gray-500 ml-1",children:"(0.0 - 2.0)"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("input",{type:"range",id:"temperature",min:"0",max:"2",step:"0.1",value:W,onChange:e=>Z(parseFloat(e.target.value)),className:"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider-orange"}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-xs text-gray-500",children:"Conservative"}),(0,r.jsx)("div",{className:"flex items-center space-x-2",children:(0,r.jsx)("input",{type:"number",min:"0",max:"2",step:"0.1",value:W,onChange:e=>Z(Math.min(2,Math.max(0,parseFloat(e.target.value)||0))),className:"w-16 px-2 py-1 text-xs border border-gray-200 rounded-lg focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-center"})}),(0,r.jsx)("span",{className:"text-xs text-gray-500",children:"Creative"})]}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Controls randomness: 0.0 = deterministic, 1.0 = balanced, 2.0 = very creative"})]})]})]}),(0,r.jsx)("button",{type:"submit",disabled:Q||!V||""===V||!G.trim()||!J.trim(),className:"w-full bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium py-3 px-4 rounded-xl transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none text-sm",children:Q?(0,r.jsxs)("span",{className:"flex items-center justify-center",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-2 animate-pulse"}),"Saving..."]}):(0,r.jsxs)("span",{className:"flex items-center justify-center",children:[(0,r.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Add API Key"]})})]}),(0,r.jsx)("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-xl",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)(g.A,{className:"h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-blue-900 mb-1",children:"Key Configuration Rules"}),(0,r.jsxs)("div",{className:"text-xs text-blue-800 space-y-1",children:[(0,r.jsxs)("p",{children:["✅ ",(0,r.jsx)("strong",{children:"Same API key, different models:"})," Allowed"]}),(0,r.jsxs)("p",{children:["✅ ",(0,r.jsx)("strong",{children:"Different API keys, same model:"})," Allowed"]}),(0,r.jsxs)("p",{children:["❌ ",(0,r.jsx)("strong",{children:"Same model twice:"})," Not allowed in one configuration"]})]})]})]})})]})}),(0,r.jsx)("div",{className:"xl:col-span-3",children:(0,r.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-3 shadow-md",children:(0,r.jsx)(h.A,{className:"h-5 w-5 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-gray-900",children:"API Keys & Roles"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Manage existing keys"})]})]}),eu&&(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(u.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-3 animate-pulse"}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:"Loading API keys..."})]}),!eu&&0===ed.length&&(!X||X&&X.startsWith("Error loading model configuration:"))&&(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3",children:(0,r.jsx)(h.A,{className:"h-6 w-6 text-gray-400"})}),(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 mb-1",children:"No API Keys"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Add your first key using the form"})]}),!eu&&ed.length>0&&(0,r.jsx)("div",{className:"space-y-3 max-h-96 overflow-y-auto",children:ed.map((e,t)=>(0,r.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-xl p-4 hover:shadow-md transition-all duration-200 animate-slide-in",style:{animationDelay:"".concat(50*t,"ms")},children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center mb-2",children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 truncate mr-2",children:e.label}),e.is_default_general_chat_model&&(0,r.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 flex-shrink-0",children:[(0,r.jsx)(b.A,{className:"h-3 w-3 mr-1"}),"Default"]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("p",{className:"text-xs text-gray-900 bg-white px-2 py-1 rounded-lg border",children:[e.provider," (",e.predefined_model_id,")"]}),(0,r.jsxs)("p",{className:"text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded-lg border border-orange-200",children:["Temp: ",e.temperature]})]}),(0,r.jsx)("div",{className:"flex flex-wrap gap-1",children:e.assigned_roles.length>0?e.assigned_roles.map(e=>(0,r.jsx)("span",{className:"inline-block whitespace-nowrap rounded-full bg-orange-100 px-2 py-1 text-xs font-medium text-orange-800",children:e.name},e.id)):(0,r.jsx)("span",{className:"text-xs text-gray-500 bg-white px-2 py-1 rounded-lg border",children:"No roles"})})]}),!e.is_default_general_chat_model&&(0,r.jsxs)("button",{onClick:()=>e5(e.id),className:"text-xs bg-white border border-gray-200 hover:border-gray-300 text-gray-700 hover:text-gray-900 py-1 px-2 rounded-lg mt-2 transition-colors","data-tooltip-id":"tooltip-set-default-".concat(e.id),"data-tooltip-content":"Set as default chat model",children:["Set Default",(0,r.jsx)(j.m_,{id:"tooltip-set-default-".concat(e.id),place:"top"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1 ml-2 flex-shrink-0",children:[(0,r.jsxs)("button",{onClick:()=>e0(e),disabled:eg===e.id,className:"p-2 text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":"tooltip-edit-".concat(e.id),"data-tooltip-content":"Edit Model & Settings",children:[(0,r.jsx)(p.A,{className:"h-4 w-4"}),(0,r.jsx)(j.m_,{id:"tooltip-edit-".concat(e.id),place:"top"})]}),(0,r.jsxs)("button",{onClick:()=>eb(e),disabled:eg===e.id,className:"p-2 text-orange-600 hover:text-orange-700 hover:bg-orange-50 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":"tooltip-roles-".concat(e.id),"data-tooltip-content":"Manage Roles",children:[(0,r.jsx)(m.A,{className:"h-4 w-4"}),(0,r.jsx)(j.m_,{id:"tooltip-roles-".concat(e.id),place:"top"})]}),(0,r.jsxs)("button",{onClick:()=>e1(e.id,e.label),disabled:eg===e.id,className:"p-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50","data-tooltip-id":"tooltip-delete-".concat(e.id),"data-tooltip-content":"Delete Key",children:[eg===e.id?(0,r.jsx)(y.A,{className:"h-4 w-4 animate-pulse"}):(0,r.jsx)(y.A,{className:"h-4 w-4"}),(0,r.jsx)(j.m_,{id:"tooltip-delete-".concat(e.id),place:"top"})]})]})]})},e.id))}),!eu&&X&&!X.startsWith("Error loading model configuration:")&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-xl p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(v.A,{className:"h-5 w-5 text-red-600"}),(0,r.jsxs)("p",{className:"text-red-800 font-medium text-sm",children:["Could not load API keys/roles: ",X]})]})})]})})]}),ef&&(()=>{if(!ef)return null;let e=[...o.map(e=>({...e,isCustom:!1})),...eA.map(e=>({id:e.role_id,name:e.name,description:e.description||void 0,isCustom:!0,databaseId:e.id}))].sort((e,t)=>e.name.localeCompare(t.name));return(0,r.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,r.jsxs)("div",{className:"card w-full max-w-lg max-h-[90vh] flex flex-col",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-200",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold text-gray-900",children:["Manage Roles for: ",(0,r.jsx)("span",{className:"text-orange-600",children:ef.label})]}),(0,r.jsx)("button",{onClick:()=>{eb(null),eY(!1),eq(null)},className:"text-gray-500 hover:text-gray-700 hover:bg-gray-100 p-1 rounded-lg transition-colors duration-200",children:(0,r.jsx)(v.A,{className:"h-6 w-6"})})]}),(0,r.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[eD&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 mb-4",children:(0,r.jsxs)("p",{className:"text-red-800 text-sm",children:["Error with custom roles: ",eD]})}),(0,r.jsx)("div",{className:"flex justify-end mb-4",children:(0,r.jsxs)("button",{onClick:()=>eY(!eE),className:"btn-primary text-sm inline-flex items-center",children:[(0,r.jsx)(x.A,{className:"h-4 w-4 mr-2"}),eE?"Cancel New Role":"Create New Custom Role"]})}),eE&&(0,r.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-4",children:[(0,r.jsx)("h3",{className:"text-md font-medium text-gray-900 mb-3",children:"Create New Custom Role for this Configuration"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"newCustomRoleId",className:"block text-sm font-medium text-gray-700 mb-1",children:"Role ID (short, no spaces, max 30 chars)"}),(0,r.jsx)("input",{type:"text",id:"newCustomRoleId",value:eF,onChange:e=>eM(e.target.value.replace(/\s/g,"")),className:"form-input",maxLength:30,placeholder:"e.g., my_blog_writer"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"newCustomRoleName",className:"block text-sm font-medium text-gray-700 mb-1",children:"Display Name (max 100 chars)"}),(0,r.jsx)("input",{type:"text",id:"newCustomRoleName",value:eR,onChange:e=>eO(e.target.value),className:"form-input",maxLength:100,placeholder:"e.g., My Awesome Blog Writer"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"newCustomRoleBackstory",className:"block text-sm font-medium text-gray-700 mb-1",children:["Agent Backstory ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})," (required for multi-role orchestration)"]}),(0,r.jsx)("textarea",{id:"newCustomRoleBackstory",value:eL,onChange:e=>eK(e.target.value),rows:4,className:"form-input",maxLength:1e3,required:!0,placeholder:"Example: You are a professional social media strategist with 8+ years of experience helping brands grow their online presence. You have worked with Fortune 500 companies and startups, creating viral campaigns and building engaged communities. You understand platform algorithms, content psychology, and audience engagement strategies..."}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Write a professional backstory that defines your agent's personality, expertise, and experience. This helps the AI understand how to behave in multi-role conversations."})]}),eV&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3",children:(0,r.jsx)("p",{className:"text-red-800 text-sm",children:eV})}),(0,r.jsx)("button",{onClick:e4,disabled:ez,className:"btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed",children:ez?"Saving Role...":"Save Custom Role"})]})]})]}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-700 mb-3",children:"Select roles to assign:"}),(0,r.jsxs)("div",{className:"overflow-y-auto space-y-2",style:{maxHeight:"calc(90vh - 350px)"},children:[eS&&(0,r.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-orange-600"}),(0,r.jsx)("p",{className:"text-gray-600 text-sm ml-2",children:"Loading custom roles..."})]}),e.map(e=>{let t=ef.assigned_roles.some(t=>t.id===e.id);return(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg border transition-all duration-200 ".concat(t?"bg-orange-50 border-orange-200 shadow-sm":"bg-white border-gray-200 hover:border-gray-300 hover:shadow-sm"),children:[(0,r.jsxs)("label",{htmlFor:"role-".concat(e.id),className:"flex items-center cursor-pointer flex-grow",children:[(0,r.jsx)("input",{type:"checkbox",id:"role-".concat(e.id),checked:t,onChange:()=>e6(ef,e.id,t),className:"h-4 w-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500 focus:ring-2 cursor-pointer"}),(0,r.jsx)("span",{className:"ml-3 text-sm font-medium ".concat(t?"text-orange-800":"text-gray-900"),children:e.name}),e.isCustom&&(0,r.jsx)("span",{className:"ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800",children:"Custom"})]}),e.isCustom&&e.databaseId&&(0,r.jsx)("button",{onClick:()=>e3(e.databaseId,e.name),disabled:eG===e.databaseId,className:"p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-wait ml-2",title:"Delete this custom role",children:eG===e.databaseId?(0,r.jsx)(m.A,{className:"h-4 w-4 animate-spin"}):(0,r.jsx)(y.A,{className:"h-4 w-4"})})]},e.id)})]})]}),(0,r.jsx)("div",{className:"p-6 border-t border-gray-200 bg-gray-50 rounded-b-xl",children:(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsx)("button",{onClick:()=>{eb(null),eY(!1),eq(null)},className:"btn-secondary",children:"Done"})})})]})})})(),ey&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,r.jsxs)("div",{className:"card w-full max-w-lg",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-200",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Edit API Key"}),(0,r.jsx)("button",{onClick:()=>ev(null),className:"text-gray-500 hover:text-gray-700 hover:bg-gray-100 p-1 rounded-lg transition-colors duration-200",children:(0,r.jsx)(v.A,{className:"h-6 w-6"})})]}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:ey.label}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Current: ",ey.provider," (",ey.predefined_model_id,")"]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Provider"}),(0,r.jsx)("div",{className:"w-full p-2.5 bg-gray-50 border border-gray-300 rounded-md text-gray-700",children:(null==(t=i.MG.find(e=>e.id===ey.provider))?void 0:t.name)||ey.provider}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Provider cannot be changed"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"editModelId",className:"block text-sm font-medium text-gray-700 mb-2",children:"Model"}),(0,r.jsx)("select",{id:"editModelId",value:eN,onChange:e=>ek(e.target.value),disabled:!e$.length,className:"w-full p-2.5 bg-white border border-gray-300 rounded-md text-gray-900 focus:ring-orange-500 focus:border-orange-500 disabled:opacity-50 disabled:bg-gray-100",children:e$.length>0?e$.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value)):(0,r.jsx)("option",{value:"",disabled:!0,children:en?"Loading models...":"No models available"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"editTemperature",className:"block text-sm font-medium text-gray-700 mb-2",children:["Temperature: ",ej]}),(0,r.jsx)("input",{type:"range",id:"editTemperature",min:"0",max:"2",step:"0.1",value:ej,onChange:e=>ew(parseFloat(e.target.value)),className:"slider-orange w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"}),(0,r.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-1",children:[(0,r.jsx)("span",{children:"0.0 (Focused)"}),(0,r.jsx)("span",{children:"1.0 (Balanced)"}),(0,r.jsx)("span",{children:"2.0 (Creative)"})]})]}),(0,r.jsx)("div",{className:"bg-gray-50 rounded-lg p-3",children:(0,r.jsx)("p",{className:"text-xs text-gray-600",children:"You can change the model and temperature settings. Temperature controls randomness in responses. Lower values (0.0-0.3) are more focused and deterministic, while higher values (1.5-2.0) are more creative and varied."})})]})]}),(0,r.jsx)("div",{className:"p-6 border-t border-gray-200 bg-gray-50 rounded-b-xl",children:(0,r.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,r.jsx)("button",{onClick:()=>ev(null),className:"btn-secondary",disabled:e_,children:"Cancel"}),(0,r.jsx)("button",{onClick:e2,disabled:e_,className:"btn-primary",children:e_?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Saving..."]}):"Save Changes"})]})})]})}),!F&&!R&&!X&&(0,r.jsxs)("div",{className:"bg-white rounded-2xl shadow-sm border border-gray-100 text-center py-16 px-8",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6",children:(0,r.jsx)(g.A,{className:"h-8 w-8 text-gray-400"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:"Model Not Found"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-8",children:"This API Model configuration could not be found or may have been deleted."}),(0,r.jsxs)("button",{onClick:()=>D("/my-models"),className:"inline-flex items-center px-6 py-3 text-sm font-medium rounded-xl text-white bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5",children:[(0,r.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Return to My API Models"]})]}),(0,r.jsx)(w.A,{isOpen:S.isOpen,onClose:S.hideConfirmation,onConfirm:S.onConfirm,title:S.title,message:S.message,confirmText:S.confirmText,cancelText:S.cancelText,type:S.type,isLoading:S.isLoading}),(0,r.jsx)(j.m_,{id:"global-tooltip"})]})}},99323:(e,t,a)=>{"use strict";a.d(t,{bu:()=>l,i9:()=>o});var r=a(95155),s=a(12115),n=a(35695);let i=(0,s.createContext)(void 0);function o(e){let{children:t}=e,[a,o]=(0,s.useState)(!1),[l,d]=(0,s.useState)(null),[c,u]=(0,s.useState)([]),[m,g]=(0,s.useState)(new Set),[h,p]=(0,s.useState)(!1),x=(0,n.usePathname)(),f=(0,n.useRouter)(),b=(0,s.useRef)(null),y=(0,s.useRef)([]),v=(0,s.useRef)(null),j=(0,s.useRef)(0),w=(0,s.useRef)({}),N=(0,s.useRef)({});(0,s.useEffect)(()=>{p(!0)},[]);let k=(0,s.useCallback)(e=>{},[h]);(0,s.useEffect)(()=>{x&&!c.includes(x)&&(u(e=>[...e,x]),g(e=>new Set([...e,x])))},[x,c]),(0,s.useEffect)(()=>{k("\uD83D\uDD0D [OPTIMISTIC NAV] Route check: target=".concat(l,", current=").concat(x,", navigationId=").concat(v.current)),l&&v.current&&x===l&&(k("✅ [OPTIMISTIC NAV] Navigation completed: ".concat(l," -> ").concat(x)),b.current&&(clearTimeout(b.current),b.current=null),o(!1),d(null),v.current=null,y.current=y.current.filter(e=>e.route!==l))},[x,l,k]),(0,s.useEffect)(()=>{a&&l&&x===l&&(k("\uD83D\uDE80 [OPTIMISTIC NAV] Immediate route match detected, clearing navigation state"),o(!1),d(null),b.current&&(clearTimeout(b.current),b.current=null))},[x,l,a,k]);let _=(0,s.useCallback)(e=>m.has(e),[m]),C=(0,s.useCallback)(()=>{if(0===y.current.length)return;let e=y.current[y.current.length-1];y.current=[e];let{route:t,id:a}=e;k("\uD83D\uDE80 [OPTIMISTIC NAV] Processing navigation to: ".concat(t," (id: ").concat(a,")")),b.current&&(clearTimeout(b.current),b.current=null),v.current=a;let r=_(t);r&&(k("⚡ [OPTIMISTIC NAV] Using cached navigation for: ".concat(t)),setTimeout(()=>{v.current===a&&o(!1)},100));try{f.push(t)}catch(e){k("❌ [OPTIMISTIC NAV] Router.push failed for: ".concat(t,", using fallback")),window.location.href=t;return}b.current=setTimeout(()=>{if(k("⚠️ [OPTIMISTIC NAV] Timeout reached for: ".concat(t," (id: ").concat(a,"), current path: ").concat(x)),v.current===a){k("\uD83D\uDD04 [OPTIMISTIC NAV] Attempting fallback navigation to: ".concat(t));try{window.location.href=t}catch(e){k("❌ [OPTIMISTIC NAV] Fallback navigation failed: ".concat(e))}o(!1),d(null),v.current=null}b.current=null},r?800:3e3)},[f,x,_,k]),A=(0,s.useCallback)(e=>{if(x===e||!h)return;let t=Date.now();if(t-j.current<100&&l===e)return void k("\uD83D\uDD04 [OPTIMISTIC NAV] Debouncing duplicate navigation to: ".concat(e));if(j.current=t,w.current[e]||(w.current[e]=0),w.current[e]++,N.current[e]&&clearTimeout(N.current[e]),N.current[e]=setTimeout(()=>{w.current[e]=0},2e3),w.current[e]>=3){k("\uD83D\uDEA8 [OPTIMISTIC NAV] Force navigation escape hatch for: ".concat(e)),w.current[e]=0,window.location.href=e;return}b.current&&(clearTimeout(b.current),b.current=null),o(!0),d(e);let a="nav_".concat(t,"_").concat(Math.random().toString(36).substr(2,9));y.current=[{route:e,timestamp:t,id:a}],C()},[x,l,C,k,h]),I=(0,s.useCallback)(()=>{b.current&&(clearTimeout(b.current),b.current=null),o(!1),d(null),v.current=null,y.current=[]},[]);return(0,s.useEffect)(()=>{if(!h)return;let e=()=>{!document.hidden&&a&&(k("\uD83D\uDC41️ [OPTIMISTIC NAV] Document visible, checking if navigation should clear"),setTimeout(()=>{l&&x===l&&(k("\uD83D\uDD27 [OPTIMISTIC NAV] Force clearing navigation state"),o(!1),d(null),b.current&&(clearTimeout(b.current),b.current=null))},100))};return document.addEventListener("visibilitychange",e),()=>document.removeEventListener("visibilitychange",e)},[a,l,x,k,h]),(0,s.useEffect)(()=>()=>{b.current&&clearTimeout(b.current)},[]),(0,r.jsx)(i.Provider,{value:{isNavigating:a,targetRoute:l,navigateOptimistically:A,clearNavigation:I,isPageCached:_,navigationHistory:c},children:t})}function l(){return(0,s.useContext)(i)||null}}},e=>{var t=t=>e(e.s=t);e.O(0,[7874,5738,274,6308,563,2662,8669,8848,622,9173,9628,4288,7706,7544,1142,2993,1561,9248,5495,7358],()=>t(44469)),_N_E=e.O()}]);